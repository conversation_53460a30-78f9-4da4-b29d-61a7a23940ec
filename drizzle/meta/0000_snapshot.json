{"version": "6", "dialect": "sqlite", "id": "5734da11-ed5b-498b-ad98-1bfc147d5235", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"statistics": {"name": "statistics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "totalRequestTimes": {"name": "totalRequestTimes", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "totalTokenUsage": {"name": "totalTokenUsage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "dingTalkUnionId": {"name": "dingTalkUnionId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "isAdmin": {"name": "isAdmin", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "token": {"name": "token", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 20000}, "requestTimes": {"name": "requestTimes", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "dingTalkUserId": {"name": "dingTalkUserId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mobile": {"name": "mobile", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {"users_dingTalkUnionId_unique": {"name": "users_dingTalkUnionId_unique", "columns": ["dingTalkUnionId"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}