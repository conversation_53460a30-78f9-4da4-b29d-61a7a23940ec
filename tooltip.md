You are given a task to integrate an existing React component in the codebase

The codebase should support:

- shadcn project structure
- Tailwind CSS
- Typescript

If it doesn't, provide instructions on how to setup project via shadcn CLI, install Tailwind or Typescript.

Determine the default path for components and styles.
If default path for components is not /components/ui, provide instructions on why it's important to create this folder
Copy-paste this component to /components/ui folder:

```tsx
context-card-1.tsx
import React, { useMemo } from "react";
import { Tooltip } from "react-tooltip";

interface ContextCardTriggerProps {
  content: React.ReactNode;
  side?: "top" | "bottom" | "left" | "right";
  children: React.ReactNode;
}

const ContextCardTrigger = ({
  content,
  side = "top",
  children
}: ContextCardTriggerProps) => {
  const id = useMemo(() => {
    const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    return Array.from({ length: 6 }, () => chars[Math.floor(Math.random() * chars.length)]).join("");
  }, []);

  return (
    <div>
      <style>
        {`
          :root {
            --context-card-border: #ebebeb
          }
          .dark {
            --context-card-border: #2e2e2e
          }
        `}
      </style>
      <div id={id} className="font-sans">{children}</div>
      <Tooltip
        anchorSelect={`#${id}`}
        place={side}
        opacity={1}
        border={"1px solid var(--context-card-border)"}
        className={`!font-sans !text-center !text-base !rounded-lg !bg-background-100 !text-gray-1000`}
      >
        {content}
      </Tooltip>
    </div>
  );
};

export const ContextCard = {
  Trigger: ContextCardTrigger
};


demo.tsx
import { ContextCard } from "@/components/ui/context-card-1";

export default function DefaultDemo() {
  return (
    <div className="flex justify-between gap-4">
          <ContextCard.Trigger
            content="The Evil Rabbit Jumped over the Fence"
            side="top"
          >
            <span className="dark:text-white">Top</span>
          </ContextCard.Trigger>
          <ContextCard.Trigger
            content="The Evil Rabbit Jumped over the Fence"
            side="bottom"
          >
            <span className="dark:text-white">Bottom</span>
          </ContextCard.Trigger>
          <ContextCard.Trigger
            content="The Evil Rabbit Jumped over the Fence"
            side="left"
          >
            <span className="dark:text-white">Left</span>
          </ContextCard.Trigger>
          <ContextCard.Trigger
            content="The Evil Rabbit Jumped over the Fence"
            side="right"
          >
            <span className="dark:text-white">Right</span>
          </ContextCard.Trigger>
        </div>
  );
}

```

Install NPM dependencies:

```bash
react-tooltip
```

Extend existing Tailwind 4 index.css with this code (or if project uses Tailwind 3, extend tailwind.config.js or globals.css):

```css
@import "tailwindcss";
@import "tw-animate-css";

@theme inline {
	--color-context-card-border: var(--context-card-border);
	--color-gray-1000: var(--ds-gray-1000);
	--color-background-100: var(--ds-background-100);
}

:root {
	--context-card-border: hsla(0, 0%, 92%, 1);
	--ds-gray-1000: hsla(0, 0%, 9%, 1);
	--ds-background-100: hsla(0, 0%, 100%, 1);
}

.dark {
	--context-card-border: hsla(0, 0%, 18%, 1);
	--ds-gray-1000: hsla(0, 0%, 93%, 1);
	--ds-background-100: hsla(0, 0%, 4%, 1);
}
```

Implementation Guidelines

1.  Analyze the component structure and identify all required dependencies
2.  Review the component's argumens and state
3.  Identify any required context providers or hooks and install them
4.  Questions to Ask

- What data/props will be passed to this component?
- Are there any specific state management requirements?
- Are there any required assets (images, icons, etc.)?
- What is the expected responsive behavior?
- What is the best place to use this component in the app?

Steps to integrate 0. Copy paste all the code above in the correct directories

1.  Install external dependencies
2.  Fill image assets with Unsplash stock images you know exist
3.  Use lucide-react icons for svgs or logos if component requires them
