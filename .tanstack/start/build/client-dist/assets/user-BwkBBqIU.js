import{r as n,j as l,d as M}from"./main-DKMnXhyP.js";import{P as S}from"./separator-DgjmiNos.js";import{c as h}from"./index-DFx3G0N8.js";import{c as y}from"./createLucideIcon-MXzgtaay.js";function Z(e,t){const a=n.createContext(t),c=r=>{const{children:o,...s}=r,i=n.useMemo(()=>s,Object.values(s));return l.jsx(a.Provider,{value:i,children:o})};c.displayName=e+"Provider";function u(r){const o=n.useContext(a);if(o)return o;if(t!==void 0)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}return[c,u]}function $(e,t=[]){let a=[];function c(r,o){const s=n.createContext(o),i=a.length;a=[...a,o];const d=f=>{const{scope:p,children:x,...m}=f,P=p?.[e]?.[i]||s,k=n.useMemo(()=>m,Object.values(m));return l.jsx(P.Provider,{value:k,children:x})};d.displayName=r+"Provider";function v(f,p){const x=p?.[e]?.[i]||s,m=n.useContext(x);if(m)return m;if(o!==void 0)return o;throw new Error(`\`${f}\` must be used within \`${r}\``)}return[d,v]}const u=()=>{const r=a.map(o=>n.createContext(o));return function(s){const i=s?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...s,[e]:i}}),[s,i])}};return u.scopeName=e,[c,F(u,...t)]}function F(...e){const t=e[0];if(e.length===1)return t;const a=()=>{const c=e.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(r){const o=c.reduce((s,{useScope:i,scopeName:d})=>{const f=i(r)[`__scope${d}`];return{...s,...f}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return a.scopeName=t.scopeName,a}function H(e){const t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...a)=>t.current?.(...a),[])}var g=globalThis?.document?n.useLayoutEffect:()=>{},T=M();function q(){return T.useSyncExternalStore(B,()=>!0,()=>!1)}function B(){return()=>{}}var A="Avatar",[U,ee]=$(A),[z,L]=U(A),C=n.forwardRef((e,t)=>{const{__scopeAvatar:a,...c}=e,[u,r]=n.useState("idle");return l.jsx(z,{scope:a,imageLoadingStatus:u,onImageLoadingStatusChange:r,children:l.jsx(S.span,{...c,ref:t})})});C.displayName=A;var E="AvatarImage",_=n.forwardRef((e,t)=>{const{__scopeAvatar:a,src:c,onLoadingStatusChange:u=()=>{},...r}=e,o=L(E,a),s=G(c,r),i=H(d=>{u(d),o.onImageLoadingStatusChange(d)});return g(()=>{s!=="idle"&&i(s)},[s,i]),s==="loaded"?l.jsx(S.img,{...r,ref:t,src:c}):null});_.displayName=E;var N="AvatarFallback",b=n.forwardRef((e,t)=>{const{__scopeAvatar:a,delayMs:c,...u}=e,r=L(N,a),[o,s]=n.useState(c===void 0);return n.useEffect(()=>{if(c!==void 0){const i=window.setTimeout(()=>s(!0),c);return()=>window.clearTimeout(i)}},[c]),o&&r.imageLoadingStatus!=="loaded"?l.jsx(S.span,{...u,ref:t}):null});b.displayName=N;function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function G(e,{referrerPolicy:t,crossOrigin:a}){const c=q(),u=n.useRef(null),r=c?(u.current||(u.current=new window.Image),u.current):null,[o,s]=n.useState(()=>w(r,e));return g(()=>{s(w(r,e))},[r,e]),g(()=>{const i=f=>()=>{s(f)};if(!r)return;const d=i("loaded"),v=i("error");return r.addEventListener("load",d),r.addEventListener("error",v),t&&(r.referrerPolicy=t),typeof a=="string"&&(r.crossOrigin=a),()=>{r.removeEventListener("load",d),r.removeEventListener("error",v)}},[r,a,t]),o}var R=C,j=_,I=b;const K=n.forwardRef(({className:e,...t},a)=>l.jsx(R,{ref:a,className:h("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));K.displayName=R.displayName;const O=n.forwardRef(({className:e,...t},a)=>l.jsx(j,{ref:a,className:h("aspect-square h-full w-full",e),...t}));O.displayName=j.displayName;const V=n.forwardRef(({className:e,...t},a)=>l.jsx(I,{ref:a,className:h("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));V.displayName=I.displayName;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],te=y("loader-circle",W);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],re=y("user",D);export{K as A,te as L,re as U,O as a,V as b,H as c,$ as d,Z as e,g as u};
