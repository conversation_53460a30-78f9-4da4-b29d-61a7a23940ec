import{R as _}from"./main-CJJWzuV9.js";const H=e=>{let o;const t=new Set,s=(i,S)=>{const g=typeof i=="function"?i(o):i;if(!Object.is(g,o)){const v=o;o=S??(typeof g!="object"||g===null)?g:Object.assign({},o,g),t.forEach(m=>m(o,v))}},a=()=>o,l={setState:s,getState:a,getInitialState:()=>d,subscribe:i=>(t.add(i),()=>t.delete(i))},d=o=e(s,a,l);return l},U=e=>e?H(e):H,j=e=>e;function E(e,o=j){const t=_.useSyncExternalStore(e.subscribe,()=>o(e.getState()),()=>o(e.getInitialState()));return _.useDebugValue(t),t}const O=e=>{const o=U(e),t=s=>E(o,s);return Object.assign(t,o),t},w=e=>e?O(e):O;function x(e,o){let t;try{t=e()}catch{return}return{getItem:a=>{var n;const f=d=>d===null?null:JSON.parse(d,void 0),l=(n=t.getItem(a))!=null?n:null;return l instanceof Promise?l.then(f):f(l)},setItem:(a,n)=>t.setItem(a,JSON.stringify(n,void 0)),removeItem:a=>t.removeItem(a)}}const b=e=>o=>{try{const t=e(o);return t instanceof Promise?t:{then(s){return b(s)(t)},catch(s){return this}}}catch(t){return{then(s){return this},catch(s){return b(s)(t)}}}},F=(e,o)=>(t,s,a)=>{let n={storage:x(()=>localStorage),partialize:r=>r,version:0,merge:(r,h)=>({...h,...r}),...o},f=!1;const l=new Set,d=new Set;let i=n.storage;if(!i)return e((...r)=>{console.warn(`[zustand persist middleware] Unable to update item '${n.name}', the given storage is currently unavailable.`),t(...r)},s,a);const S=()=>{const r=n.partialize({...s()});return i.setItem(n.name,{state:r,version:n.version})},g=a.setState;a.setState=(r,h)=>{g(r,h),S()};const v=e((...r)=>{t(...r),S()},s,a);a.getInitialState=()=>v;let m;const I=()=>{var r,h;if(!i)return;f=!1,l.forEach(u=>{var c;return u((c=s())!=null?c:v)});const p=((h=n.onRehydrateStorage)==null?void 0:h.call(n,(r=s())!=null?r:v))||void 0;return b(i.getItem.bind(i))(n.name).then(u=>{if(u)if(typeof u.version=="number"&&u.version!==n.version){if(n.migrate){const c=n.migrate(u.state,u.version);return c instanceof Promise?c.then(y=>[!0,y]):[!0,c]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,u.state];return[!1,void 0]}).then(u=>{var c;const[y,R]=u;if(m=n.merge(R,(c=s())!=null?c:v),t(m,!0),y)return S()}).then(()=>{p?.(m,void 0),m=s(),f=!0,d.forEach(u=>u(m))}).catch(u=>{p?.(void 0,u)})};return a.persist={setOptions:r=>{n={...n,...r},r.storage&&(i=r.storage)},clearStorage:()=>{i?.removeItem(n.name)},getOptions:()=>n,rehydrate:()=>I(),hasHydrated:()=>f,onHydrate:r=>(l.add(r),()=>{l.delete(r)}),onFinishHydration:r=>(d.add(r),()=>{d.delete(r)})},n.skipHydration||I(),m||v},J=F,N=w()(J((e,o)=>({user:null,loading:!1,error:null,setUser:t=>{e({user:t,error:null})},setLoading:t=>{e({loading:t})},setError:t=>{e({error:t,loading:!1})},clearUser:()=>{e({user:null,error:null,loading:!1})},updateUser:t=>{const s=o().user;s&&e({user:{...s,...t}})}}),{name:"user-store",partialize:e=>({user:e.user})}));export{w as c,J as p,N as u};
