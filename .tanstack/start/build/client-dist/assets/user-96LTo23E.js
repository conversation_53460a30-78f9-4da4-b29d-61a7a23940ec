import{r,j as e}from"./main-DKMnXhyP.js";import{C as m,d as x,a as k,b as C,c as U}from"./card-BLJdaYT0.js";import{B as h}from"./button-C0rBNTfn.js";import{B as v}from"./badge-BR4SVeWL.js";import{a as S,u as D,d as L}from"./user-B0B3o9Nr.js";import"./index-DFx3G0N8.js";function T({user:i,onSave:l,onCancel:u}){const[n,a]=r.useState({name:i.name||"",mobile:i.mobile||"",token:i.token,isAdmin:i.isAdmin}),c=t=>{t.preventDefault(),l(n)};return e.jsxs(m,{className:"mt-4",children:[e.jsxs(k,{children:[e.jsx(C,{children:"编辑用户信息"}),e.jsx(U,{children:"修改用户的基本信息和权限"})]}),e.jsx(x,{children:e.jsxs("form",{onSubmit:c,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"姓名"}),e.jsx("input",{type:"text",value:n.name,onChange:t=>a({...n,name:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"手机号"}),e.jsx("input",{type:"tel",value:n.mobile,onChange:t=>a({...n,mobile:t.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Token"}),e.jsx("input",{type:"number",value:n.token,onChange:t=>a({...n,token:parseInt(t.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{id:"isAdmin",type:"checkbox",checked:n.isAdmin,onChange:t=>a({...n,isAdmin:t.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),e.jsx("label",{htmlFor:"isAdmin",className:"text-sm font-medium",children:"管理员权限"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(h,{type:"submit",children:"保存更改"}),e.jsx(h,{type:"button",variant:"outline",onClick:u,children:"取消"})]})]})})]})}const R=function(){const[l,u]=r.useState([]),[n,a]=r.useState(!0),[c,t]=r.useState(null),[d,j]=r.useState(null),[o,b]=r.useState(""),f=async()=>{try{a(!0);const s=await S();u(s)}catch(s){console.error("获取用户列表失败:",s),t("获取用户列表失败")}finally{a(!1)}};r.useEffect(()=>{f()},[]);const N=async s=>{if(d)try{await D(d.dingTalkUnionId,s),j(null),await f()}catch(g){console.error("更新用户失败:",g),alert("更新用户失败")}},y=async(s,g)=>{if(confirm(`确定要删除用户 "${g}" 吗？此操作不可撤销。`))try{await L(s),await f()}catch(w){console.error("删除用户失败:",w),alert("删除用户失败")}},p=l.filter(s=>s.name?.toLowerCase().includes(o.toLowerCase())||s.dingTalkUnionId.toLowerCase().includes(o.toLowerCase()));return n?e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsx("div",{className:"text-lg",children:"加载中..."})}):c?e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsx("div",{className:"text-lg text-red-500",children:c})}):e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"用户管理"}),e.jsx("p",{className:"text-muted-foreground",children:"管理系统用户信息和权限"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{variant:"secondary",className:"text-sm",children:["总用户: ",l.length]}),e.jsx("a",{href:"/dashboard",className:"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2",children:"返回仪表板"})]})]}),e.jsx(m,{children:e.jsx(x,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",className:"h-4 w-4 text-muted-foreground",children:[e.jsx("circle",{cx:"11",cy:"11",r:"8"}),e.jsx("path",{d:"M21 21l-4.35-4.35"})]}),e.jsx("input",{type:"text",placeholder:"搜索用户（姓名、钉钉ID）...",value:o,onChange:s=>b(s.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})})}),d&&e.jsx(T,{user:d,onSave:N,onCancel:()=>j(null)}),e.jsx("div",{className:"grid gap-4",children:p.length===0?e.jsx(m,{children:e.jsx(x,{className:"pt-6",children:e.jsx("div",{className:"text-center text-muted-foreground",children:o?"没有找到匹配的用户":"暂无用户数据"})})}):p.map(s=>e.jsx(m,{className:"hover:shadow-md transition-shadow",children:e.jsx(x,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold",children:s.name||"未设置姓名"}),s.isAdmin&&e.jsx(v,{variant:"destructive",children:"管理员"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-muted-foreground",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"钉钉ID:"})," ",s.dingTalkUnionId]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"手机:"})," ",s.mobile||"未设置"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Token:"})," ",s.token.toLocaleString()]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"请求次数:"})," ",s.requestTimes]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"创建时间:"})," ",new Date(s.createdAt).toLocaleDateString()]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"更新时间:"})," ",new Date(s.updatedAt).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"flex gap-2 ml-4",children:[e.jsx(h,{variant:"outline",size:"sm",onClick:()=>j(s),children:"编辑"}),e.jsx(h,{variant:"destructive",size:"sm",onClick:()=>y(s.dingTalkUnionId,s.name||s.dingTalkUnionId),children:"删除"})]})]})})},s.id))})]})};export{R as component};
