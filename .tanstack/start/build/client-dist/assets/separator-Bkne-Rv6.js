import{r as s,j as c,c as v}from"./main-CJJWzuV9.js";import{c as u}from"./button-8ggP9MaO.js";import{c as h}from"./index-DFx3G0N8.js";var N=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],x=N.reduce((o,r)=>{const a=u(`Primitive.${r}`),t=s.forwardRef((i,e)=>{const{asChild:l,...n}=i,f=l?a:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),c.jsx(f,{...n,ref:e})});return t.displayName=`Primitive.${r}`,{...o,[r]:t}},{});function b(o,r){o&&v.flushSync(()=>o.dispatchEvent(r))}var E="Separator",p="horizontal",S=["horizontal","vertical"],d=s.forwardRef((o,r)=>{const{decorative:a,orientation:t=p,...i}=o,e=w(t)?t:p,n=a?{role:"none"}:{"aria-orientation":e==="vertical"?e:void 0,role:"separator"};return c.jsx(x.div,{"data-orientation":e,...n,...i,ref:r})});d.displayName=E;function w(o){return S.includes(o)}var m=d;const O=s.forwardRef(({className:o,orientation:r="horizontal",decorative:a=!0,...t},i)=>c.jsx(m,{ref:i,decorative:a,orientation:r,className:h("shrink-0 bg-border",r==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",o),...t}));O.displayName=m.displayName;export{x as P,O as S,b as d};
