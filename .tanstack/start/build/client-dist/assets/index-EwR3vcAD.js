import{g as vl,r as d,j as h,R as de,a as yl,b as If,c as Lf,u as xl}from"./main-CJJWzuV9.js";import{t as Vf,a as hs,c as O}from"./index-DFx3G0N8.js";import{c as le}from"./createLucideIcon-BR-plM33.js";import{L as Do,u as ft,c as tt,d as jt,e as Ff,A as bl,a as wl,b as Sl,U as Bf}from"./user-Bp_enb1S.js";import{B as Ce,u as ie,c as gr,a as $f,S as Fn,b as Cl}from"./button-8ggP9MaO.js";import{c as Uf,p as zf,u as Wf}from"./user-store-X9O8Em_D.js";import{P as te,d as Tl,S as Kf}from"./separator-Bkne-Rv6.js";import{B as Hf}from"./badge-DOzKpig8.js";import{u as El}from"./use-auth-B-Txq4cC.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gf=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Al=le("check",Gf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yf=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Xf=le("chevron-down",Yf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Zf=le("chevron-right",qf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qf=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Jf=le("chevron-up",Qf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eh=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],th=le("circle",eh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nh=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],rh=le("copy",nh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oh=[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]],sh=le("image-plus",oh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ih=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],ah=le("log-out",ih);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lh=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],ch=le("menu",lh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uh=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],dh=le("message-square",uh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fh=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]],hh=le("panel-left-close",fh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ph=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]],mh=le("panel-left-open",ph);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gh=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],vh=le("panel-left",gh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yh=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],xh=le("pen-line",yh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bh=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],wh=le("plus",bh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sh=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Rl=le("trash-2",Sh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ch=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],Th=le("upload",Ch);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eh=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Dr=le("x",Eh);var to,Ci;function Ah(){return Ci||(Ci=1,to=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,o,s;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(s=Object.keys(t),r=s.length,r!==Object.keys(n).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(n,s[o]))return!1;for(o=r;o--!==0;){var i=s[o];if(!e(t[i],n[i]))return!1}return!0}return t!==t&&n!==n}),to}var Rh=Ah();const Ph=vl(Rh),ps=d.createContext({});function ms(e){const t=d.useRef(null);return t.current===null&&(t.current=e()),t.current}const gs=typeof window<"u",Pl=gs?d.useLayoutEffect:d.useEffect,Nr=d.createContext(null);function vs(e,t){e.indexOf(t)===-1&&e.push(t)}function ys(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const nt=(e,t,n)=>n>t?t:n<e?e:n;let xs=()=>{};const rt={},Ml=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function Dl(e){return typeof e=="object"&&e!==null}const Nl=e=>/^0[^.\s]+$/u.test(e);function bs(e){let t;return()=>(t===void 0&&(t=e()),t)}const Ne=e=>e,Mh=(e,t)=>n=>t(e(n)),Bn=(...e)=>e.reduce(Mh),Rn=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r};class ws{constructor(){this.subscriptions=[]}add(t){return vs(this.subscriptions,t),()=>ys(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let s=0;s<o;s++){const i=this.subscriptions[s];i&&i(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ge=e=>e*1e3,Ye=e=>e/1e3;function jl(e,t){return t?e*(1e3/t):0}const kl=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Dh=1e-7,Nh=12;function jh(e,t,n,r,o){let s,i,a=0;do i=t+(n-t)/2,s=kl(i,r,o)-e,s>0?n=i:t=i;while(Math.abs(s)>Dh&&++a<Nh);return i}function $n(e,t,n,r){if(e===t&&n===r)return Ne;const o=s=>jh(s,0,1,e,n);return s=>s===0||s===1?s:kl(o(s),t,r)}const _l=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Ol=e=>t=>1-e(1-t),Il=$n(.33,1.53,.69,.99),Ss=Ol(Il),Ll=_l(Ss),Vl=e=>(e*=2)<1?.5*Ss(e):.5*(2-Math.pow(2,-10*(e-1))),Cs=e=>1-Math.sin(Math.acos(e)),Fl=Ol(Cs),Bl=_l(Cs),kh=$n(.42,0,1,1),_h=$n(0,0,.58,1),$l=$n(.42,0,.58,1),Oh=e=>Array.isArray(e)&&typeof e[0]!="number",Ul=e=>Array.isArray(e)&&typeof e[0]=="number",Ih={linear:Ne,easeIn:kh,easeInOut:$l,easeOut:_h,circIn:Cs,circInOut:Bl,circOut:Fl,backIn:Ss,backInOut:Ll,backOut:Il,anticipate:Vl},Lh=e=>typeof e=="string",Ti=e=>{if(Ul(e)){xs(e.length===4);const[t,n,r,o]=e;return $n(t,n,r,o)}else if(Lh(e))return Ih[e];return e},Jn=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function Vh(e,t){let n=new Set,r=new Set,o=!1,s=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(c){i.has(c)&&(u.schedule(c),e()),c(a)}const u={schedule:(c,f=!1,p=!1)=>{const v=p&&o?n:r;return f&&i.add(c),v.has(c)||v.add(c),c},cancel:c=>{r.delete(c),i.delete(c)},process:c=>{if(a=c,o){s=!0;return}o=!0,[n,r]=[r,n],n.forEach(l),n.clear(),o=!1,s&&(s=!1,u.process(c))}};return u}const Fh=40;function zl(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,i=Jn.reduce((b,S)=>(b[S]=Vh(s),b),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:f,preRender:p,render:m,postRender:v}=i,g=()=>{const b=rt.useManualTiming?o.timestamp:performance.now();n=!1,rt.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(b-o.timestamp,Fh),1)),o.timestamp=b,o.isProcessing=!0,a.process(o),l.process(o),u.process(o),c.process(o),f.process(o),p.process(o),m.process(o),v.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(g))},y=()=>{n=!0,r=!0,o.isProcessing||e(g)};return{schedule:Jn.reduce((b,S)=>{const C=i[S];return b[S]=(E,T=!1,A=!1)=>(n||y(),C.schedule(E,T,A)),b},{}),cancel:b=>{for(let S=0;S<Jn.length;S++)i[Jn[S]].cancel(b)},state:o,steps:i}}const{schedule:q,cancel:ht,state:ue,steps:no}=zl(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ne,!0);let cr;function Bh(){cr=void 0}const xe={now:()=>(cr===void 0&&xe.set(ue.isProcessing||rt.useManualTiming?ue.timestamp:performance.now()),cr),set:e=>{cr=e,queueMicrotask(Bh)}},Wl=e=>t=>typeof t=="string"&&t.startsWith(e),Ts=Wl("--"),$h=Wl("var(--"),Es=e=>$h(e)?Uh.test(e.split("/*")[0].trim()):!1,Uh=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,on={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Pn={...on,transform:e=>nt(0,1,e)},er={...on,default:1},Sn=e=>Math.round(e*1e5)/1e5,As=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function zh(e){return e==null}const Wh=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Rs=(e,t)=>n=>!!(typeof n=="string"&&Wh.test(n)&&n.startsWith(e)||t&&!zh(n)&&Object.prototype.hasOwnProperty.call(n,t)),Kl=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,s,i,a]=r.match(As);return{[e]:parseFloat(o),[t]:parseFloat(s),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},Kh=e=>nt(0,255,e),ro={...on,transform:e=>Math.round(Kh(e))},Et={test:Rs("rgb","red"),parse:Kl("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ro.transform(e)+", "+ro.transform(t)+", "+ro.transform(n)+", "+Sn(Pn.transform(r))+")"};function Hh(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const No={test:Rs("#"),parse:Hh,transform:Et.transform},Un=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),ut=Un("deg"),Xe=Un("%"),V=Un("px"),Gh=Un("vh"),Yh=Un("vw"),Ei={...Xe,parse:e=>Xe.parse(e)/100,transform:e=>Xe.transform(e*100)},Gt={test:Rs("hsl","hue"),parse:Kl("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Xe.transform(Sn(t))+", "+Xe.transform(Sn(n))+", "+Sn(Pn.transform(r))+")"},oe={test:e=>Et.test(e)||No.test(e)||Gt.test(e),parse:e=>Et.test(e)?Et.parse(e):Gt.test(e)?Gt.parse(e):No.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Et.transform(e):Gt.transform(e),getAnimatableNone:e=>{const t=oe.parse(e);return t.alpha=0,oe.transform(t)}},Xh=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function qh(e){return isNaN(e)&&typeof e=="string"&&(e.match(As)?.length||0)+(e.match(Xh)?.length||0)>0}const Hl="number",Gl="color",Zh="var",Qh="var(",Ai="${}",Jh=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Mn(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let s=0;const a=t.replace(Jh,l=>(oe.test(l)?(r.color.push(s),o.push(Gl),n.push(oe.parse(l))):l.startsWith(Qh)?(r.var.push(s),o.push(Zh),n.push(l)):(r.number.push(s),o.push(Hl),n.push(parseFloat(l))),++s,Ai)).split(Ai);return{values:n,split:a,indexes:r,types:o}}function Yl(e){return Mn(e).values}function Xl(e){const{split:t,types:n}=Mn(e),r=t.length;return o=>{let s="";for(let i=0;i<r;i++)if(s+=t[i],o[i]!==void 0){const a=n[i];a===Hl?s+=Sn(o[i]):a===Gl?s+=oe.transform(o[i]):s+=o[i]}return s}}const ep=e=>typeof e=="number"?0:oe.test(e)?oe.getAnimatableNone(e):e;function tp(e){const t=Yl(e);return Xl(e)(t.map(ep))}const pt={test:qh,parse:Yl,createTransformer:Xl,getAnimatableNone:tp};function oo(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function np({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,s=0,i=0;if(!t)o=s=i=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=oo(l,a,e+1/3),s=oo(l,a,e),i=oo(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(s*255),blue:Math.round(i*255),alpha:r}}function vr(e,t){return n=>n>0?t:e}const J=(e,t,n)=>e+(t-e)*n,so=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},rp=[No,Et,Gt],op=e=>rp.find(t=>t.test(e));function Ri(e){const t=op(e);if(!t)return!1;let n=t.parse(e);return t===Gt&&(n=np(n)),n}const Pi=(e,t)=>{const n=Ri(e),r=Ri(t);if(!n||!r)return vr(e,t);const o={...n};return s=>(o.red=so(n.red,r.red,s),o.green=so(n.green,r.green,s),o.blue=so(n.blue,r.blue,s),o.alpha=J(n.alpha,r.alpha,s),Et.transform(o))},jo=new Set(["none","hidden"]);function sp(e,t){return jo.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function ip(e,t){return n=>J(e,t,n)}function Ps(e){return typeof e=="number"?ip:typeof e=="string"?Es(e)?vr:oe.test(e)?Pi:cp:Array.isArray(e)?ql:typeof e=="object"?oe.test(e)?Pi:ap:vr}function ql(e,t){const n=[...e],r=n.length,o=e.map((s,i)=>Ps(s)(s,t[i]));return s=>{for(let i=0;i<r;i++)n[i]=o[i](s);return n}}function ap(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=Ps(e[o])(e[o],t[o]));return o=>{for(const s in r)n[s]=r[s](o);return n}}function lp(e,t){const n=[],r={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const s=t.types[o],i=e.indexes[s][r[s]],a=e.values[i]??0;n[o]=a,r[s]++}return n}const cp=(e,t)=>{const n=pt.createTransformer(t),r=Mn(e),o=Mn(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?jo.has(e)&&!o.values.length||jo.has(t)&&!r.values.length?sp(e,t):Bn(ql(lp(r,o),o.values),n):vr(e,t)};function Zl(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?J(e,t,n):Ps(e)(e,t)}const up=e=>{const t=({timestamp:n})=>e(n);return{start:(n=!0)=>q.update(t,n),stop:()=>ht(t),now:()=>ue.isProcessing?ue.timestamp:xe.now()}},Ql=(e,t,n=10)=>{let r="";const o=Math.max(Math.round(t/n),2);for(let s=0;s<o;s++)r+=Math.round(e(s/(o-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},yr=2e4;function Ms(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<yr;)t+=n,r=e.next(t);return t>=yr?1/0:t}function dp(e,t=100,n){const r=n({...e,keyframes:[0,t]}),o=Math.min(Ms(r),yr);return{type:"keyframes",ease:s=>r.next(o*s).value/t,duration:Ye(o)}}const fp=5;function Jl(e,t,n){const r=Math.max(t-fp,0);return jl(n-e(r),t-r)}const ee={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},io=.001;function hp({duration:e=ee.duration,bounce:t=ee.bounce,velocity:n=ee.velocity,mass:r=ee.mass}){let o,s,i=1-t;i=nt(ee.minDamping,ee.maxDamping,i),e=nt(ee.minDuration,ee.maxDuration,Ye(e)),i<1?(o=u=>{const c=u*i,f=c*e,p=c-n,m=ko(u,i),v=Math.exp(-f);return io-p/m*v},s=u=>{const f=u*i*e,p=f*n+n,m=Math.pow(i,2)*Math.pow(u,2)*e,v=Math.exp(-f),g=ko(Math.pow(u,2),i);return(-o(u)+io>0?-1:1)*((p-m)*v)/g}):(o=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-io+c*f},s=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const a=5/e,l=mp(o,s,a);if(e=Ge(e),isNaN(l))return{stiffness:ee.stiffness,damping:ee.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:i*2*Math.sqrt(r*u),duration:e}}}const pp=12;function mp(e,t,n){let r=n;for(let o=1;o<pp;o++)r=r-e(r)/t(r);return r}function ko(e,t){return e*Math.sqrt(1-t*t)}const gp=["duration","bounce"],vp=["stiffness","damping","mass"];function Mi(e,t){return t.some(n=>e[n]!==void 0)}function yp(e){let t={velocity:ee.velocity,stiffness:ee.stiffness,damping:ee.damping,mass:ee.mass,isResolvedFromDuration:!1,...e};if(!Mi(e,vp)&&Mi(e,gp))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),o=r*r,s=2*nt(.05,1,1-(e.bounce||0))*Math.sqrt(o);t={...t,mass:ee.mass,stiffness:o,damping:s}}else{const n=hp(e);t={...t,...n,mass:ee.mass},t.isResolvedFromDuration=!0}return t}function xr(e=ee.visualDuration,t=ee.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:o}=n;const s=n.keyframes[0],i=n.keyframes[n.keyframes.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:c,duration:f,velocity:p,isResolvedFromDuration:m}=yp({...n,velocity:-Ye(n.velocity||0)}),v=p||0,g=u/(2*Math.sqrt(l*c)),y=i-s,x=Ye(Math.sqrt(l/c)),w=Math.abs(y)<5;r||(r=w?ee.restSpeed.granular:ee.restSpeed.default),o||(o=w?ee.restDelta.granular:ee.restDelta.default);let b;if(g<1){const C=ko(x,g);b=E=>{const T=Math.exp(-g*x*E);return i-T*((v+g*x*y)/C*Math.sin(C*E)+y*Math.cos(C*E))}}else if(g===1)b=C=>i-Math.exp(-x*C)*(y+(v+x*y)*C);else{const C=x*Math.sqrt(g*g-1);b=E=>{const T=Math.exp(-g*x*E),A=Math.min(C*E,300);return i-T*((v+g*x*y)*Math.sinh(A)+C*y*Math.cosh(A))/C}}const S={calculatedDuration:m&&f||null,next:C=>{const E=b(C);if(m)a.done=C>=f;else{let T=C===0?v:0;g<1&&(T=C===0?Ge(v):Jl(b,C,E));const A=Math.abs(T)<=r,R=Math.abs(i-E)<=o;a.done=A&&R}return a.value=a.done?i:E,a},toString:()=>{const C=Math.min(Ms(S),yr),E=Ql(T=>S.next(C*T).value,C,30);return C+"ms "+E},toTransition:()=>{}};return S}xr.applyToOptions=e=>{const t=dp(e,100,xr);return e.ease=t.ease,e.duration=Ge(t.duration),e.type="keyframes",e};function _o({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:s=500,modifyTarget:i,min:a,max:l,restDelta:u=.5,restSpeed:c}){const f=e[0],p={done:!1,value:f},m=A=>a!==void 0&&A<a||l!==void 0&&A>l,v=A=>a===void 0?l:l===void 0||Math.abs(a-A)<Math.abs(l-A)?a:l;let g=n*t;const y=f+g,x=i===void 0?y:i(y);x!==y&&(g=x-f);const w=A=>-g*Math.exp(-A/r),b=A=>x+w(A),S=A=>{const R=w(A),P=b(A);p.done=Math.abs(R)<=u,p.value=p.done?x:P};let C,E;const T=A=>{m(p.value)&&(C=A,E=xr({keyframes:[p.value,v(p.value)],velocity:Jl(b,A,p.value),damping:o,stiffness:s,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:A=>{let R=!1;return!E&&C===void 0&&(R=!0,S(A),T(A)),C!==void 0&&A>=C?E.next(A-C):(!R&&S(A),p)}}}function xp(e,t,n){const r=[],o=n||rt.mix||Zl,s=e.length-1;for(let i=0;i<s;i++){let a=o(e[i],e[i+1]);if(t){const l=Array.isArray(t)?t[i]||Ne:t;a=Bn(l,a)}r.push(a)}return r}function bp(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const s=e.length;if(xs(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const i=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=xp(t,r,o),l=a.length,u=c=>{if(i&&c<e[0])return t[0];let f=0;if(l>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const p=Rn(e[f],e[f+1],c);return a[f](p)};return n?c=>u(nt(e[0],e[s-1],c)):u}function wp(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=Rn(0,t,r);e.push(J(n,1,o))}}function Sp(e){const t=[0];return wp(t,e.length-1),t}function Cp(e,t){return e.map(n=>n*t)}function Tp(e,t){return e.map(()=>t||$l).splice(0,e.length-1)}function Cn({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=Oh(r)?r.map(Ti):Ti(r),s={done:!1,value:t[0]},i=Cp(n&&n.length===t.length?n:Sp(t),e),a=bp(i,t,{ease:Array.isArray(o)?o:Tp(t,o)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}const Ep=e=>e!==null;function Ds(e,{repeat:t,repeatType:n="loop"},r,o=1){const s=e.filter(Ep),a=o<0||t&&n!=="loop"&&t%2===1?0:s.length-1;return!a||r===void 0?s[a]:r}const Ap={decay:_o,inertia:_o,tween:Cn,keyframes:Cn,spring:xr};function ec(e){typeof e.type=="string"&&(e.type=Ap[e.type])}class Ns{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,n){return this.finished.then(t,n)}}const Rp=e=>e/100;class js extends Ns{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==xe.now()&&this.tick(xe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;ec(t);const{type:n=Cn,repeat:r=0,repeatDelay:o=0,repeatType:s,velocity:i=0}=t;let{keyframes:a}=t;const l=n||Cn;l!==Cn&&typeof a[0]!="number"&&(this.mixKeyframes=Bn(Rp,Zl(a[0],a[1])),a=[0,100]);const u=l({...t,keyframes:a});s==="mirror"&&(this.mirroredGenerator=l({...t,keyframes:[...a].reverse(),velocity:-i})),u.calculatedDuration===null&&(u.calculatedDuration=Ms(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+o,this.totalDuration=this.resolvedDuration*(r+1)-o,this.generator=u}updateTime(t){const n=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(t,n=!1){const{generator:r,totalDuration:o,mixKeyframes:s,mirroredGenerator:i,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return r.next(0);const{delay:u=0,keyframes:c,repeat:f,repeatType:p,repeatDelay:m,type:v,onUpdate:g,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-o/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),w=this.playbackSpeed>=0?x<0:x>o;this.currentTime=Math.max(x,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=o);let b=this.currentTime,S=r;if(f){const A=Math.min(this.currentTime,o)/a;let R=Math.floor(A),P=A%1;!P&&A>=1&&(P=1),P===1&&R--,R=Math.min(R,f+1),!!(R%2)&&(p==="reverse"?(P=1-P,m&&(P-=m/a)):p==="mirror"&&(S=i)),b=nt(0,1,P)*a}const C=w?{done:!1,value:c[0]}:S.next(b);s&&(C.value=s(C.value));let{done:E}=C;!w&&l!==null&&(E=this.playbackSpeed>=0?this.currentTime>=o:this.currentTime<=0);const T=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&E);return T&&v!==_o&&(C.value=Ds(c,this.options,y,this.speed)),g&&g(C.value),T&&this.finish(),C}then(t,n){return this.finished.then(t,n)}get duration(){return Ye(this.calculatedDuration)}get time(){return Ye(this.currentTime)}set time(t){t=Ge(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(xe.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Ye(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=up,startTime:n}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=n??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(xe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Pp(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const At=e=>e*180/Math.PI,Oo=e=>{const t=At(Math.atan2(e[1],e[0]));return Io(t)},Mp={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Oo,rotateZ:Oo,skewX:e=>At(Math.atan(e[1])),skewY:e=>At(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Io=e=>(e=e%360,e<0&&(e+=360),e),Di=Oo,Ni=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),ji=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),Dp={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ni,scaleY:ji,scale:e=>(Ni(e)+ji(e))/2,rotateX:e=>Io(At(Math.atan2(e[6],e[5]))),rotateY:e=>Io(At(Math.atan2(-e[2],e[0]))),rotateZ:Di,rotate:Di,skewX:e=>At(Math.atan(e[4])),skewY:e=>At(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Lo(e){return e.includes("scale")?1:0}function Vo(e,t){if(!e||e==="none")return Lo(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,o;if(n)r=Dp,o=n;else{const a=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=Mp,o=a}if(!o)return Lo(t);const s=r[t],i=o[1].split(",").map(jp);return typeof s=="function"?s(i):i[s]}const Np=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return Vo(n,t)};function jp(e){return parseFloat(e.trim())}const sn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],an=new Set(sn),ki=e=>e===on||e===V,kp=new Set(["x","y","z"]),_p=sn.filter(e=>!kp.has(e));function Op(e){const t=[];return _p.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Rt={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Vo(t,"x"),y:(e,{transform:t})=>Vo(t,"y")};Rt.translateX=Rt.x;Rt.translateY=Rt.y;const Pt=new Set;let Fo=!1,Bo=!1,$o=!1;function tc(){if(Bo){const e=Array.from(Pt).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=Op(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([s,i])=>{r.getValue(s)?.set(i)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Bo=!1,Fo=!1,Pt.forEach(e=>e.complete($o)),Pt.clear()}function nc(){Pt.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Bo=!0)})}function Ip(){$o=!0,nc(),tc(),$o=!1}class ks{constructor(t,n,r,o,s,i=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=s,this.isAsync=i}scheduleResolve(){this.state="scheduled",this.isAsync?(Pt.add(this),Fo||(Fo=!0,q.read(nc),q.resolveKeyframes(tc))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;if(t[0]===null){const s=o?.get(),i=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const a=r.readValue(n,i);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=i),o&&s===void 0&&o.set(t[0])}Pp(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Pt.delete(this)}cancel(){this.state==="scheduled"&&(Pt.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Lp=e=>e.startsWith("--");function Vp(e,t,n){Lp(t)?e.style.setProperty(t,n):e.style[t]=n}const Fp=bs(()=>window.ScrollTimeline!==void 0),Bp={};function $p(e,t){const n=bs(e);return()=>Bp[t]??n()}const rc=$p(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),bn=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,_i={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:bn([0,.65,.55,1]),circOut:bn([.55,0,1,.45]),backIn:bn([.31,.01,.66,-.59]),backOut:bn([.33,1.53,.69,.99])};function oc(e,t){if(e)return typeof e=="function"?rc()?Ql(e,t):"ease-out":Ul(e)?bn(e):Array.isArray(e)?e.map(n=>oc(n,t)||_i.easeOut):_i[e]}function Up(e,t,n,{delay:r=0,duration:o=300,repeat:s=0,repeatType:i="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[t]:n};l&&(c.offset=l);const f=oc(a,o);Array.isArray(f)&&(c.easing=f);const p={delay:r,duration:o,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:s+1,direction:i==="reverse"?"alternate":"normal"};return u&&(p.pseudoElement=u),e.animate(c,p)}function sc(e){return typeof e=="function"&&"applyToOptions"in e}function zp({type:e,...t}){return sc(e)&&rc()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Wp extends Ns{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:r,keyframes:o,pseudoElement:s,allowFlatten:i=!1,finalKeyframe:a,onComplete:l}=t;this.isPseudoElement=!!s,this.allowFlatten=i,this.options=t,xs(typeof t.type!="string");const u=zp(t);this.animation=Up(n,r,o,u,s),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const c=Ds(o,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(c):Vp(n,r,c),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return Ye(Number(t))}get time(){return Ye(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Ge(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Fp()?(this.animation.timeline=t,Ne):n(this)}}const ic={anticipate:Vl,backInOut:Ll,circInOut:Bl};function Kp(e){return e in ic}function Hp(e){typeof e.ease=="string"&&Kp(e.ease)&&(e.ease=ic[e.ease])}const Oi=10;class Gp extends Wp{constructor(t){Hp(t),ec(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:r,onComplete:o,element:s,...i}=this.options;if(!n)return;if(t!==void 0){n.set(t);return}const a=new js({...i,autoplay:!1}),l=Ge(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-Oi).value,a.sample(l).value,Oi),a.stop()}}const Ii=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(pt.test(e)||e==="0")&&!e.startsWith("url("));function Yp(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Xp(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],i=Ii(o,t),a=Ii(s,t);return!i||!a?!1:Yp(e)||(n==="spring"||sc(n))&&r}function Uo(e){e.duration=0,e.type}const qp=new Set(["opacity","clipPath","filter","transform"]),Zp=bs(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Qp(e){const{motionValue:t,name:n,repeatDelay:r,repeatType:o,damping:s,type:i}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=t.owner.getProps();return Zp()&&n&&qp.has(n)&&(n!=="transform"||!u)&&!l&&!r&&o!=="mirror"&&s!==0&&i!=="inertia"}const Jp=40;class em extends Ns{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:i="loop",keyframes:a,name:l,motionValue:u,element:c,...f}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=xe.now();const p={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:s,repeatType:i,name:l,motionValue:u,element:c,...f},m=c?.KeyframeResolver||ks;this.keyframeResolver=new m(a,(v,g,y)=>this.onKeyframesResolved(v,g,p,!y),l,u,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,r,o){this.keyframeResolver=void 0;const{name:s,type:i,velocity:a,delay:l,isHandoff:u,onUpdate:c}=r;this.resolvedAt=xe.now(),Xp(t,s,i,a)||((rt.instantAnimations||!l)&&c?.(Ds(t,r,n)),t[0]=t[t.length-1],Uo(r),r.repeat=0);const p={startTime:o?this.resolvedAt?this.resolvedAt-this.createdAt>Jp?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...r,keyframes:t},m=!u&&Qp(p)?new Gp({...p,element:p.motionValue.owner.current}):new js(p);m.finished.then(()=>this.notifyFinished()).catch(Ne),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,n){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Ip()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const tm=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function nm(e){const t=tm.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function ac(e,t,n=1){const[r,o]=nm(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const i=s.trim();return Ml(i)?parseFloat(i):i}return Es(o)?ac(o,t,n+1):o}function _s(e,t){return e?.[t]??e?.default??e}const lc=new Set(["width","height","top","left","right","bottom",...sn]),rm={test:e=>e==="auto",parse:e=>e},cc=e=>t=>t.test(e),uc=[on,V,Xe,ut,Yh,Gh,rm],Li=e=>uc.find(cc(e));function om(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Nl(e):!0}const sm=new Set(["brightness","contrast","saturate","opacity"]);function im(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(As)||[];if(!r)return e;const o=n.replace(r,"");let s=sm.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+o+")"}const am=/\b([a-z-]*)\(.*?\)/gu,zo={...pt,getAnimatableNone:e=>{const t=e.match(am);return t?t.map(im).join(" "):e}},Vi={...on,transform:Math.round},lm={rotate:ut,rotateX:ut,rotateY:ut,rotateZ:ut,scale:er,scaleX:er,scaleY:er,scaleZ:er,skew:ut,skewX:ut,skewY:ut,distance:V,translateX:V,translateY:V,translateZ:V,x:V,y:V,z:V,perspective:V,transformPerspective:V,opacity:Pn,originX:Ei,originY:Ei,originZ:V},Os={borderWidth:V,borderTopWidth:V,borderRightWidth:V,borderBottomWidth:V,borderLeftWidth:V,borderRadius:V,radius:V,borderTopLeftRadius:V,borderTopRightRadius:V,borderBottomRightRadius:V,borderBottomLeftRadius:V,width:V,maxWidth:V,height:V,maxHeight:V,top:V,right:V,bottom:V,left:V,padding:V,paddingTop:V,paddingRight:V,paddingBottom:V,paddingLeft:V,margin:V,marginTop:V,marginRight:V,marginBottom:V,marginLeft:V,backgroundPositionX:V,backgroundPositionY:V,...lm,zIndex:Vi,fillOpacity:Pn,strokeOpacity:Pn,numOctaves:Vi},cm={...Os,color:oe,backgroundColor:oe,outlineColor:oe,fill:oe,stroke:oe,borderColor:oe,borderTopColor:oe,borderRightColor:oe,borderBottomColor:oe,borderLeftColor:oe,filter:zo,WebkitFilter:zo},dc=e=>cm[e];function fc(e,t){let n=dc(e);return n!==zo&&(n=pt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const um=new Set(["auto","none","0"]);function dm(e,t,n){let r=0,o;for(;r<e.length&&!o;){const s=e[r];typeof s=="string"&&!um.has(s)&&Mn(s).values.length&&(o=e[r]),r++}if(o&&n)for(const s of t)e[s]=fc(n,o)}class fm extends ks{constructor(t,n,r,o,s){super(t,n,r,o,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),Es(u))){const c=ac(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!lc.has(r)||t.length!==2)return;const[o,s]=t,i=Li(o),a=Li(s);if(i!==a)if(ki(i)&&ki(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else Rt[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)(t[o]===null||om(t[o]))&&r.push(o);r.length&&dm(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Rt[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const o=t.getValue(n);o&&o.jump(this.measuredOrigin,!1);const s=r.length-1,i=r[s];r[s]=Rt[n](t.measureViewportBox(),window.getComputedStyle(t.current)),i!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,l])=>{t.getValue(a).set(l)}),this.resolveNoneKeyframes()}}function hm(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let r=document;const o=n?.[e]??r.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}const hc=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function pc(e){return Dl(e)&&"offsetHeight"in e}const Fi=30,pm=e=>!isNaN(parseFloat(e));class mm{constructor(t,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=r=>{const o=xe.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const s of this.dependents)s.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=xe.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=pm(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new ws);const r=this.events[t].add(n);return t==="change"?()=>{r(),q.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=xe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Fi)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Fi);return jl(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function en(e,t){return new mm(e,t)}const{schedule:Is}=zl(queueMicrotask,!1),Oe={x:!1,y:!1};function mc(){return Oe.x||Oe.y}function gm(e){return e==="x"||e==="y"?Oe[e]?null:(Oe[e]=!0,()=>{Oe[e]=!1}):Oe.x||Oe.y?null:(Oe.x=Oe.y=!0,()=>{Oe.x=Oe.y=!1})}function gc(e,t){const n=hm(e),r=new AbortController,o={passive:!0,...t,signal:r.signal};return[n,o,()=>r.abort()]}function Bi(e){return!(e.pointerType==="touch"||mc())}function vm(e,t,n={}){const[r,o,s]=gc(e,n),i=a=>{if(!Bi(a))return;const{target:l}=a,u=t(l,a);if(typeof u!="function"||!l)return;const c=f=>{Bi(f)&&(u(f),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,o)};return r.forEach(a=>{a.addEventListener("pointerenter",i,o)}),s}const vc=(e,t)=>t?e===t?!0:vc(e,t.parentElement):!1,Ls=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,ym=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function xm(e){return ym.has(e.tagName)||e.tabIndex!==-1}const ur=new WeakSet;function $i(e){return t=>{t.key==="Enter"&&e(t)}}function ao(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const bm=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=$i(()=>{if(ur.has(n))return;ao(n,"down");const o=$i(()=>{ao(n,"up")}),s=()=>ao(n,"cancel");n.addEventListener("keyup",o,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Ui(e){return Ls(e)&&!mc()}function wm(e,t,n={}){const[r,o,s]=gc(e,n),i=a=>{const l=a.currentTarget;if(!Ui(a))return;ur.add(l);const u=t(l,a),c=(m,v)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",p),ur.has(l)&&ur.delete(l),Ui(m)&&typeof u=="function"&&u(m,{success:v})},f=m=>{c(m,l===window||l===document||n.useGlobalTarget||vc(l,m.target))},p=m=>{c(m,!1)};window.addEventListener("pointerup",f,o),window.addEventListener("pointercancel",p,o)};return r.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",i,o),pc(a)&&(a.addEventListener("focus",u=>bm(u,o)),!xm(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),s}function yc(e){return Dl(e)&&"ownerSVGElement"in e}function Sm(e){return yc(e)&&e.tagName==="svg"}const he=e=>!!(e&&e.getVelocity),Cm=[...uc,oe,pt],Tm=e=>Cm.find(cc(e)),Vs=d.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class Em extends d.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,o=pc(r)&&r.offsetWidth||0,s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft,s.right=o-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Am({children:e,isPresent:t,anchorX:n,root:r}){const o=d.useId(),s=d.useRef(null),i=d.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=d.useContext(Vs);return d.useInsertionEffect(()=>{const{width:l,height:u,top:c,left:f,right:p}=i.current;if(t||!s.current||!l||!u)return;const m=n==="left"?`left: ${f}`:`right: ${p}`;s.current.dataset.motionPopId=o;const v=document.createElement("style");a&&(v.nonce=a);const g=r??document.head;return g.appendChild(v),v.sheet&&v.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${l}px !important;
            height: ${u}px !important;
            ${m}px !important;
            top: ${c}px !important;
          }
        `),()=>{g.contains(v)&&g.removeChild(v)}},[t]),h.jsx(Em,{isPresent:t,childRef:s,sizeRef:i,children:d.cloneElement(e,{ref:s})})}const Rm=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:s,mode:i,anchorX:a,root:l})=>{const u=ms(Pm),c=d.useId();let f=!0,p=d.useMemo(()=>(f=!1,{id:c,initial:t,isPresent:n,custom:o,onExitComplete:m=>{u.set(m,!0);for(const v of u.values())if(!v)return;r&&r()},register:m=>(u.set(m,!1),()=>u.delete(m))}),[n,u,r]);return s&&f&&(p={...p}),d.useMemo(()=>{u.forEach((m,v)=>u.set(v,!1))},[n]),d.useEffect(()=>{!n&&!u.size&&r&&r()},[n]),i==="popLayout"&&(e=h.jsx(Am,{isPresent:n,anchorX:a,root:l,children:e})),h.jsx(Nr.Provider,{value:p,children:e})};function Pm(){return new Map}function xc(e=!0){const t=d.useContext(Nr);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,s=d.useId();d.useEffect(()=>{if(e)return o(s)},[e]);const i=d.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,i]:[!0]}const tr=e=>e.key||"";function zi(e){const t=[];return d.Children.forEach(e,n=>{d.isValidElement(n)&&t.push(n)}),t}const bc=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:s="sync",propagate:i=!1,anchorX:a="left",root:l})=>{const[u,c]=xc(i),f=d.useMemo(()=>zi(e),[e]),p=i&&!u?[]:f.map(tr),m=d.useRef(!0),v=d.useRef(f),g=ms(()=>new Map),[y,x]=d.useState(f),[w,b]=d.useState(f);Pl(()=>{m.current=!1,v.current=f;for(let E=0;E<w.length;E++){const T=tr(w[E]);p.includes(T)?g.delete(T):g.get(T)!==!0&&g.set(T,!1)}},[w,p.length,p.join("-")]);const S=[];if(f!==y){let E=[...f];for(let T=0;T<w.length;T++){const A=w[T],R=tr(A);p.includes(R)||(E.splice(T,0,A),S.push(A))}return s==="wait"&&S.length&&(E=S),b(zi(E)),x(f),null}const{forceRender:C}=d.useContext(ps);return h.jsx(h.Fragment,{children:w.map(E=>{const T=tr(E),A=i&&!u?!1:f===w||p.includes(T),R=()=>{if(g.has(T))g.set(T,!0);else return;let P=!0;g.forEach(I=>{I||(P=!1)}),P&&(C?.(),b(v.current),i&&c?.(),r&&r())};return h.jsx(Rm,{isPresent:A,initial:!m.current||n?void 0:!1,custom:t,presenceAffectsLayout:o,mode:s,root:l,onExitComplete:A?void 0:R,anchorX:a,children:E},T)})})},wc=d.createContext({strict:!1}),Wi={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},tn={};for(const e in Wi)tn[e]={isEnabled:t=>Wi[e].some(n=>!!t[n])};function Mm(e){for(const t in e)tn[t]={...tn[t],...e[t]}}const Dm=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function br(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Dm.has(e)}let Sc=e=>!br(e);function Nm(e){typeof e=="function"&&(Sc=t=>t.startsWith("on")?!br(t):e(t))}try{Nm(require("@emotion/is-prop-valid").default)}catch{}function jm(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Sc(o)||n===!0&&br(o)||!t&&!br(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}const jr=d.createContext({});function kr(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function Dn(e){return typeof e=="string"||Array.isArray(e)}const Fs=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Bs=["initial",...Fs];function _r(e){return kr(e.animate)||Bs.some(t=>Dn(e[t]))}function Cc(e){return!!(_r(e)||e.variants)}function km(e,t){if(_r(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Dn(n)?n:void 0,animate:Dn(r)?r:void 0}}return e.inherit!==!1?t:{}}function _m(e){const{initial:t,animate:n}=km(e,d.useContext(jr));return d.useMemo(()=>({initial:t,animate:n}),[Ki(t),Ki(n)])}function Ki(e){return Array.isArray(e)?e.join(" "):e}const Nn={};function Om(e){for(const t in e)Nn[t]=e[t],Ts(t)&&(Nn[t].isCSSVariable=!0)}function Tc(e,{layout:t,layoutId:n}){return an.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Nn[e]||e==="opacity")}const Im={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Lm=sn.length;function Vm(e,t,n){let r="",o=!0;for(let s=0;s<Lm;s++){const i=sn[s],a=e[i];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(i.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=hc(a,Os[i]);if(!l){o=!1;const c=Im[i]||i;r+=`${c}(${u}) `}n&&(t[i]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function $s(e,t,n){const{style:r,vars:o,transformOrigin:s}=e;let i=!1,a=!1;for(const l in t){const u=t[l];if(an.has(l)){i=!0;continue}else if(Ts(l)){o[l]=u;continue}else{const c=hc(u,Os[l]);l.startsWith("origin")?(a=!0,s[l]=c):r[l]=c}}if(t.transform||(i||n?r.transform=Vm(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin=`${l} ${u} ${c}`}}const Us=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ec(e,t,n){for(const r in t)!he(t[r])&&!Tc(r,n)&&(e[r]=t[r])}function Fm({transformTemplate:e},t){return d.useMemo(()=>{const n=Us();return $s(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Bm(e,t){const n=e.style||{},r={};return Ec(r,n,e),Object.assign(r,Fm(e,t)),r}function $m(e,t){const n={},r=Bm(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Um={offset:"stroke-dashoffset",array:"stroke-dasharray"},zm={offset:"strokeDashoffset",array:"strokeDasharray"};function Wm(e,t,n=1,r=0,o=!0){e.pathLength=1;const s=o?Um:zm;e[s.offset]=V.transform(-r);const i=V.transform(t),a=V.transform(n);e[s.array]=`${i} ${a}`}function Ac(e,{attrX:t,attrY:n,attrScale:r,pathLength:o,pathSpacing:s=1,pathOffset:i=0,...a},l,u,c){if($s(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:p}=e;f.transform&&(p.transform=f.transform,delete f.transform),(p.transform||f.transformOrigin)&&(p.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),p.transform&&(p.transformBox=c?.transformBox??"fill-box",delete f.transformBox),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&Wm(f,o,s,i,!1)}const Rc=()=>({...Us(),attrs:{}}),Pc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Km(e,t,n,r){const o=d.useMemo(()=>{const s=Rc();return Ac(s,t,Pc(r),e.transformTemplate,e.style),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};Ec(s,e.style,e),o.style={...s,...o.style}}return o}const Hm=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function zs(e){return typeof e!="string"||e.includes("-")?!1:!!(Hm.indexOf(e)>-1||/[A-Z]/u.test(e))}function Gm(e,t,n,{latestValues:r},o,s=!1){const a=(zs(e)?Km:$m)(t,r,o,e),l=jm(t,typeof e=="string",s),u=e!==d.Fragment?{...l,...a,ref:n}:{},{children:c}=t,f=d.useMemo(()=>he(c)?c.get():c,[c]);return d.createElement(e,{...u,children:f})}function Hi(e){const t=[{},{}];return e?.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Ws(e,t,n,r){if(typeof t=="function"){const[o,s]=Hi(r);t=t(n!==void 0?n:e.custom,o,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,s]=Hi(r);t=t(n!==void 0?n:e.custom,o,s)}return t}function dr(e){return he(e)?e.get():e}function Ym({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:Xm(n,r,o,e),renderState:t()}}function Xm(e,t,n,r){const o={},s=r(e,{});for(const p in s)o[p]=dr(s[p]);let{initial:i,animate:a}=e;const l=_r(e),u=Cc(e);t&&u&&!l&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||i===!1;const f=c?a:i;if(f&&typeof f!="boolean"&&!kr(f)){const p=Array.isArray(f)?f:[f];for(let m=0;m<p.length;m++){const v=Ws(e,p[m]);if(v){const{transitionEnd:g,transition:y,...x}=v;for(const w in x){let b=x[w];if(Array.isArray(b)){const S=c?b.length-1:0;b=b[S]}b!==null&&(o[w]=b)}for(const w in g)o[w]=g[w]}}}return o}const Mc=e=>(t,n)=>{const r=d.useContext(jr),o=d.useContext(Nr),s=()=>Ym(e,t,r,o);return n?s():ms(s)};function Ks(e,t,n){const{style:r}=e,o={};for(const s in r)(he(r[s])||t.style&&he(t.style[s])||Tc(s,e)||n?.getValue(s)?.liveStyle!==void 0)&&(o[s]=r[s]);return o}const qm=Mc({scrapeMotionValuesFromProps:Ks,createRenderState:Us});function Dc(e,t,n){const r=Ks(e,t,n);for(const o in e)if(he(e[o])||he(t[o])){const s=sn.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[s]=e[o]}return r}const Zm=Mc({scrapeMotionValuesFromProps:Dc,createRenderState:Rc}),Qm=Symbol.for("motionComponentSymbol");function Yt(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Jm(e,t,n){return d.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Yt(n)&&(n.current=r))},[t])}const Hs=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eg="framerAppearId",Nc="data-"+Hs(eg),jc=d.createContext({});function tg(e,t,n,r,o){const{visualElement:s}=d.useContext(jr),i=d.useContext(wc),a=d.useContext(Nr),l=d.useContext(Vs).reducedMotion,u=d.useRef(null);r=r||i.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:s,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,f=d.useContext(jc);c&&!c.projection&&o&&(c.type==="html"||c.type==="svg")&&ng(u.current,n,o,f);const p=d.useRef(!1);d.useInsertionEffect(()=>{c&&p.current&&c.update(n,a)});const m=n[Nc],v=d.useRef(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return Pl(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),v.current&&c.animationState&&c.animationState.animateChanges())}),d.useEffect(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),v.current=!1))}),c}function ng(e,t,n,r){const{layoutId:o,layout:s,drag:i,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:kc(e.parent)),e.projection.setOptions({layoutId:o,layout:s,alwaysMeasureLayout:!!i||a&&Yt(a),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}function kc(e){if(e)return e.options.allowProjection!==!1?e.projection:kc(e.parent)}function lo(e,{forwardMotionProps:t=!1}={},n,r){n&&Mm(n);const o=zs(e)?Zm:qm;function s(a,l){let u;const c={...d.useContext(Vs),...a,layoutId:rg(a)},{isStatic:f}=c,p=_m(a),m=o(a,f);if(!f&&gs){og();const v=sg(c);u=v.MeasureLayout,p.visualElement=tg(e,m,c,r,v.ProjectionNode)}return h.jsxs(jr.Provider,{value:p,children:[u&&p.visualElement?h.jsx(u,{visualElement:p.visualElement,...c}):null,Gm(e,a,Jm(m,p.visualElement,l),m,f,t)]})}s.displayName=`motion.${typeof e=="string"?e:`create(${e.displayName??e.name??""})`}`;const i=d.forwardRef(s);return i[Qm]=e,i}function rg({layoutId:e}){const t=d.useContext(ps).id;return t&&e!==void 0?t+"-"+e:e}function og(e,t){d.useContext(wc).strict}function sg(e){const{drag:t,layout:n}=tn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function ig(e,t){if(typeof Proxy>"u")return lo;const n=new Map,r=(s,i)=>lo(s,i,e,t),o=(s,i)=>r(s,i);return new Proxy(o,{get:(s,i)=>i==="create"?r:(n.has(i)||n.set(i,lo(i,void 0,e,t)),n.get(i))})}function _c({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function ag({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function lg(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function co(e){return e===void 0||e===1}function Wo({scale:e,scaleX:t,scaleY:n}){return!co(e)||!co(t)||!co(n)}function Tt(e){return Wo(e)||Oc(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Oc(e){return Gi(e.x)||Gi(e.y)}function Gi(e){return e&&e!=="0%"}function wr(e,t,n){const r=e-n,o=t*r;return n+o}function Yi(e,t,n,r,o){return o!==void 0&&(e=wr(e,o,r)),wr(e,n,r)+t}function Ko(e,t=0,n=1,r,o){e.min=Yi(e.min,t,n,r,o),e.max=Yi(e.max,t,n,r,o)}function Ic(e,{x:t,y:n}){Ko(e.x,t.translate,t.scale,t.originPoint),Ko(e.y,n.translate,n.scale,n.originPoint)}const Xi=.999999999999,qi=1.0000000000001;function cg(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let s,i;for(let a=0;a<o;a++){s=n[a],i=s.projectionDelta;const{visualElement:l}=s.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&qt(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,Ic(e,i)),r&&Tt(s.latestValues)&&qt(e,s.latestValues))}t.x<qi&&t.x>Xi&&(t.x=1),t.y<qi&&t.y>Xi&&(t.y=1)}function Xt(e,t){e.min=e.min+t,e.max=e.max+t}function Zi(e,t,n,r,o=.5){const s=J(e.min,e.max,o);Ko(e,t,n,s,r)}function qt(e,t){Zi(e.x,t.x,t.scaleX,t.scale,t.originX),Zi(e.y,t.y,t.scaleY,t.scale,t.originY)}function Lc(e,t){return _c(lg(e.getBoundingClientRect(),t))}function ug(e,t,n){const r=Lc(e,n),{scroll:o}=t;return o&&(Xt(r.x,o.offset.x),Xt(r.y,o.offset.y)),r}const Qi=()=>({translate:0,scale:1,origin:0,originPoint:0}),Zt=()=>({x:Qi(),y:Qi()}),Ji=()=>({min:0,max:0}),ne=()=>({x:Ji(),y:Ji()}),Ho={current:null},Vc={current:!1};function dg(){if(Vc.current=!0,!!gs)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ho.current=e.matches;e.addEventListener("change",t),t()}else Ho.current=!1}const fg=new WeakMap;function hg(e,t,n){for(const r in t){const o=t[r],s=n[r];if(he(o))e.addValue(r,o);else if(he(s))e.addValue(r,en(o,{owner:e}));else if(s!==o)if(e.hasValue(r)){const i=e.getValue(r);i.liveStyle===!0?i.jump(o):i.hasAnimated||i.set(o)}else{const i=e.getStaticValue(r);e.addValue(r,en(i!==void 0?i:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const ea=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class pg{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:s,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ks,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const p=xe.now();this.renderScheduledAt<p&&(this.renderScheduledAt=p,q.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=i;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=_r(n),this.isVariantNode=Cc(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const p in f){const m=f[p];l[p]!==void 0&&he(m)&&m.set(l[p])}}mount(t){this.current=t,fg.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Vc.current||dg(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ho.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ht(this.notifyUpdate),ht(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=an.has(t);r&&this.onBindTransform&&this.onBindTransform();const o=n.on("change",i=>{this.latestValues[t]=i,this.props.onUpdate&&q.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in tn){const n=tn[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<ea.length;r++){const o=ea[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const s="on"+o,i=t[s];i&&(this.propEventSubscriptions[o]=this.on(o,i))}this.prevMotionValues=hg(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=en(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(Ml(r)||Nl(r))?r=parseFloat(r):!Tm(r)&&pt.test(n)&&(r=fc(t,n)),this.setBaseTarget(t,he(r)?r.get():r)),he(r)?r.get():r}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const s=Ws(this.props,n,this.presenceContext?.custom);s&&(r=s[t])}if(n&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!he(o)?o:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ws),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}scheduleRenderMicrotask(){Is.render(this.render)}}class Fc extends pg{constructor(){super(...arguments),this.KeyframeResolver=fm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;he(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Bc(e,{style:t,vars:n},r,o){const s=e.style;let i;for(i in t)s[i]=t[i];o?.applyProjectionStyles(s,r);for(i in n)s.setProperty(i,n[i])}function mg(e){return window.getComputedStyle(e)}class gg extends Fc{constructor(){super(...arguments),this.type="html",this.renderInstance=Bc}readValueFromInstance(t,n){if(an.has(n))return this.projection?.isProjecting?Lo(n):Np(t,n);{const r=mg(t),o=(Ts(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Lc(t,n)}build(t,n,r){$s(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Ks(t,n,r)}}const $c=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function vg(e,t,n,r){Bc(e,t,void 0,r);for(const o in t.attrs)e.setAttribute($c.has(o)?o:Hs(o),t.attrs[o])}class yg extends Fc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ne}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(an.has(n)){const r=dc(n);return r&&r.default||0}return n=$c.has(n)?n:Hs(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Dc(t,n,r)}build(t,n,r){Ac(t,n,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,n,r,o){vg(t,n,r,o)}mount(t){this.isSVGTag=Pc(t.tagName),super.mount(t)}}const xg=(e,t)=>zs(e)?new yg(t):new gg(t,{allowProjection:e!==d.Fragment});function jn(e,t,n){const r=e.getProps();return Ws(r,t,n!==void 0?n:r.custom,e)}const Go=e=>Array.isArray(e);function bg(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,en(n))}function wg(e){return Go(e)?e[e.length-1]||0:e}function Sg(e,t){const n=jn(e,t);let{transitionEnd:r={},transition:o={},...s}=n||{};s={...s,...r};for(const i in s){const a=wg(s[i]);bg(e,i,a)}}function Cg(e){return!!(he(e)&&e.add)}function Yo(e,t){const n=e.getValue("willChange");if(Cg(n))return n.add(t);if(!n&&rt.WillChange){const r=new rt.WillChange("auto");e.addValue("willChange",r),r.add(t)}}function Uc(e){return e.props[Nc]}const Tg=e=>e!==null;function Eg(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(Tg),s=t&&n!=="loop"&&t%2===1?0:o.length-1;return o[s]}const Ag={type:"spring",stiffness:500,damping:25,restSpeed:10},Rg=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Pg={type:"keyframes",duration:.8},Mg={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Dg=(e,{keyframes:t})=>t.length>2?Pg:an.has(e)?e.startsWith("scale")?Rg(t[1]):Ag:Mg;function Ng({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:s,repeatType:i,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Gs=(e,t,n,r={},o,s)=>i=>{const a=_s(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Ge(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:p=>{t.set(p),a.onUpdate&&a.onUpdate(p)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:o};Ng(a)||Object.assign(c,Dg(e,c)),c.duration&&(c.duration=Ge(c.duration)),c.repeatDelay&&(c.repeatDelay=Ge(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(Uo(c),c.delay===0&&(f=!0)),(rt.instantAnimations||rt.skipAnimations)&&(f=!0,Uo(c),c.delay=0),c.allowFlatten=!a.type&&!a.ease,f&&!s&&t.get()!==void 0){const p=Eg(c.keyframes,a);if(p!==void 0){q.update(()=>{c.onUpdate(p),c.onComplete()});return}}return a.isSync?new js(c):new em(c)};function jg({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function zc(e,t,{delay:n=0,transitionOverride:r,type:o}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:i,...a}=t;r&&(s=r);const l=[],u=o&&e.animationState&&e.animationState.getState()[o];for(const c in a){const f=e.getValue(c,e.latestValues[c]??null),p=a[c];if(p===void 0||u&&jg(u,c))continue;const m={delay:n,..._s(s||{},c)},v=f.get();if(v!==void 0&&!f.isAnimating&&!Array.isArray(p)&&p===v&&!m.velocity)continue;let g=!1;if(window.MotionHandoffAnimation){const x=Uc(e);if(x){const w=window.MotionHandoffAnimation(x,c,q);w!==null&&(m.startTime=w,g=!0)}}Yo(e,c),f.start(Gs(c,f,p,e.shouldReduceMotion&&lc.has(c)?{type:!1}:m,e,g));const y=f.animation;y&&l.push(y)}return i&&Promise.all(l).then(()=>{q.update(()=>{i&&Sg(e,i)})}),l}function Xo(e,t,n={}){const r=jn(e,t,n.type==="exit"?e.presenceContext?.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const s=r?()=>Promise.all(zc(e,r,n)):()=>Promise.resolve(),i=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:f}=o;return kg(e,t,l,u,c,f,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[l,u]=a==="beforeChildren"?[s,i]:[i,s];return l().then(()=>u())}else return Promise.all([s(),i(n.delay)])}function kg(e,t,n=0,r=0,o=0,s=1,i){const a=[],l=e.variantChildren.size,u=(l-1)*o,c=typeof r=="function",f=c?p=>r(p,l):s===1?(p=0)=>p*o:(p=0)=>u-p*o;return Array.from(e.variantChildren).sort(_g).forEach((p,m)=>{p.notify("AnimationStart",t),a.push(Xo(p,t,{...i,delay:n+(c?0:r)+f(m)}).then(()=>p.notify("AnimationComplete",t)))}),Promise.all(a)}function _g(e,t){return e.sortNodePosition(t)}function Og(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(s=>Xo(e,s,n));r=Promise.all(o)}else if(typeof t=="string")r=Xo(e,t,n);else{const o=typeof t=="function"?jn(e,t,n.custom):t;r=Promise.all(zc(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}function Wc(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Ig=Bs.length;function Kc(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Kc(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<Ig;n++){const r=Bs[n],o=e.props[r];(Dn(o)||o===!1)&&(t[r]=o)}return t}const Lg=[...Fs].reverse(),Vg=Fs.length;function Fg(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Og(e,n,r)))}function Bg(e){let t=Fg(e),n=ta(),r=!0;const o=l=>(u,c)=>{const f=jn(e,c,l==="exit"?e.presenceContext?.custom:void 0);if(f){const{transition:p,transitionEnd:m,...v}=f;u={...u,...v,...m}}return u};function s(l){t=l(e)}function i(l){const{props:u}=e,c=Kc(e.parent)||{},f=[],p=new Set;let m={},v=1/0;for(let y=0;y<Vg;y++){const x=Lg[y],w=n[x],b=u[x]!==void 0?u[x]:c[x],S=Dn(b),C=x===l?w.isActive:null;C===!1&&(v=y);let E=b===c[x]&&b!==u[x]&&S;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),w.protectedKeys={...m},!w.isActive&&C===null||!b&&!w.prevProp||kr(b)||typeof b=="boolean")continue;const T=$g(w.prevProp,b);let A=T||x===l&&w.isActive&&!E&&S||y>v&&S,R=!1;const P=Array.isArray(b)?b:[b];let I=P.reduce(o(x),{});C===!1&&(I={});const{prevResolvedValues:j={}}=w,k={...j,...I},B=_=>{A=!0,p.has(_)&&(R=!0,p.delete(_)),w.needsAnimating[_]=!0;const N=e.getValue(_);N&&(N.liveStyle=!1)};for(const _ in k){const N=I[_],M=j[_];if(m.hasOwnProperty(_))continue;let H=!1;Go(N)&&Go(M)?H=!Wc(N,M):H=N!==M,H?N!=null?B(_):p.add(_):N!==void 0&&p.has(_)?B(_):w.protectedKeys[_]=!0}w.prevProp=b,w.prevResolvedValues=I,w.isActive&&(m={...m,...I}),r&&e.blockInitialAnimation&&(A=!1),A&&(!(E&&T)||R)&&f.push(...P.map(_=>({animation:_,options:{type:x}})))}if(p.size){const y={};if(typeof u.initial!="boolean"){const x=jn(e,Array.isArray(u.initial)?u.initial[0]:u.initial);x&&x.transition&&(y.transition=x.transition)}p.forEach(x=>{const w=e.getBaseTarget(x),b=e.getValue(x);b&&(b.liveStyle=!0),y[x]=w??null}),f.push({animation:y})}let g=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(f):Promise.resolve()}function a(l,u){if(n[l].isActive===u)return Promise.resolve();e.variantChildren?.forEach(f=>f.animationState?.setActive(l,u)),n[l].isActive=u;const c=i(l);for(const f in n)n[f].protectedKeys={};return c}return{animateChanges:i,setActive:a,setAnimateFunction:s,getState:()=>n,reset:()=>{n=ta(),r=!0}}}function $g(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Wc(t,e):!1}function Ct(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ta(){return{animate:Ct(!0),whileInView:Ct(),whileHover:Ct(),whileTap:Ct(),whileDrag:Ct(),whileFocus:Ct(),exit:Ct()}}class vt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Ug extends vt{constructor(t){super(t),t.animationState||(t.animationState=Bg(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();kr(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let zg=0;class Wg extends vt{constructor(){super(...arguments),this.id=zg++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>{n(this.id)})}mount(){const{register:t,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const Kg={animation:{Feature:Ug},exit:{Feature:Wg}};function kn(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function zn(e){return{point:{x:e.pageX,y:e.pageY}}}const Hg=e=>t=>Ls(t)&&e(t,zn(t));function Tn(e,t,n,r){return kn(e,t,Hg(n),r)}const Hc=1e-4,Gg=1-Hc,Yg=1+Hc,Gc=.01,Xg=0-Gc,qg=0+Gc;function me(e){return e.max-e.min}function Zg(e,t,n){return Math.abs(e-t)<=n}function na(e,t,n,r=.5){e.origin=r,e.originPoint=J(t.min,t.max,e.origin),e.scale=me(n)/me(t),e.translate=J(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Gg&&e.scale<=Yg||isNaN(e.scale))&&(e.scale=1),(e.translate>=Xg&&e.translate<=qg||isNaN(e.translate))&&(e.translate=0)}function En(e,t,n,r){na(e.x,t.x,n.x,r?r.originX:void 0),na(e.y,t.y,n.y,r?r.originY:void 0)}function ra(e,t,n){e.min=n.min+t.min,e.max=e.min+me(t)}function Qg(e,t,n){ra(e.x,t.x,n.x),ra(e.y,t.y,n.y)}function oa(e,t,n){e.min=t.min-n.min,e.max=e.min+me(t)}function An(e,t,n){oa(e.x,t.x,n.x),oa(e.y,t.y,n.y)}function De(e){return[e("x"),e("y")]}const Yc=({current:e})=>e?e.ownerDocument.defaultView:null,sa=(e,t)=>Math.abs(e-t);function Jg(e,t){const n=sa(e.x,t.x),r=sa(e.y,t.y);return Math.sqrt(n**2+r**2)}class Xc{constructor(t,n,{transformPagePoint:r,contextWindow:o=window,dragSnapToOrigin:s=!1,distanceThreshold:i=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const p=fo(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,v=Jg(p.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!v)return;const{point:g}=p,{timestamp:y}=ue;this.history.push({...g,timestamp:y});const{onStart:x,onMove:w}=this.handlers;m||(x&&x(this.lastMoveEvent,p),this.startEvent=this.lastMoveEvent),w&&w(this.lastMoveEvent,p)},this.handlePointerMove=(p,m)=>{this.lastMoveEvent=p,this.lastMoveEventInfo=uo(m,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(p,m)=>{this.end();const{onEnd:v,onSessionEnd:g,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=fo(p.type==="pointercancel"?this.lastMoveEventInfo:uo(m,this.transformPagePoint),this.history);this.startEvent&&v&&v(p,x),g&&g(p,x)},!Ls(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.distanceThreshold=i,this.contextWindow=o||window;const a=zn(t),l=uo(a,this.transformPagePoint),{point:u}=l,{timestamp:c}=ue;this.history=[{...u,timestamp:c}];const{onSessionStart:f}=n;f&&f(t,fo(l,this.history)),this.removeListeners=Bn(Tn(this.contextWindow,"pointermove",this.handlePointerMove),Tn(this.contextWindow,"pointerup",this.handlePointerUp),Tn(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ht(this.updatePoint)}}function uo(e,t){return t?{point:t(e.point)}:e}function ia(e,t){return{x:e.x-t.x,y:e.y-t.y}}function fo({point:e},t){return{point:e,delta:ia(e,qc(t)),offset:ia(e,ev(t)),velocity:tv(t,.1)}}function ev(e){return e[0]}function qc(e){return e[e.length-1]}function tv(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=qc(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>Ge(t)));)n--;if(!r)return{x:0,y:0};const s=Ye(o.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const i={x:(o.x-r.x)/s,y:(o.y-r.y)/s};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function nv(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?J(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?J(n,e,r.max):Math.min(e,n)),e}function aa(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function rv(e,{top:t,left:n,bottom:r,right:o}){return{x:aa(e.x,n,o),y:aa(e.y,t,r)}}function la(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function ov(e,t){return{x:la(e.x,t.x),y:la(e.y,t.y)}}function sv(e,t){let n=.5;const r=me(e),o=me(t);return o>r?n=Rn(t.min,t.max-r,e.min):r>o&&(n=Rn(e.min,e.max-o,t.min)),nt(0,1,n)}function iv(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const qo=.35;function av(e=qo){return e===!1?e=0:e===!0&&(e=qo),{x:ca(e,"left","right"),y:ca(e,"top","bottom")}}function ca(e,t,n){return{min:ua(e,t),max:ua(e,n)}}function ua(e,t){return typeof e=="number"?e:e[t]||0}const lv=new WeakMap;class cv{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:n=!1,distanceThreshold:r}={}){const{presenceContext:o}=this.visualElement;if(o&&o.isPresent===!1)return;const s=f=>{const{dragSnapToOrigin:p}=this.getProps();p?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(zn(f).point)},i=(f,p)=>{const{drag:m,dragPropagation:v,onDragStart:g}=this.getProps();if(m&&!v&&(this.openDragLock&&this.openDragLock(),this.openDragLock=gm(m),!this.openDragLock))return;this.latestPointerEvent=f,this.latestPanInfo=p,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),De(x=>{let w=this.getAxisMotionValue(x).get()||0;if(Xe.test(w)){const{projection:b}=this.visualElement;if(b&&b.layout){const S=b.layout.layoutBox[x];S&&(w=me(S)*(parseFloat(w)/100))}}this.originPoint[x]=w}),g&&q.postRender(()=>g(f,p)),Yo(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},a=(f,p)=>{this.latestPointerEvent=f,this.latestPanInfo=p;const{dragPropagation:m,dragDirectionLock:v,onDirectionLock:g,onDrag:y}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:x}=p;if(v&&this.currentDirection===null){this.currentDirection=uv(x),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",p.point,x),this.updateAxis("y",p.point,x),this.visualElement.render(),y&&y(f,p)},l=(f,p)=>{this.latestPointerEvent=f,this.latestPanInfo=p,this.stop(f,p),this.latestPointerEvent=null,this.latestPanInfo=null},u=()=>De(f=>this.getAnimationState(f)==="paused"&&this.getAxisMotionValue(f).animation?.play()),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Xc(t,{onSessionStart:s,onStart:i,onMove:a,onSessionEnd:l,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,distanceThreshold:r,contextWindow:Yc(this.visualElement)})}stop(t,n){const r=t||this.latestPointerEvent,o=n||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!o||!r)return;const{velocity:i}=o;this.startAnimation(i);const{onDragEnd:a}=this.getProps();a&&q.postRender(()=>a(r,o))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!nr(t,o,this.currentDirection))return;const s=this.getAxisMotionValue(t);let i=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(i=nv(i,this.constraints[t],this.elastic[t])),s.set(i)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,o=this.constraints;t&&Yt(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=rv(r.layoutBox,t):this.constraints=!1,this.elastic=av(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&De(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=iv(r.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Yt(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const s=ug(r,o.root,this.visualElement.getTransformPagePoint());let i=ov(o.layout.layoutBox,s);if(n){const a=n(ag(i));this.hasMutatedConstraints=!!a,a&&(i=_c(a))}return i}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:s,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=De(c=>{if(!nr(c,n,this.currentDirection))return;let f=l&&l[c]||{};i&&(f={min:0,max:0});const p=o?200:1e6,m=o?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...s,...f};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Yo(this.visualElement,t),r.start(Gs(t,r,0,n,this.visualElement,!1))}stopAnimation(){De(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){De(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){De(n=>{const{drag:r}=this.getProps();if(!nr(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,s=this.getAxisMotionValue(n);if(o&&o.layout){const{min:i,max:a}=o.layout.layoutBox[n];s.set(t[n]-J(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Yt(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};De(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const l=a.get();o[i]=sv({min:l,max:l},this.constraints[i])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),De(i=>{if(!nr(i,t,null))return;const a=this.getAxisMotionValue(i),{min:l,max:u}=this.constraints[i];a.set(J(l,u,o[i]))})}addListeners(){if(!this.visualElement.current)return;lv.set(this.visualElement,this);const t=this.visualElement.current,n=Tn(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Yt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,s=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),q.read(r);const i=kn(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(De(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=l[c].translate,f.set(f.get()+l[c].translate))}),this.visualElement.render())});return()=>{i(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:s=!1,dragElastic:i=qo,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:s,dragElastic:i,dragMomentum:a}}}function nr(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function uv(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class dv extends vt{constructor(t){super(t),this.removeGroupControls=Ne,this.removeListeners=Ne,this.controls=new cv(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ne}unmount(){this.removeGroupControls(),this.removeListeners()}}const da=e=>(t,n)=>{e&&q.postRender(()=>e(t,n))};class fv extends vt{constructor(){super(...arguments),this.removePointerDownListener=Ne}onPointerDown(t){this.session=new Xc(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Yc(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:da(t),onStart:da(n),onMove:r,onEnd:(s,i)=>{delete this.session,o&&q.postRender(()=>o(s,i))}}}mount(){this.removePointerDownListener=Tn(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const fr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function fa(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const xn={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(V.test(e))e=parseFloat(e);else return e;const n=fa(e,t.target.x),r=fa(e,t.target.y);return`${n}% ${r}%`}},hv={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=pt.parse(e);if(o.length>5)return r;const s=pt.createTransformer(e),i=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+i]/=a,o[1+i]/=l;const u=J(a,l,.5);return typeof o[2+i]=="number"&&(o[2+i]/=u),typeof o[3+i]=="number"&&(o[3+i]/=u),s(o)}};let ha=!1;class pv extends d.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:s}=t;Om(mv),s&&(n.group&&n.group.add(s),r&&r.register&&o&&r.register(s),ha&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),fr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:s}=this.props,{projection:i}=r;return i&&(i.isPresent=s,ha=!0,o||t.layoutDependency!==n||n===void 0||t.isPresent!==s?i.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?i.promote():i.relegate()||q.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Is.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Zc(e){const[t,n]=xc(),r=d.useContext(ps);return h.jsx(pv,{...e,layoutGroup:r,switchLayoutGroup:d.useContext(jc),isPresent:t,safeToRemove:n})}const mv={borderRadius:{...xn,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xn,borderTopRightRadius:xn,borderBottomLeftRadius:xn,borderBottomRightRadius:xn,boxShadow:hv};function gv(e,t,n){const r=he(e)?e:en(e);return r.start(Gs("",r,t,n)),r.animation}const vv=(e,t)=>e.depth-t.depth;class yv{constructor(){this.children=[],this.isDirty=!1}add(t){vs(this.children,t),this.isDirty=!0}remove(t){ys(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(vv),this.isDirty=!1,this.children.forEach(t)}}function xv(e,t){const n=xe.now(),r=({timestamp:o})=>{const s=o-n;s>=t&&(ht(r),e(s-t))};return q.setup(r,!0),()=>ht(r)}const Qc=["TopLeft","TopRight","BottomLeft","BottomRight"],bv=Qc.length,pa=e=>typeof e=="string"?parseFloat(e):e,ma=e=>typeof e=="number"||V.test(e);function wv(e,t,n,r,o,s){o?(e.opacity=J(0,n.opacity??1,Sv(r)),e.opacityExit=J(t.opacity??1,0,Cv(r))):s&&(e.opacity=J(t.opacity??1,n.opacity??1,r));for(let i=0;i<bv;i++){const a=`border${Qc[i]}Radius`;let l=ga(t,a),u=ga(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||ma(l)===ma(u)?(e[a]=Math.max(J(pa(l),pa(u),r),0),(Xe.test(u)||Xe.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=J(t.rotate||0,n.rotate||0,r))}function ga(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Sv=Jc(0,.5,Fl),Cv=Jc(.5,.95,Ne);function Jc(e,t,n){return r=>r<e?0:r>t?1:n(Rn(e,t,r))}function va(e,t){e.min=t.min,e.max=t.max}function Pe(e,t){va(e.x,t.x),va(e.y,t.y)}function ya(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function xa(e,t,n,r,o){return e-=t,e=wr(e,1/n,r),o!==void 0&&(e=wr(e,1/o,r)),e}function Tv(e,t=0,n=1,r=.5,o,s=e,i=e){if(Xe.test(t)&&(t=parseFloat(t),t=J(i.min,i.max,t/100)-i.min),typeof t!="number")return;let a=J(s.min,s.max,r);e===s&&(a-=t),e.min=xa(e.min,t,n,a,o),e.max=xa(e.max,t,n,a,o)}function ba(e,t,[n,r,o],s,i){Tv(e,t[n],t[r],t[o],t.scale,s,i)}const Ev=["x","scaleX","originX"],Av=["y","scaleY","originY"];function wa(e,t,n,r){ba(e.x,t,Ev,n?n.x:void 0,r?r.x:void 0),ba(e.y,t,Av,n?n.y:void 0,r?r.y:void 0)}function Sa(e){return e.translate===0&&e.scale===1}function eu(e){return Sa(e.x)&&Sa(e.y)}function Ca(e,t){return e.min===t.min&&e.max===t.max}function Rv(e,t){return Ca(e.x,t.x)&&Ca(e.y,t.y)}function Ta(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function tu(e,t){return Ta(e.x,t.x)&&Ta(e.y,t.y)}function Ea(e){return me(e.x)/me(e.y)}function Aa(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Pv{constructor(){this.members=[]}add(t){vs(this.members,t),t.scheduleRender()}remove(t){if(ys(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const s=this.members[o];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Mv(e,t,n){let r="";const o=e.x.translate/t.x,s=e.y.translate/t.y,i=n?.z||0;if((o||s||i)&&(r=`translate3d(${o}px, ${s}px, ${i}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:f,rotateY:p,skewX:m,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),f&&(r+=`rotateX(${f}deg) `),p&&(r+=`rotateY(${p}deg) `),m&&(r+=`skewX(${m}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const ho=["","X","Y","Z"],Dv=1e3;let Nv=0;function po(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function nu(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Uc(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",q,!(o||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&nu(r)}function ru({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(i={},a=t?.()){this.id=Nv++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(_v),this.nodes.forEach(Vv),this.nodes.forEach(Fv),this.nodes.forEach(Ov)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new yv)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new ws),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const l=this.eventHandlers.get(i);l&&l.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i){if(this.instance)return;this.isSVG=yc(i)&&!Sm(i),this.instance=i;const{layoutId:a,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),e){let c,f=0;const p=()=>this.root.updateBlockedByResize=!1;q.read(()=>{f=window.innerWidth}),e(i,()=>{const m=window.innerWidth;m!==f&&(f=m,this.root.updateBlockedByResize=!0,c&&c(),c=xv(p,250),fr.hasAnimatedSinceResize&&(fr.hasAnimatedSinceResize=!1,this.nodes.forEach(Ma)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:f,hasRelativeLayoutChanged:p,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||u.getDefaultTransition()||Wv,{onLayoutAnimationStart:g,onLayoutAnimationComplete:y}=u.getProps(),x=!this.targetLayout||!tu(this.targetLayout,m),w=!f&&p;if(this.options.layoutRoot||this.resumeFrom||w||f&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const b={..._s(v,"layout"),onPlay:g,onComplete:y};(u.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(c,w)}else f||Ma(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ht(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Bv),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&nu(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ra);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Pa);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Lv),this.nodes.forEach(jv),this.nodes.forEach(kv)):this.nodes.forEach(Pa),this.clearAllSnapshots();const a=xe.now();ue.delta=nt(0,1e3/60,a-ue.timestamp),ue.timestamp=a,ue.isProcessing=!0,no.update.process(ue),no.preRender.process(ue),no.render.process(ue),ue.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Is.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Iv),this.sharedNodes.forEach($v)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!me(this.snapshot.measuredBox.x)&&!me(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a&&this.instance){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!o)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!eu(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;i&&this.instance&&(a||Tt(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return i&&(l=this.removeTransform(l)),Kv(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:i}=this.options;if(!i)return ne();const a=i.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Hv))){const{scroll:u}=this.root;u&&(Xt(a.x,u.offset.x),Xt(a.y,u.offset.y))}return a}removeElementScroll(i){const a=ne();if(Pe(a,i),this.scroll?.wasRoot)return a;for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:f}=u;u!==this.root&&c&&f.layoutScroll&&(c.wasRoot&&Pe(a,i),Xt(a.x,c.offset.x),Xt(a.y,c.offset.y))}return a}applyTransform(i,a=!1){const l=ne();Pe(l,i);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&qt(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Tt(c.latestValues)&&qt(l,c.latestValues)}return Tt(this.latestValues)&&qt(l,this.latestValues),l}removeTransform(i){const a=ne();Pe(a,i);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Tt(u.latestValues))continue;Wo(u.latestValues)&&u.updateSnapshot();const c=ne(),f=u.measurePageBox();Pe(c,f),wa(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Tt(this.latestValues)&&wa(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ue.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(i||l&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:f}=this.options;if(!(!this.layout||!(c||f))){if(this.resolvedRelativeTargetAt=ue.timestamp,!this.targetDelta&&!this.relativeTarget){const p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),An(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),Pe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Qg(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Pe(this.target,this.layout.layoutBox),Ic(this.target,this.targetDelta)):Pe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),An(this.relativeTargetOrigin,this.target,p.target),Pe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wo(this.parent.latestValues)||Oc(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const i=this.getLead(),a=!!this.resumingFrom||this!==i;let l=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===ue.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;Pe(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,p=this.treeScale.y;cg(this.layoutCorrected,this.treeScale,this.path,a),i.layout&&!i.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(i.target=i.layout.layoutBox,i.targetWithTransforms=ne());const{target:m}=i;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ya(this.prevProjectionDelta.x,this.projectionDelta.x),ya(this.prevProjectionDelta.y,this.projectionDelta.y)),En(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==p||!Aa(this.projectionDelta.x,this.prevProjectionDelta.x)||!Aa(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){if(this.options.visualElement?.scheduleRender(),i){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Zt(),this.projectionDelta=Zt(),this.projectionDeltaWithTransform=Zt()}setAnimationOrigin(i,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},f=Zt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=ne(),m=l?l.source:void 0,v=this.layout?this.layout.source:void 0,g=m!==v,y=this.getStack(),x=!y||y.members.length<=1,w=!!(g&&!x&&this.options.crossfade===!0&&!this.path.some(zv));this.animationProgress=0;let b;this.mixTargetDelta=S=>{const C=S/1e3;Da(f.x,i.x,C),Da(f.y,i.y,C),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(An(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Uv(this.relativeTarget,this.relativeTargetOrigin,p,C),b&&Rv(this.relativeTarget,b)&&(this.isProjectionDirty=!1),b||(b=ne()),Pe(b,this.relativeTarget)),g&&(this.animationValues=c,wv(c,u,this.latestValues,C,w,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ht(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{fr.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=en(0)),this.currentAnimation=gv(this.motionValue,[0,1e3],{...i,velocity:0,isSync:!0,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onStop:()=>{},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Dv),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=i;if(!(!a||!l||!u)){if(this!==i&&this.layout&&u&&ou(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ne();const f=me(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+f;const p=me(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+p}Pe(a,l),qt(a,c),En(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Pv),this.sharedNodes.get(i).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){const{layoutId:i}=this.options;return i?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:i}=this.options;return i?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:l}=i;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&po("z",i,u,this.animationValues);for(let c=0;c<ho.length;c++)po(`rotate${ho[c]}`,i,u,this.animationValues),po(`skew${ho[c]}`,i,u,this.animationValues);i.render();for(const c in u)i.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);i.scheduleRender()}applyProjectionStyles(i,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){i.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,i.visibility="",i.opacity="",i.pointerEvents=dr(a?.pointerEvents)||"",i.transform=l?l(this.latestValues,""):"none";return}const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){this.options.layoutId&&(i.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,i.pointerEvents=dr(a?.pointerEvents)||""),this.hasProjected&&!Tt(this.latestValues)&&(i.transform=l?l({},""):"none",this.hasProjected=!1);return}i.visibility="";const c=u.animationValues||u.latestValues;this.applyTransformsToTarget();let f=Mv(this.projectionDeltaWithTransform,this.treeScale,c);l&&(f=l(c,f)),i.transform=f;const{x:p,y:m}=this.projectionDelta;i.transformOrigin=`${p.origin*100}% ${m.origin*100}% 0`,u.animationValues?i.opacity=u===this?c.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:i.opacity=u===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const v in Nn){if(c[v]===void 0)continue;const{correct:g,applyTo:y,isCSSVariable:x}=Nn[v],w=f==="none"?c[v]:g(c[v],u);if(y){const b=y.length;for(let S=0;S<b;S++)i[y[S]]=w}else x?this.options.visualElement.renderState.vars[v]=w:i[v]=w}this.options.layoutId&&(i.pointerEvents=u===this?dr(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>i.currentAnimation?.stop()),this.root.nodes.forEach(Ra),this.root.sharedNodes.clear()}}}function jv(e){e.updateLayout()}function kv(e){const t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=e.layout,{animationType:o}=e.options,s=t.source!==e.layout.source;o==="size"?De(c=>{const f=s?t.measuredBox[c]:t.layoutBox[c],p=me(f);f.min=n[c].min,f.max=f.min+p}):ou(o,t.layoutBox,n)&&De(c=>{const f=s?t.measuredBox[c]:t.layoutBox[c],p=me(n[c]);f.max=f.min+p,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+p)});const i=Zt();En(i,n,t.layoutBox);const a=Zt();s?En(a,e.applyTransform(r,!0),t.measuredBox):En(a,n,t.layoutBox);const l=!eu(i);let u=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:f,layout:p}=c;if(f&&p){const m=ne();An(m,t.layoutBox,f.layoutBox);const v=ne();An(v,n,p.layoutBox),tu(m,v)||(u=!0),c.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=m,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:i,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function _v(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Ov(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Iv(e){e.clearSnapshot()}function Ra(e){e.clearMeasurements()}function Pa(e){e.isLayoutDirty=!1}function Lv(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ma(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Vv(e){e.resolveTargetDelta()}function Fv(e){e.calcProjection()}function Bv(e){e.resetSkewAndRotation()}function $v(e){e.removeLeadSnapshot()}function Da(e,t,n){e.translate=J(t.translate,0,n),e.scale=J(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Na(e,t,n,r){e.min=J(t.min,n.min,r),e.max=J(t.max,n.max,r)}function Uv(e,t,n,r){Na(e.x,t.x,n.x,r),Na(e.y,t.y,n.y,r)}function zv(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Wv={duration:.45,ease:[.4,0,.1,1]},ja=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ka=ja("applewebkit/")&&!ja("chrome/")?Math.round:Ne;function _a(e){e.min=ka(e.min),e.max=ka(e.max)}function Kv(e){_a(e.x),_a(e.y)}function ou(e,t,n){return e==="position"||e==="preserve-aspect"&&!Zg(Ea(t),Ea(n),.2)}function Hv(e){return e!==e.root&&e.scroll?.wasRoot}const Gv=ru({attachResizeListener:(e,t)=>kn(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),mo={current:void 0},su=ru({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!mo.current){const e=new Gv({});e.mount(window),e.setOptions({layoutScroll:!0}),mo.current=e}return mo.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Yv={pan:{Feature:fv},drag:{Feature:dv,ProjectionNode:su,MeasureLayout:Zc}};function Oa(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const o="onHover"+n,s=r[o];s&&q.postRender(()=>s(t,zn(t)))}class Xv extends vt{mount(){const{current:t}=this.node;t&&(this.unmount=vm(t,(n,r)=>(Oa(this.node,r,"Start"),o=>Oa(this.node,o,"End"))))}unmount(){}}class qv extends vt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Bn(kn(this.node.current,"focus",()=>this.onFocus()),kn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Ia(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const o="onTap"+(n==="End"?"":n),s=r[o];s&&q.postRender(()=>s(t,zn(t)))}class Zv extends vt{mount(){const{current:t}=this.node;t&&(this.unmount=wm(t,(n,r)=>(Ia(this.node,r,"Start"),(o,{success:s})=>Ia(this.node,o,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Zo=new WeakMap,go=new WeakMap,Qv=e=>{const t=Zo.get(e.target);t&&t(e)},Jv=e=>{e.forEach(Qv)};function ey({root:e,...t}){const n=e||document;go.has(n)||go.set(n,{});const r=go.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(Jv,{root:e,...t})),r[o]}function ty(e,t,n){const r=ey(t);return Zo.set(e,n),r.observe(e),()=>{Zo.delete(e),r.unobserve(e)}}const ny={some:0,all:1};class ry extends vt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:s}=t,i={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:ny[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),p=u?c:f;p&&p(l)};return ty(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(oy(t,n))&&this.startObserver()}unmount(){}}function oy({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const sy={inView:{Feature:ry},tap:{Feature:Zv},focus:{Feature:qv},hover:{Feature:Xv}},iy={layout:{ProjectionNode:su,MeasureLayout:Zc}},ay={...Kg,...sy,...Yv,...iy},Ys=ig(ay,xg),ly=(...e)=>e.filter(Boolean).join(" "),Sr=(...e)=>Vf(ly(e)),cy=hs("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-black text-white hover:bg-gray-800",destructive:"border border-black text-black hover:bg-gray-100",outline:"border border-gray-400 bg-white hover:bg-gray-100 hover:text-black",secondary:"bg-gray-200 text-black hover:bg-gray-300",ghost:"text-black hover:bg-gray-100 hover:text-black",link:"text-black underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default"}}),Wn=de.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>h.jsx("button",{className:Sr(cy({variant:t,size:n,className:e})),ref:s,...o}));Wn.displayName="Button";const iu=de.forwardRef(({className:e,...t},n)=>h.jsx("textarea",{className:Sr("flex min-h-[80px] w-full rounded-md border border-gray-400 bg-white px-3 py-2 text-base ring-offset-white placeholder:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-black",e),ref:n,...t}));iu.displayName="Textarea";const uy=({size:e=16})=>h.jsx("svg",{height:e,viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:h.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})}),dy=({size:e=16})=>h.jsx("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:h.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})});function fy({onSelectAction:e}){const t=[{title:"受限空间内气体环境满足作业要求下",label:"气体检测分析合格标准是多少",action:"受限空间内气体环境满足作业要求下，气体检测分析合格标准是多少"},{title:"受限空间作业前,应根据受限空间",label:"盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",action:"受限空间作业前,应根据受限空间盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准"},{title:"固定式配电箱及开关箱的底面",label:"离地面垂直高度的高度是多少",action:"固定式配电箱及开关箱的底面离地面垂直高度的高度是多少"},{title:"八大保命原则",label:"是什么",action:"中化集团八大保命原则"}];return h.jsx("div",{"data-testid":"suggested-actions",className:"grid pb-2 grid-cols-1 sm:grid-cols-2 gap-2 w-full",children:h.jsx(bc,{children:t.map((n,r)=>h.jsx(Ys.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*r},className:"block",children:h.jsxs(Wn,{variant:"ghost",onClick:()=>e(n.action),className:`text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start
                       border-gray-300 bg-white hover:bg-gray-100 text-black hover:text-gray-900`,children:[h.jsx("span",{className:"font-medium",children:n.title}),h.jsx("span",{className:"text-gray-500",children:n.label})]})},`suggested-action-${r}`))})})}const hy=d.memo(fy,(e,t)=>!(e.chatId!==t.chatId||e.selectedVisibilityType!==t.selectedVisibilityType)),La=({attachment:e,isUploading:t=!1})=>{const{name:n,url:r,contentType:o}=e;return h.jsxs("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-1",children:[h.jsxs("div",{className:"w-20 h-16 aspect-video bg-gray-200 rounded-md relative flex flex-col items-center justify-center overflow-hidden border border-gray-300",children:[o?.startsWith("image/")&&r?h.jsx("img",{src:r,alt:n??"An image attachment",className:"rounded-md size-full object-cover grayscale"},r):h.jsxs("div",{className:"flex items-center justify-center text-xs text-gray-600 text-center p-1",children:["File: ",n?.split(".").pop()?.toUpperCase()||"Unknown"]}),t&&h.jsx("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-gray-500",children:h.jsx(Do,{className:"size-5"})})]}),h.jsx("div",{className:"text-xs text-gray-600 max-w-20 truncate",children:n})]})};function py({onStop:e}){return h.jsx(Wn,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border border-black text-white",onClick:t=>{t.preventDefault(),e()},"aria-label":"Stop generating",children:h.jsx(uy,{size:14})})}const my=d.memo(py,(e,t)=>e.onStop===t.onStop);function gy({submitForm:e,input:t,uploadQueue:n,attachments:r,canSend:o,isGenerating:s}){const i=n.length>0||!o||s||t.trim().length===0&&r.length===0;return h.jsx(Wn,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit",onClick:a=>{a.preventDefault(),i||e()},disabled:i,"aria-label":"Send message",children:h.jsx(dy,{size:14})})}const vy=d.memo(gy,(e,t)=>!(e.input!==t.input||e.uploadQueue.length!==t.uploadQueue.length||e.attachments.length!==t.attachments.length||e.attachments.length>0&&!Ph(e.attachments,t.attachments)||e.canSend!==t.canSend||e.isGenerating!==t.isGenerating));function yy({chatId:e,messages:t,attachments:n,setAttachments:r,onSendMessage:o,onStopGenerating:s,isGenerating:i,canSend:a,className:l,selectedVisibilityType:u}){const c=d.useRef(null),f=d.useRef(null),[p,m]=d.useState(""),[v,g]=d.useState([]),y=()=>{const R=c.current;R&&(R.style.height="auto",R.style.height=`${R.scrollHeight+2}px`)},x=d.useCallback(()=>{const R=c.current;R&&(R.style.height="auto",R.rows=1,y())},[]);d.useEffect(()=>{c.current&&y()},[p]);const w=R=>{m(R.target.value)},b=async R=>(console.log(`MOCK: Simulating upload for file: ${R.name}`),new Promise(P=>{setTimeout(()=>{try{const j={url:URL.createObjectURL(R),name:R.name,contentType:R.type||"application/octet-stream",size:R.size};console.log(`MOCK: Upload successful for ${R.name}`),P(j)}catch(I){console.error("MOCK: Failed to create object URL for preview:",I),P(void 0)}finally{g(I=>I.filter(j=>j!==R.name))}},700)})),S=d.useCallback(async R=>{const P=Array.from(R.target.files||[]);if(P.length===0)return;g(_=>[..._,...P.map(N=>N.name)]),f.current&&(f.current.value="");const I=25*1024*1024,j=P.filter(_=>_.size<=I),k=P.filter(_=>_.size>I);k.length>0&&(console.warn(`Skipped ${k.length} files larger than ${I/1024/1024}MB.`),g(_=>_.filter(N=>!k.some(M=>M.name===N))));const B=j.map(_=>b(_)),U=(await Promise.all(B)).filter(_=>_!==void 0);r(_=>[..._,...U])},[r,b]),C=d.useCallback(R=>{R.url.startsWith("blob:")&&URL.revokeObjectURL(R.url),r(P=>P.filter(I=>I.url!==R.url||I.name!==R.name)),c.current?.focus()},[r,c]),E=d.useCallback(()=>{if(p.trim().length===0&&n.length===0){console.warn("Please enter a message or add an attachment.");return}o({input:p,attachments:n}),m(""),r([]),n.forEach(R=>{R.url.startsWith("blob:")&&URL.revokeObjectURL(R.url)}),x(),c.current?.focus()},[p,n,o,r,c,x]),T=t.length===0&&n.length===0&&v.length===0,A=i||v.length>0;return h.jsxs("div",{className:Sr("relative w-full flex flex-col gap-4",l),children:[h.jsx(bc,{children:T&&h.jsx(Ys.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.2},children:h.jsx(hy,{onSelectAction:R=>{m(R),requestAnimationFrame(()=>{y(),c.current?.focus()})},chatId:e,selectedVisibilityType:u})},"suggested-actions-container")}),h.jsx("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:f,multiple:!0,onChange:S,tabIndex:-1,disabled:A,accept:"image/*,video/*,audio/*,.pdf"}),(n.length>0||v.length>0)&&h.jsxs("div",{"data-testid":"attachments-preview",className:"flex pt-[10px] flex-row gap-3 overflow-x-auto items-end pb-2 pl-1",children:[n.map(R=>h.jsxs("div",{className:"relative group",children:[h.jsx(La,{attachment:R,isUploading:!1}),h.jsx(Wn,{variant:"destructive",size:"icon",className:"absolute top-[-8px] right-[-8px] h-5 w-5 rounded-full p-0 flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>C(R),"aria-label":`Remove ${R.name}`,children:h.jsx(Dr,{className:"size-3"})})]},R.url||R.name)),v.map((R,P)=>h.jsx(La,{attachment:{url:"",name:R,contentType:"",size:0},isUploading:!0},`upload-${R}-${P}`))]}),h.jsx(iu,{"data-testid":"multimodal-input",ref:c,placeholder:"你想问什么",value:p,onChange:w,className:Sr("min-h-[24px] max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-2xl !text-base pb-10","bg-gray-100 border border-gray-300",l),style:{color:"black"},rows:1,autoFocus:!0,disabled:!a||i||v.length>0,onKeyDown:R=>{R.key==="Enter"&&!R.shiftKey&&!R.nativeEvent.isComposing&&(R.preventDefault(),a&&!i&&v.length===0&&(p.trim().length>0||n.length>0)&&E())}}),h.jsx("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end z-10",children:i?h.jsx(my,{onStop:s}):h.jsx(vy,{submitForm:E,input:p,uploadQueue:v,attachments:n,canSend:a,isGenerating:i})})]})}function xy({onSend:e}={}){const[t,n]=d.useState([]),[r,o]=d.useState(!1),[s]=d.useState("demo-input-only"),[i,a]=d.useState(!0),l=d.useCallback(({input:p,attachments:m})=>{console.log("--- 发送消息 ---"),console.log("输入:",p),console.log("附件:",m),console.log("---------------------------------"),e&&p.trim()&&e(p.trim()),o(!0),setTimeout(()=>{o(!1),n([])},500)},[e]),u=d.useCallback(()=>{console.log("停止按钮被点击（模拟）。"),o(!1)},[]);return h.jsx("div",{className:"w-full max-w-3xl mx-auto p-4",children:h.jsxs("div",{className:"flex flex-col gap-4",children:[h.jsx("div",{className:"flex justify-center",children:h.jsx("button",{onClick:()=>a(!i),className:"p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm",title:i?"收起快捷输入":"展开快捷输入",children:i?h.jsx(Jf,{className:"w-4 h-4 text-gray-600"}):h.jsx(Xf,{className:"w-4 h-4 text-gray-600"})})}),h.jsx("div",{children:h.jsx(yy,{chatId:s,messages:i?[]:[{id:"dummy",content:"",role:"user"}],attachments:t,setAttachments:n,onSendMessage:l,onStopGenerating:u,isGenerating:r,canSend:!0,selectedVisibilityType:"private"})})]})})}const Or=d.forwardRef(({className:e,type:t,...n},r)=>h.jsx("input",{type:t,className:O("flex h-9 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm text-foreground shadow-sm shadow-black/5 transition-shadow placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:cursor-not-allowed disabled:opacity-50",t==="search"&&"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none",t==="file"&&"p-0 pr-3 italic text-muted-foreground/70 file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:border-input file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic file:text-foreground",e),ref:r,...n}));Or.displayName="Input";function by({onUpload:e}={}){const t=d.useRef(null),n=d.useRef(null),[r,o]=d.useState(null),[s,i]=d.useState(null),a=d.useCallback(()=>{n.current?.click()},[]),l=d.useCallback(c=>{const f=c.target.files?.[0];if(f){i(f.name);const p=URL.createObjectURL(f);o(p),t.current=p,e?.(p)}},[e]),u=d.useCallback(()=>{r&&URL.revokeObjectURL(r),o(null),i(null),t.current=null,n.current&&(n.current.value="")},[r]);return d.useEffect(()=>()=>{t.current&&URL.revokeObjectURL(t.current)},[]),{previewUrl:r,fileName:s,fileInputRef:n,handleThumbnailClick:a,handleFileChange:l,handleRemove:u}}function wy({onFileSelect:e}){const{previewUrl:t,fileName:n,fileInputRef:r,handleThumbnailClick:o,handleFileChange:s,handleRemove:i}=by({onUpload:g=>console.log("Uploaded image URL:",g)}),[a,l]=d.useState(!1),u=d.useCallback(g=>{s(g);const y=g.target.files?.[0]||null;e?.(y)},[s,e]),c=d.useCallback(()=>{i(),e?.(null)},[i,e]),f=g=>{g.preventDefault(),g.stopPropagation()},p=g=>{g.preventDefault(),g.stopPropagation(),l(!0)},m=g=>{g.preventDefault(),g.stopPropagation(),l(!1)},v=d.useCallback(g=>{g.preventDefault(),g.stopPropagation(),l(!1);const y=g.dataTransfer.files?.[0];if(y){if(r.current){const x=new DataTransfer;x.items.add(y),r.current.files=x.files;const w=new Event("change",{bubbles:!0});r.current.dispatchEvent(w)}e?.(y)}},[r,e]);return h.jsxs("div",{className:"w-full max-w-md space-y-6 rounded-xl border border-border bg-card p-6 shadow-sm",children:[h.jsxs("div",{className:"space-y-2",children:[h.jsx("h3",{className:"text-lg font-medium",children:"文件上传"}),h.jsx("p",{className:"text-sm text-muted-foreground",children:"支持格式: JPG, PNG, GIF, PDF, DOC, DOCX"})]}),h.jsx(Or,{type:"file",accept:"image/*,.pdf,.doc,.docx",className:"hidden",ref:r,onChange:u}),t?h.jsxs("div",{className:"relative",children:[h.jsxs("div",{className:"group relative h-64 overflow-hidden rounded-lg border",children:[h.jsx("img",{src:t,alt:"Preview",className:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"}),h.jsx("div",{className:"absolute inset-0 bg-black/40 opacity-0 transition-opacity group-hover:opacity-100"}),h.jsxs("div",{className:"absolute inset-0 flex items-center justify-center gap-2 opacity-0 transition-opacity group-hover:opacity-100",children:[h.jsx(Ce,{size:"sm",variant:"secondary",onClick:o,className:"h-9 w-9 p-0",children:h.jsx(Th,{className:"h-4 w-4"})}),h.jsx(Ce,{size:"sm",variant:"destructive",onClick:c,className:"h-9 w-9 p-0",children:h.jsx(Rl,{className:"h-4 w-4"})})]})]}),n&&h.jsxs("div",{className:"mt-2 flex items-center gap-2 text-sm text-muted-foreground",children:[h.jsx("span",{className:"truncate",children:n}),h.jsx("button",{onClick:c,className:"ml-auto rounded-full p-1 hover:bg-muted",children:h.jsx(Dr,{className:"h-4 w-4"})})]})]}):h.jsxs("div",{onClick:o,onDragOver:f,onDragEnter:p,onDragLeave:m,onDrop:v,className:O("flex h-64 cursor-pointer flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/50 transition-colors hover:bg-muted",a&&"border-primary/50 bg-primary/5"),children:[h.jsx("div",{className:"rounded-full bg-background p-3 shadow-sm",children:h.jsx(sh,{className:"h-6 w-6 text-muted-foreground"})}),h.jsxs("div",{className:"text-center",children:[h.jsx("p",{className:"text-sm font-medium",children:"点击选择文件"}),h.jsx("p",{className:"text-xs text-muted-foreground",children:"或拖拽文件到此处"})]})]})]})}function Sy({text:e,selected:t,setSelected:n,discount:r=!1}){return h.jsxs("button",{onClick:()=>n(e),className:O("relative w-fit px-4 py-2 text-sm font-semibold capitalize","text-foreground transition-colors",r&&"flex items-center justify-center gap-2.5"),children:[h.jsx("span",{className:"relative z-10",children:e}),t&&h.jsx(Ys.span,{layoutId:"tab",transition:{type:"spring",duration:.4},className:"absolute inset-0 z-0 rounded-full bg-background shadow-sm"})]})}const Cy=Uf()(zf((e,t)=>({conversations:[],currentConversationId:null,createConversation:(n="新对话")=>{const r=`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,o={id:r,title:n,messages:[],createdAt:new Date,updatedAt:new Date};return e(s=>({conversations:[o,...s.conversations],currentConversationId:r})),r},deleteConversation:n=>{e(r=>{const o=r.conversations.filter(i=>i.id!==n),s=r.currentConversationId===n?o.length>0?o[0].id:null:r.currentConversationId;return{conversations:o,currentConversationId:s}})},updateConversationTitle:(n,r)=>{e(o=>({conversations:o.conversations.map(s=>s.id===n?{...s,title:r,updatedAt:new Date}:s)}))},setCurrentConversation:n=>{e({currentConversationId:n})},addMessage:(n,r,o)=>{const i={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,content:r,role:o,timestamp:new Date};e(a=>({conversations:a.conversations.map(l=>l.id===n?{...l,messages:[...l.messages,i],updatedAt:new Date}:l)}))},getCurrentConversation:()=>{const n=t();return n.conversations.find(r=>r.id===n.currentConversationId)||null},clearAllConversations:()=>{e({conversations:[],currentConversationId:null})}}),{name:"conversation-store",partialize:e=>({conversations:e.conversations,currentConversationId:e.currentConversationId})})),vo=768;function Ty(){const[e,t]=d.useState(void 0);return d.useEffect(()=>{const n=window.matchMedia(`(max-width: ${vo-1}px)`),r=()=>{t(window.innerWidth<vo)};return n.addEventListener("change",r),t(window.innerWidth<vo),()=>n.removeEventListener("change",r)},[]),!!e}function $(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var Ey=yl[" useId ".trim().toString()]||(()=>{}),Ay=0;function Mt(e){const[t,n]=d.useState(Ey());return ft(()=>{n(r=>r??String(Ay++))},[e]),e||(t?`radix-${t}`:"")}var Ry=yl[" useInsertionEffect ".trim().toString()]||ft;function Ir({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,s,i]=Py({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:o;{const c=d.useRef(e!==void 0);d.useEffect(()=>{const f=c.current;f!==a&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=a},[a,r])}const u=d.useCallback(c=>{if(a){const f=My(c)?c(e):c;f!==e&&i.current?.(f)}else s(c)},[a,e,s,i]);return[l,u]}function Py({defaultProp:e,onChange:t}){const[n,r]=d.useState(e),o=d.useRef(n),s=d.useRef(t);return Ry(()=>{s.current=t},[t]),d.useEffect(()=>{o.current!==n&&(s.current?.(n),o.current=n)},[n,o]),[n,r,s]}function My(e){return typeof e=="function"}function Dy(e,t=globalThis?.document){const n=tt(e);d.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ny="DismissableLayer",Qo="dismissableLayer.update",jy="dismissableLayer.pointerDownOutside",ky="dismissableLayer.focusOutside",Va,au=d.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Lr=d.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,u=d.useContext(au),[c,f]=d.useState(null),p=c?.ownerDocument??globalThis?.document,[,m]=d.useState({}),v=ie(t,T=>f(T)),g=Array.from(u.layers),[y]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=g.indexOf(y),w=c?g.indexOf(c):-1,b=u.layersWithOutsidePointerEventsDisabled.size>0,S=w>=x,C=Iy(T=>{const A=T.target,R=[...u.branches].some(P=>P.contains(A));!S||R||(o?.(T),i?.(T),T.defaultPrevented||a?.())},p),E=Ly(T=>{const A=T.target;[...u.branches].some(P=>P.contains(A))||(s?.(T),i?.(T),T.defaultPrevented||a?.())},p);return Dy(T=>{w===u.layers.size-1&&(r?.(T),!T.defaultPrevented&&a&&(T.preventDefault(),a()))},p),d.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Va=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),Fa(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Va)}},[c,p,n,u]),d.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),Fa())},[c,u]),d.useEffect(()=>{const T=()=>m({});return document.addEventListener(Qo,T),()=>document.removeEventListener(Qo,T)},[]),h.jsx(te.div,{...l,ref:v,style:{pointerEvents:b?S?"auto":"none":void 0,...e.style},onFocusCapture:$(e.onFocusCapture,E.onFocusCapture),onBlurCapture:$(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:$(e.onPointerDownCapture,C.onPointerDownCapture)})});Lr.displayName=Ny;var _y="DismissableLayerBranch",Oy=d.forwardRef((e,t)=>{const n=d.useContext(au),r=d.useRef(null),o=ie(t,r);return d.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),h.jsx(te.div,{...e,ref:o})});Oy.displayName=_y;function Iy(e,t=globalThis?.document){const n=tt(e),r=d.useRef(!1),o=d.useRef(()=>{});return d.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){lu(jy,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Ly(e,t=globalThis?.document){const n=tt(e),r=d.useRef(!1);return d.useEffect(()=>{const o=s=>{s.target&&!r.current&&lu(ky,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Fa(){const e=new CustomEvent(Qo);document.dispatchEvent(e)}function lu(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Tl(o,s):o.dispatchEvent(s)}var yo="focusScope.autoFocusOnMount",xo="focusScope.autoFocusOnUnmount",Ba={bubbles:!1,cancelable:!0},Vy="FocusScope",Xs=d.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,l]=d.useState(null),u=tt(o),c=tt(s),f=d.useRef(null),p=ie(t,g=>l(g)),m=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(r){let g=function(b){if(m.paused||!a)return;const S=b.target;a.contains(S)?f.current=S:dt(f.current,{select:!0})},y=function(b){if(m.paused||!a)return;const S=b.relatedTarget;S!==null&&(a.contains(S)||dt(f.current,{select:!0}))},x=function(b){if(document.activeElement===document.body)for(const C of b)C.removedNodes.length>0&&dt(a)};document.addEventListener("focusin",g),document.addEventListener("focusout",y);const w=new MutationObserver(x);return a&&w.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",y),w.disconnect()}}},[r,a,m.paused]),d.useEffect(()=>{if(a){Ua.add(m);const g=document.activeElement;if(!a.contains(g)){const x=new CustomEvent(yo,Ba);a.addEventListener(yo,u),a.dispatchEvent(x),x.defaultPrevented||(Fy(Wy(cu(a)),{select:!0}),document.activeElement===g&&dt(a))}return()=>{a.removeEventListener(yo,u),setTimeout(()=>{const x=new CustomEvent(xo,Ba);a.addEventListener(xo,c),a.dispatchEvent(x),x.defaultPrevented||dt(g??document.body,{select:!0}),a.removeEventListener(xo,c),Ua.remove(m)},0)}}},[a,u,c,m]);const v=d.useCallback(g=>{if(!n&&!r||m.paused)return;const y=g.key==="Tab"&&!g.altKey&&!g.ctrlKey&&!g.metaKey,x=document.activeElement;if(y&&x){const w=g.currentTarget,[b,S]=By(w);b&&S?!g.shiftKey&&x===S?(g.preventDefault(),n&&dt(b,{select:!0})):g.shiftKey&&x===b&&(g.preventDefault(),n&&dt(S,{select:!0})):x===w&&g.preventDefault()}},[n,r,m.paused]);return h.jsx(te.div,{tabIndex:-1,...i,ref:p,onKeyDown:v})});Xs.displayName=Vy;function Fy(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(dt(r,{select:t}),document.activeElement!==n)return}function By(e){const t=cu(e),n=$a(t,e),r=$a(t.reverse(),e);return[n,r]}function cu(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function $a(e,t){for(const n of e)if(!$y(n,{upTo:t}))return n}function $y(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Uy(e){return e instanceof HTMLInputElement&&"select"in e}function dt(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Uy(e)&&t&&e.select()}}var Ua=zy();function zy(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=za(e,t),e.unshift(t)},remove(t){e=za(e,t),e[0]?.resume()}}}function za(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Wy(e){return e.filter(t=>t.tagName!=="A")}var Ky="Portal",qs=d.forwardRef((e,t)=>{const{container:n,...r}=e,[o,s]=d.useState(!1);ft(()=>s(!0),[]);const i=n||o&&globalThis?.document?.body;return i?If.createPortal(h.jsx(te.div,{...r,ref:t}),i):null});qs.displayName=Ky;function Hy(e,t){return d.useReducer((n,r)=>t[n][r]??n,e)}var it=e=>{const{present:t,children:n}=e,r=Gy(t),o=typeof n=="function"?n({present:r.isPresent}):d.Children.only(n),s=ie(r.ref,Yy(o));return typeof n=="function"||r.isPresent?d.cloneElement(o,{ref:s}):null};it.displayName="Presence";function Gy(e){const[t,n]=d.useState(),r=d.useRef(null),o=d.useRef(e),s=d.useRef("none"),i=e?"mounted":"unmounted",[a,l]=Hy(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return d.useEffect(()=>{const u=rr(r.current);s.current=a==="mounted"?u:"none"},[a]),ft(()=>{const u=r.current,c=o.current;if(c!==e){const p=s.current,m=rr(u);e?l("MOUNT"):m==="none"||u?.display==="none"?l("UNMOUNT"):l(c&&p!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),ft(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,f=m=>{const g=rr(r.current).includes(m.animationName);if(m.target===t&&g&&(l("ANIMATION_END"),!o.current)){const y=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=y)})}},p=m=>{m.target===t&&(s.current=rr(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:d.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function rr(e){return e?.animationName||"none"}function Yy(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var bo=0;function uu(){d.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Wa()),document.body.insertAdjacentElement("beforeend",e[1]??Wa()),bo++,()=>{bo===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),bo--}},[])}function Wa(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ke=function(){return Ke=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Ke.apply(this,arguments)};function du(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Xy(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var hr="right-scroll-bar-position",pr="width-before-scroll-bar",qy="with-scroll-bars-hidden",Zy="--removed-body-scroll-bar-size";function wo(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Qy(e,t){var n=d.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Jy=typeof window<"u"?d.useLayoutEffect:d.useEffect,Ka=new WeakMap;function ex(e,t){var n=Qy(null,function(r){return e.forEach(function(o){return wo(o,r)})});return Jy(function(){var r=Ka.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||wo(a,null)}),s.forEach(function(a){o.has(a)||wo(a,i)})}Ka.set(n,e)},[e]),n}function tx(e){return e}function nx(e,t){t===void 0&&(t=tx);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var c=i;i=[],c.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(c){i.push(c),u()},filter:function(c){return i=i.filter(c),n}}}};return o}function rx(e){e===void 0&&(e={});var t=nx(null);return t.options=Ke({async:!0,ssr:!1},e),t}var fu=function(e){var t=e.sideCar,n=du(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return d.createElement(r,Ke({},n))};fu.isSideCarExport=!0;function ox(e,t){return e.useMedium(t),fu}var hu=rx(),So=function(){},Vr=d.forwardRef(function(e,t){var n=d.useRef(null),r=d.useState({onScrollCapture:So,onWheelCapture:So,onTouchMoveCapture:So}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,c=e.enabled,f=e.shards,p=e.sideCar,m=e.noRelative,v=e.noIsolation,g=e.inert,y=e.allowPinchZoom,x=e.as,w=x===void 0?"div":x,b=e.gapMode,S=du(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=p,E=ex([n,t]),T=Ke(Ke({},S),o);return d.createElement(d.Fragment,null,c&&d.createElement(C,{sideCar:hu,removeScrollBar:u,shards:f,noRelative:m,noIsolation:v,inert:g,setCallbacks:s,allowPinchZoom:!!y,lockRef:n,gapMode:b}),i?d.cloneElement(d.Children.only(a),Ke(Ke({},T),{ref:E})):d.createElement(w,Ke({},T,{className:l,ref:E}),a))});Vr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Vr.classNames={fullWidth:pr,zeroRight:hr};var sx=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function ix(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=sx();return t&&e.setAttribute("nonce",t),e}function ax(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function lx(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var cx=function(){var e=0,t=null;return{add:function(n){e==0&&(t=ix())&&(ax(t,n),lx(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ux=function(){var e=cx();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},pu=function(){var e=ux(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},dx={left:0,top:0,right:0,gap:0},Co=function(e){return parseInt(e||"",10)||0},fx=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Co(n),Co(r),Co(o)]},hx=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return dx;var t=fx(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},px=pu(),Qt="data-scroll-locked",mx=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(qy,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Qt,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(hr,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(pr,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(hr," .").concat(hr,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(pr," .").concat(pr,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Qt,`] {
    `).concat(Zy,": ").concat(a,`px;
  }
`)},Ha=function(){var e=parseInt(document.body.getAttribute(Qt)||"0",10);return isFinite(e)?e:0},gx=function(){d.useEffect(function(){return document.body.setAttribute(Qt,(Ha()+1).toString()),function(){var e=Ha()-1;e<=0?document.body.removeAttribute(Qt):document.body.setAttribute(Qt,e.toString())}},[])},vx=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;gx();var s=d.useMemo(function(){return hx(o)},[o]);return d.createElement(px,{styles:mx(s,!t,o,n?"":"!important")})},Jo=!1;if(typeof window<"u")try{var or=Object.defineProperty({},"passive",{get:function(){return Jo=!0,!0}});window.addEventListener("test",or,or),window.removeEventListener("test",or,or)}catch{Jo=!1}var zt=Jo?{passive:!1}:!1,yx=function(e){return e.tagName==="TEXTAREA"},mu=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!yx(e)&&n[t]==="visible")},xx=function(e){return mu(e,"overflowY")},bx=function(e){return mu(e,"overflowX")},Ga=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=gu(e,r);if(o){var s=vu(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},wx=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Sx=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},gu=function(e,t){return e==="v"?xx(t):bx(t)},vu=function(e,t){return e==="v"?wx(t):Sx(t)},Cx=function(e,t){return e==="h"&&t==="rtl"?-1:1},Tx=function(e,t,n,r,o){var s=Cx(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,l=t.contains(a),u=!1,c=i>0,f=0,p=0;do{if(!a)break;var m=vu(e,a),v=m[0],g=m[1],y=m[2],x=g-y-s*v;(v||x)&&gu(e,a)&&(f+=x,p+=v);var w=a.parentNode;a=w&&w.nodeType===Node.DOCUMENT_FRAGMENT_NODE?w.host:w}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(c&&Math.abs(f)<1||!c&&Math.abs(p)<1)&&(u=!0),u},sr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ya=function(e){return[e.deltaX,e.deltaY]},Xa=function(e){return e&&"current"in e?e.current:e},Ex=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Ax=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Rx=0,Wt=[];function Px(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState(Rx++)[0],s=d.useState(pu)[0],i=d.useRef(e);d.useEffect(function(){i.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=Xy([e.lockRef.current],(e.shards||[]).map(Xa),!0).filter(Boolean);return g.forEach(function(y){return y.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=d.useCallback(function(g,y){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!i.current.allowPinchZoom;var x=sr(g),w=n.current,b="deltaX"in g?g.deltaX:w[0]-x[0],S="deltaY"in g?g.deltaY:w[1]-x[1],C,E=g.target,T=Math.abs(b)>Math.abs(S)?"h":"v";if("touches"in g&&T==="h"&&E.type==="range")return!1;var A=Ga(T,E);if(!A)return!0;if(A?C=T:(C=T==="v"?"h":"v",A=Ga(T,E)),!A)return!1;if(!r.current&&"changedTouches"in g&&(b||S)&&(r.current=C),!C)return!0;var R=r.current||C;return Tx(R,y,g,R==="h"?b:S)},[]),l=d.useCallback(function(g){var y=g;if(!(!Wt.length||Wt[Wt.length-1]!==s)){var x="deltaY"in y?Ya(y):sr(y),w=t.current.filter(function(C){return C.name===y.type&&(C.target===y.target||y.target===C.shadowParent)&&Ex(C.delta,x)})[0];if(w&&w.should){y.cancelable&&y.preventDefault();return}if(!w){var b=(i.current.shards||[]).map(Xa).filter(Boolean).filter(function(C){return C.contains(y.target)}),S=b.length>0?a(y,b[0]):!i.current.noIsolation;S&&y.cancelable&&y.preventDefault()}}},[]),u=d.useCallback(function(g,y,x,w){var b={name:g,delta:y,target:x,should:w,shadowParent:Mx(x)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(S){return S!==b})},1)},[]),c=d.useCallback(function(g){n.current=sr(g),r.current=void 0},[]),f=d.useCallback(function(g){u(g.type,Ya(g),g.target,a(g,e.lockRef.current))},[]),p=d.useCallback(function(g){u(g.type,sr(g),g.target,a(g,e.lockRef.current))},[]);d.useEffect(function(){return Wt.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,zt),document.addEventListener("touchmove",l,zt),document.addEventListener("touchstart",c,zt),function(){Wt=Wt.filter(function(g){return g!==s}),document.removeEventListener("wheel",l,zt),document.removeEventListener("touchmove",l,zt),document.removeEventListener("touchstart",c,zt)}},[]);var m=e.removeScrollBar,v=e.inert;return d.createElement(d.Fragment,null,v?d.createElement(s,{styles:Ax(o)}):null,m?d.createElement(vx,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Mx(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Dx=ox(hu,Px);var Zs=d.forwardRef(function(e,t){return d.createElement(Vr,Ke({},e,{ref:t,sideCar:Dx}))});Zs.classNames=Vr.classNames;var Nx=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Kt=new WeakMap,ir=new WeakMap,ar={},To=0,yu=function(e){return e&&(e.host||yu(e.parentNode))},jx=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=yu(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},kx=function(e,t,n,r){var o=jx(t,Array.isArray(e)?e:[e]);ar[n]||(ar[n]=new WeakMap);var s=ar[n],i=[],a=new Set,l=new Set(o),u=function(f){!f||a.has(f)||(a.add(f),u(f.parentNode))};o.forEach(u);var c=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(p){if(a.has(p))c(p);else try{var m=p.getAttribute(r),v=m!==null&&m!=="false",g=(Kt.get(p)||0)+1,y=(s.get(p)||0)+1;Kt.set(p,g),s.set(p,y),i.push(p),g===1&&v&&ir.set(p,!0),y===1&&p.setAttribute(n,"true"),v||p.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",p,x)}})};return c(t),a.clear(),To++,function(){i.forEach(function(f){var p=Kt.get(f)-1,m=s.get(f)-1;Kt.set(f,p),s.set(f,m),p||(ir.has(f)||f.removeAttribute(r),ir.delete(f)),m||f.removeAttribute(n)}),To--,To||(Kt=new WeakMap,Kt=new WeakMap,ir=new WeakMap,ar={})}},xu=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Nx(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),kx(r,o,n,"aria-hidden")):function(){return null}},Fr="Dialog",[bu,bC]=jt(Fr),[_x,Ve]=bu(Fr),wu=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,a=d.useRef(null),l=d.useRef(null),[u,c]=Ir({prop:r,defaultProp:o??!1,onChange:s,caller:Fr});return h.jsx(_x,{scope:t,triggerRef:a,contentRef:l,contentId:Mt(),titleId:Mt(),descriptionId:Mt(),open:u,onOpenChange:c,onOpenToggle:d.useCallback(()=>c(f=>!f),[c]),modal:i,children:n})};wu.displayName=Fr;var Su="DialogTrigger",Ox=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Su,n),s=ie(t,o.triggerRef);return h.jsx(te.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ei(o.open),...r,ref:s,onClick:$(e.onClick,o.onOpenToggle)})});Ox.displayName=Su;var Qs="DialogPortal",[Ix,Cu]=bu(Qs,{forceMount:void 0}),Tu=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Ve(Qs,t);return h.jsx(Ix,{scope:t,forceMount:n,children:d.Children.map(r,i=>h.jsx(it,{present:n||s.open,children:h.jsx(qs,{asChild:!0,container:o,children:i})}))})};Tu.displayName=Qs;var Cr="DialogOverlay",Eu=d.forwardRef((e,t)=>{const n=Cu(Cr,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ve(Cr,e.__scopeDialog);return s.modal?h.jsx(it,{present:r||s.open,children:h.jsx(Vx,{...o,ref:t})}):null});Eu.displayName=Cr;var Lx=gr("DialogOverlay.RemoveScroll"),Vx=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Cr,n);return h.jsx(Zs,{as:Lx,allowPinchZoom:!0,shards:[o.contentRef],children:h.jsx(te.div,{"data-state":ei(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Dt="DialogContent",Au=d.forwardRef((e,t)=>{const n=Cu(Dt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ve(Dt,e.__scopeDialog);return h.jsx(it,{present:r||s.open,children:s.modal?h.jsx(Fx,{...o,ref:t}):h.jsx(Bx,{...o,ref:t})})});Au.displayName=Dt;var Fx=d.forwardRef((e,t)=>{const n=Ve(Dt,e.__scopeDialog),r=d.useRef(null),o=ie(t,n.contentRef,r);return d.useEffect(()=>{const s=r.current;if(s)return xu(s)},[]),h.jsx(Ru,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:$(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:$(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:$(e.onFocusOutside,s=>s.preventDefault())})}),Bx=d.forwardRef((e,t)=>{const n=Ve(Dt,e.__scopeDialog),r=d.useRef(!1),o=d.useRef(!1);return h.jsx(Ru,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||n.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;n.triggerRef.current?.contains(i)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Ru=d.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,a=Ve(Dt,n),l=d.useRef(null),u=ie(t,l);return uu(),h.jsxs(h.Fragment,{children:[h.jsx(Xs,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:h.jsx(Lr,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":ei(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),h.jsxs(h.Fragment,{children:[h.jsx($x,{titleId:a.titleId}),h.jsx(zx,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Js="DialogTitle",Pu=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Js,n);return h.jsx(te.h2,{id:o.titleId,...r,ref:t})});Pu.displayName=Js;var Mu="DialogDescription",Du=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Mu,n);return h.jsx(te.p,{id:o.descriptionId,...r,ref:t})});Du.displayName=Mu;var Nu="DialogClose",ju=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Nu,n);return h.jsx(te.button,{type:"button",...r,ref:t,onClick:$(e.onClick,()=>o.onOpenChange(!1))})});ju.displayName=Nu;function ei(e){return e?"open":"closed"}var ku="DialogTitleWarning",[wC,_u]=Ff(ku,{contentName:Dt,titleName:Js,docsSlug:"dialog"}),$x=({titleId:e})=>{const t=_u(ku),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return d.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Ux="DialogDescriptionWarning",zx=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${_u(Ux).contentName}}.`;return d.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Wx=wu,Kx=Tu,Ou=Eu,Iu=Au,Lu=Pu,Vu=Du,Hx=ju;const Gx=Wx,Yx=Kx,Fu=d.forwardRef(({className:e,...t},n)=>h.jsx(Ou,{className:O("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));Fu.displayName=Ou.displayName;const Xx=hs("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Bu=d.forwardRef(({side:e="right",className:t,children:n,...r},o)=>h.jsxs(Yx,{children:[h.jsx(Fu,{}),h.jsxs(Iu,{ref:o,className:O(Xx({side:e}),t),...r,children:[n,h.jsxs(Hx,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[h.jsx(Dr,{className:"h-4 w-4"}),h.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Bu.displayName=Iu.displayName;const qx=d.forwardRef(({className:e,...t},n)=>h.jsx(Lu,{ref:n,className:O("text-lg font-semibold text-foreground",e),...t}));qx.displayName=Lu.displayName;const Zx=d.forwardRef(({className:e,...t},n)=>h.jsx(Vu,{ref:n,className:O("text-sm text-muted-foreground",e),...t}));Zx.displayName=Vu.displayName;function qa({className:e,...t}){return h.jsx("div",{className:O("animate-pulse rounded-md bg-muted",e),...t})}const Qx=["top","right","bottom","left"],mt=Math.min,Te=Math.max,Tr=Math.round,lr=Math.floor,qe=e=>({x:e,y:e}),Jx={left:"right",right:"left",bottom:"top",top:"bottom"},eb={start:"end",end:"start"};function es(e,t,n){return Te(e,mt(t,n))}function ot(e,t){return typeof e=="function"?e(t):e}function st(e){return e.split("-")[0]}function ln(e){return e.split("-")[1]}function ti(e){return e==="x"?"y":"x"}function ni(e){return e==="y"?"height":"width"}const tb=new Set(["top","bottom"]);function He(e){return tb.has(st(e))?"y":"x"}function ri(e){return ti(He(e))}function nb(e,t,n){n===void 0&&(n=!1);const r=ln(e),o=ri(e),s=ni(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Er(i)),[i,Er(i)]}function rb(e){const t=Er(e);return[ts(e),t,ts(t)]}function ts(e){return e.replace(/start|end/g,t=>eb[t])}const Za=["left","right"],Qa=["right","left"],ob=["top","bottom"],sb=["bottom","top"];function ib(e,t,n){switch(e){case"top":case"bottom":return n?t?Qa:Za:t?Za:Qa;case"left":case"right":return t?ob:sb;default:return[]}}function ab(e,t,n,r){const o=ln(e);let s=ib(st(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(ts)))),s}function Er(e){return e.replace(/left|right|bottom|top/g,t=>Jx[t])}function lb(e){return{top:0,right:0,bottom:0,left:0,...e}}function $u(e){return typeof e!="number"?lb(e):{top:e,right:e,bottom:e,left:e}}function Ar(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Ja(e,t,n){let{reference:r,floating:o}=e;const s=He(t),i=ri(t),a=ni(i),l=st(t),u=s==="y",c=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,p=r[a]/2-o[a]/2;let m;switch(l){case"top":m={x:c,y:r.y-o.height};break;case"bottom":m={x:c,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:f};break;case"left":m={x:r.x-o.width,y:f};break;default:m={x:r.x,y:r.y}}switch(ln(t)){case"start":m[i]-=p*(n&&u?-1:1);break;case"end":m[i]+=p*(n&&u?-1:1);break}return m}const cb=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=Ja(u,r,l),p=r,m={},v=0;for(let g=0;g<a.length;g++){const{name:y,fn:x}=a[g],{x:w,y:b,data:S,reset:C}=await x({x:c,y:f,initialPlacement:r,placement:p,strategy:o,middlewareData:m,rects:u,platform:i,elements:{reference:e,floating:t}});c=w??c,f=b??f,m={...m,[y]:{...m[y],...S}},C&&v<=50&&(v++,typeof C=="object"&&(C.placement&&(p=C.placement),C.rects&&(u=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:c,y:f}=Ja(u,p,l)),g=-1)}return{x:c,y:f,placement:p,strategy:o,middlewareData:m}};async function _n(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=ot(t,e),v=$u(m),y=a[p?f==="floating"?"reference":"floating":f],x=Ar(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(y)))==null||n?y:y.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),w=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),S=await(s.isElement==null?void 0:s.isElement(b))?await(s.getScale==null?void 0:s.getScale(b))||{x:1,y:1}:{x:1,y:1},C=Ar(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:b,strategy:l}):w);return{top:(x.top-C.top+v.top)/S.y,bottom:(C.bottom-x.bottom+v.bottom)/S.y,left:(x.left-C.left+v.left)/S.x,right:(C.right-x.right+v.right)/S.x}}const ub=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:c=0}=ot(e,t)||{};if(u==null)return{};const f=$u(c),p={x:n,y:r},m=ri(o),v=ni(m),g=await i.getDimensions(u),y=m==="y",x=y?"top":"left",w=y?"bottom":"right",b=y?"clientHeight":"clientWidth",S=s.reference[v]+s.reference[m]-p[m]-s.floating[v],C=p[m]-s.reference[m],E=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let T=E?E[b]:0;(!T||!await(i.isElement==null?void 0:i.isElement(E)))&&(T=a.floating[b]||s.floating[v]);const A=S/2-C/2,R=T/2-g[v]/2-1,P=mt(f[x],R),I=mt(f[w],R),j=P,k=T-g[v]-I,B=T/2-g[v]/2+A,Y=es(j,B,k),U=!l.arrow&&ln(o)!=null&&B!==Y&&s.reference[v]/2-(B<j?P:I)-g[v]/2<0,_=U?B<j?B-j:B-k:0;return{[m]:p[m]+_,data:{[m]:Y,centerOffset:B-Y-_,...U&&{alignmentOffset:_}},reset:U}}}),db=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:c=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:g=!0,...y}=ot(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=st(o),w=He(a),b=st(a)===a,S=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=p||(b||!g?[Er(a)]:rb(a)),E=v!=="none";!p&&E&&C.push(...ab(a,g,v,S));const T=[a,...C],A=await _n(t,y),R=[];let P=((r=s.flip)==null?void 0:r.overflows)||[];if(c&&R.push(A[x]),f){const B=nb(o,i,S);R.push(A[B[0]],A[B[1]])}if(P=[...P,{placement:o,overflows:R}],!R.every(B=>B<=0)){var I,j;const B=(((I=s.flip)==null?void 0:I.index)||0)+1,Y=T[B];if(Y&&(!(f==="alignment"?w!==He(Y):!1)||P.every(N=>N.overflows[0]>0&&He(N.placement)===w)))return{data:{index:B,overflows:P},reset:{placement:Y}};let U=(j=P.filter(_=>_.overflows[0]<=0).sort((_,N)=>_.overflows[1]-N.overflows[1])[0])==null?void 0:j.placement;if(!U)switch(m){case"bestFit":{var k;const _=(k=P.filter(N=>{if(E){const M=He(N.placement);return M===w||M==="y"}return!0}).map(N=>[N.placement,N.overflows.filter(M=>M>0).reduce((M,H)=>M+H,0)]).sort((N,M)=>N[1]-M[1])[0])==null?void 0:k[0];_&&(U=_);break}case"initialPlacement":U=a;break}if(o!==U)return{reset:{placement:U}}}return{}}}};function el(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function tl(e){return Qx.some(t=>e[t]>=0)}const fb=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ot(e,t);switch(r){case"referenceHidden":{const s=await _n(t,{...o,elementContext:"reference"}),i=el(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:tl(i)}}}case"escaped":{const s=await _n(t,{...o,altBoundary:!0}),i=el(s,n.floating);return{data:{escapedOffsets:i,escaped:tl(i)}}}default:return{}}}}},Uu=new Set(["left","top"]);async function hb(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=st(n),a=ln(n),l=He(n)==="y",u=Uu.has(i)?-1:1,c=s&&l?-1:1,f=ot(t,e);let{mainAxis:p,crossAxis:m,alignmentAxis:v}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof v=="number"&&(m=a==="end"?v*-1:v),l?{x:m*c,y:p*u}:{x:p*u,y:m*c}}const pb=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await hb(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},mb=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:y=>{let{x,y:w}=y;return{x,y:w}}},...l}=ot(e,t),u={x:n,y:r},c=await _n(t,l),f=He(st(o)),p=ti(f);let m=u[p],v=u[f];if(s){const y=p==="y"?"top":"left",x=p==="y"?"bottom":"right",w=m+c[y],b=m-c[x];m=es(w,m,b)}if(i){const y=f==="y"?"top":"left",x=f==="y"?"bottom":"right",w=v+c[y],b=v-c[x];v=es(w,v,b)}const g=a.fn({...t,[p]:m,[f]:v});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[p]:s,[f]:i}}}}}},gb=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=ot(e,t),c={x:n,y:r},f=He(o),p=ti(f);let m=c[p],v=c[f];const g=ot(a,t),y=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){const b=p==="y"?"height":"width",S=s.reference[p]-s.floating[b]+y.mainAxis,C=s.reference[p]+s.reference[b]-y.mainAxis;m<S?m=S:m>C&&(m=C)}if(u){var x,w;const b=p==="y"?"width":"height",S=Uu.has(st(o)),C=s.reference[f]-s.floating[b]+(S&&((x=i.offset)==null?void 0:x[f])||0)+(S?0:y.crossAxis),E=s.reference[f]+s.reference[b]+(S?0:((w=i.offset)==null?void 0:w[f])||0)-(S?y.crossAxis:0);v<C?v=C:v>E&&(v=E)}return{[p]:m,[f]:v}}}},vb=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=ot(e,t),c=await _n(t,u),f=st(o),p=ln(o),m=He(o)==="y",{width:v,height:g}=s.floating;let y,x;f==="top"||f==="bottom"?(y=f,x=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=f,y=p==="end"?"top":"bottom");const w=g-c.top-c.bottom,b=v-c.left-c.right,S=mt(g-c[y],w),C=mt(v-c[x],b),E=!t.middlewareData.shift;let T=S,A=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(A=b),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(T=w),E&&!p){const P=Te(c.left,0),I=Te(c.right,0),j=Te(c.top,0),k=Te(c.bottom,0);m?A=v-2*(P!==0||I!==0?P+I:Te(c.left,c.right)):T=g-2*(j!==0||k!==0?j+k:Te(c.top,c.bottom))}await l({...t,availableWidth:A,availableHeight:T});const R=await i.getDimensions(a.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}};function Br(){return typeof window<"u"}function cn(e){return zu(e)?(e.nodeName||"").toLowerCase():"#document"}function Ee(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Qe(e){var t;return(t=(zu(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function zu(e){return Br()?e instanceof Node||e instanceof Ee(e).Node:!1}function Ie(e){return Br()?e instanceof Element||e instanceof Ee(e).Element:!1}function Ze(e){return Br()?e instanceof HTMLElement||e instanceof Ee(e).HTMLElement:!1}function nl(e){return!Br()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ee(e).ShadowRoot}const yb=new Set(["inline","contents"]);function Kn(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Le(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!yb.has(o)}const xb=new Set(["table","td","th"]);function bb(e){return xb.has(cn(e))}const wb=[":popover-open",":modal"];function $r(e){return wb.some(t=>{try{return e.matches(t)}catch{return!1}})}const Sb=["transform","translate","scale","rotate","perspective"],Cb=["transform","translate","scale","rotate","perspective","filter"],Tb=["paint","layout","strict","content"];function oi(e){const t=si(),n=Ie(e)?Le(e):e;return Sb.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Cb.some(r=>(n.willChange||"").includes(r))||Tb.some(r=>(n.contain||"").includes(r))}function Eb(e){let t=gt(e);for(;Ze(t)&&!nn(t);){if(oi(t))return t;if($r(t))return null;t=gt(t)}return null}function si(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Ab=new Set(["html","body","#document"]);function nn(e){return Ab.has(cn(e))}function Le(e){return Ee(e).getComputedStyle(e)}function Ur(e){return Ie(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function gt(e){if(cn(e)==="html")return e;const t=e.assignedSlot||e.parentNode||nl(e)&&e.host||Qe(e);return nl(t)?t.host:t}function Wu(e){const t=gt(e);return nn(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ze(t)&&Kn(t)?t:Wu(t)}function On(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Wu(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=Ee(o);if(s){const a=ns(i);return t.concat(i,i.visualViewport||[],Kn(o)?o:[],a&&n?On(a):[])}return t.concat(o,On(o,[],n))}function ns(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ku(e){const t=Le(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ze(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=Tr(n)!==s||Tr(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function ii(e){return Ie(e)?e:e.contextElement}function Jt(e){const t=ii(e);if(!Ze(t))return qe(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Ku(t);let i=(s?Tr(n.width):n.width)/r,a=(s?Tr(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Rb=qe(0);function Hu(e){const t=Ee(e);return!si()||!t.visualViewport?Rb:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Pb(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ee(e)?!1:t}function Nt(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=ii(e);let i=qe(1);t&&(r?Ie(r)&&(i=Jt(r)):i=Jt(e));const a=Pb(s,n,r)?Hu(s):qe(0);let l=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,c=o.width/i.x,f=o.height/i.y;if(s){const p=Ee(s),m=r&&Ie(r)?Ee(r):r;let v=p,g=ns(v);for(;g&&r&&m!==v;){const y=Jt(g),x=g.getBoundingClientRect(),w=Le(g),b=x.left+(g.clientLeft+parseFloat(w.paddingLeft))*y.x,S=x.top+(g.clientTop+parseFloat(w.paddingTop))*y.y;l*=y.x,u*=y.y,c*=y.x,f*=y.y,l+=b,u+=S,v=Ee(g),g=ns(v)}}return Ar({width:c,height:f,x:l,y:u})}function ai(e,t){const n=Ur(e).scrollLeft;return t?t.left+n:Nt(Qe(e)).left+n}function Gu(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:ai(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function Mb(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Qe(r),a=t?$r(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},u=qe(1);const c=qe(0),f=Ze(r);if((f||!f&&!s)&&((cn(r)!=="body"||Kn(i))&&(l=Ur(r)),Ze(r))){const m=Nt(r);u=Jt(r),c.x=m.x+r.clientLeft,c.y=m.y+r.clientTop}const p=i&&!f&&!s?Gu(i,l,!0):qe(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-l.scrollTop*u.y+c.y+p.y}}function Db(e){return Array.from(e.getClientRects())}function Nb(e){const t=Qe(e),n=Ur(e),r=e.ownerDocument.body,o=Te(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=Te(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+ai(e);const a=-n.scrollTop;return Le(r).direction==="rtl"&&(i+=Te(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function jb(e,t){const n=Ee(e),r=Qe(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const u=si();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}const kb=new Set(["absolute","fixed"]);function _b(e,t){const n=Nt(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Ze(e)?Jt(e):qe(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:i,height:a,x:l,y:u}}function rl(e,t,n){let r;if(t==="viewport")r=jb(e,n);else if(t==="document")r=Nb(Qe(e));else if(Ie(t))r=_b(t,n);else{const o=Hu(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Ar(r)}function Yu(e,t){const n=gt(e);return n===t||!Ie(n)||nn(n)?!1:Le(n).position==="fixed"||Yu(n,t)}function Ob(e,t){const n=t.get(e);if(n)return n;let r=On(e,[],!1).filter(a=>Ie(a)&&cn(a)!=="body"),o=null;const s=Le(e).position==="fixed";let i=s?gt(e):e;for(;Ie(i)&&!nn(i);){const a=Le(i),l=oi(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&kb.has(o.position)||Kn(i)&&!l&&Yu(e,i))?r=r.filter(c=>c!==i):o=a,i=gt(i)}return t.set(e,r),r}function Ib(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?$r(t)?[]:Ob(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((u,c)=>{const f=rl(t,c,o);return u.top=Te(f.top,u.top),u.right=mt(f.right,u.right),u.bottom=mt(f.bottom,u.bottom),u.left=Te(f.left,u.left),u},rl(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Lb(e){const{width:t,height:n}=Ku(e);return{width:t,height:n}}function Vb(e,t,n){const r=Ze(t),o=Qe(t),s=n==="fixed",i=Nt(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=qe(0);function u(){l.x=ai(o)}if(r||!r&&!s)if((cn(t)!=="body"||Kn(o))&&(a=Ur(t)),r){const m=Nt(t,!0,s,t);l.x=m.x+t.clientLeft,l.y=m.y+t.clientTop}else o&&u();s&&!r&&o&&u();const c=o&&!r&&!s?Gu(o,a):qe(0),f=i.left+a.scrollLeft-l.x-c.x,p=i.top+a.scrollTop-l.y-c.y;return{x:f,y:p,width:i.width,height:i.height}}function Eo(e){return Le(e).position==="static"}function ol(e,t){if(!Ze(e)||Le(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Qe(e)===n&&(n=n.ownerDocument.body),n}function Xu(e,t){const n=Ee(e);if($r(e))return n;if(!Ze(e)){let o=gt(e);for(;o&&!nn(o);){if(Ie(o)&&!Eo(o))return o;o=gt(o)}return n}let r=ol(e,t);for(;r&&bb(r)&&Eo(r);)r=ol(r,t);return r&&nn(r)&&Eo(r)&&!oi(r)?n:r||Eb(e)||n}const Fb=async function(e){const t=this.getOffsetParent||Xu,n=this.getDimensions,r=await n(e.floating);return{reference:Vb(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Bb(e){return Le(e).direction==="rtl"}const $b={convertOffsetParentRelativeRectToViewportRelativeRect:Mb,getDocumentElement:Qe,getClippingRect:Ib,getOffsetParent:Xu,getElementRects:Fb,getClientRects:Db,getDimensions:Lb,getScale:Jt,isElement:Ie,isRTL:Bb};function qu(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ub(e,t){let n=null,r;const o=Qe(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:c,top:f,width:p,height:m}=u;if(a||t(),!p||!m)return;const v=lr(f),g=lr(o.clientWidth-(c+p)),y=lr(o.clientHeight-(f+m)),x=lr(c),b={rootMargin:-v+"px "+-g+"px "+-y+"px "+-x+"px",threshold:Te(0,mt(1,l))||1};let S=!0;function C(E){const T=E[0].intersectionRatio;if(T!==l){if(!S)return i();T?i(!1,T):r=setTimeout(()=>{i(!1,1e-7)},1e3)}T===1&&!qu(u,e.getBoundingClientRect())&&i(),S=!1}try{n=new IntersectionObserver(C,{...b,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,b)}n.observe(e)}return i(!0),s}function Zu(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=ii(e),c=o||s?[...u?On(u):[],...On(t)]:[];c.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const f=u&&a?Ub(u,n):null;let p=-1,m=null;i&&(m=new ResizeObserver(x=>{let[w]=x;w&&w.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var b;(b=m)==null||b.observe(t)})),n()}),u&&!l&&m.observe(u),m.observe(t));let v,g=l?Nt(e):null;l&&y();function y(){const x=Nt(e);g&&!qu(g,x)&&n(),g=x,v=requestAnimationFrame(y)}return n(),()=>{var x;c.forEach(w=>{o&&w.removeEventListener("scroll",n),s&&w.removeEventListener("resize",n)}),f?.(),(x=m)==null||x.disconnect(),m=null,l&&cancelAnimationFrame(v)}}const Qu=pb,Ju=mb,ed=db,zb=vb,Wb=fb,rs=ub,Kb=gb,os=(e,t,n)=>{const r=new Map,o={platform:$b,...n},s={...o.platform,_c:r};return cb(e,t,{...o,platform:s})};var Hb=typeof document<"u",Gb=function(){},mr=Hb?d.useLayoutEffect:Gb;function Rr(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Rr(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Rr(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function td(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function sl(e,t){const n=td(e);return Math.round(t*n)/n}function Ao(e){const t=d.useRef(e);return mr(()=>{t.current=e}),t}function Yb(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[c,f]=d.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=d.useState(r);Rr(p,r)||m(r);const[v,g]=d.useState(null),[y,x]=d.useState(null),w=d.useCallback(N=>{N!==E.current&&(E.current=N,g(N))},[]),b=d.useCallback(N=>{N!==T.current&&(T.current=N,x(N))},[]),S=s||v,C=i||y,E=d.useRef(null),T=d.useRef(null),A=d.useRef(c),R=l!=null,P=Ao(l),I=Ao(o),j=Ao(u),k=d.useCallback(()=>{if(!E.current||!T.current)return;const N={placement:t,strategy:n,middleware:p};I.current&&(N.platform=I.current),os(E.current,T.current,N).then(M=>{const H={...M,isPositioned:j.current!==!1};B.current&&!Rr(A.current,H)&&(A.current=H,Lf.flushSync(()=>{f(H)}))})},[p,t,n,I,j]);mr(()=>{u===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,f(N=>({...N,isPositioned:!1})))},[u]);const B=d.useRef(!1);mr(()=>(B.current=!0,()=>{B.current=!1}),[]),mr(()=>{if(S&&(E.current=S),C&&(T.current=C),S&&C){if(P.current)return P.current(S,C,k);k()}},[S,C,k,P,R]);const Y=d.useMemo(()=>({reference:E,floating:T,setReference:w,setFloating:b}),[w,b]),U=d.useMemo(()=>({reference:S,floating:C}),[S,C]),_=d.useMemo(()=>{const N={position:n,left:0,top:0};if(!U.floating)return N;const M=sl(U.floating,c.x),H=sl(U.floating,c.y);return a?{...N,transform:"translate("+M+"px, "+H+"px)",...td(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:M,top:H}},[n,a,U.floating,c.x,c.y]);return d.useMemo(()=>({...c,update:k,refs:Y,elements:U,floatingStyles:_}),[c,k,Y,U,_])}const Xb=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?rs({element:r.current,padding:o}).fn(n):{}:r?rs({element:r,padding:o}).fn(n):{}}}},qb=(e,t)=>({...Qu(e),options:[e,t]}),Zb=(e,t)=>({...Ju(e),options:[e,t]}),Qb=(e,t)=>({...Kb(e),options:[e,t]}),Jb=(e,t)=>({...ed(e),options:[e,t]}),ew=(e,t)=>({...zb(e),options:[e,t]}),tw=(e,t)=>({...Wb(e),options:[e,t]}),nw=(e,t)=>({...Xb(e),options:[e,t]});var rw="Arrow",nd=d.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return h.jsx(te.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:h.jsx("polygon",{points:"0,0 30,0 15,10"})})});nd.displayName=rw;var ow=nd;function sw(e){const[t,n]=d.useState(void 0);return ft(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var li="Popper",[rd,zr]=jt(li),[iw,od]=rd(li),sd=e=>{const{__scopePopper:t,children:n}=e,[r,o]=d.useState(null);return h.jsx(iw,{scope:t,anchor:r,onAnchorChange:o,children:n})};sd.displayName=li;var id="PopperAnchor",ad=d.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=od(id,n),i=d.useRef(null),a=ie(t,i);return d.useEffect(()=>{s.onAnchorChange(r?.current||i.current)}),r?null:h.jsx(te.div,{...o,ref:a})});ad.displayName=id;var ci="PopperContent",[aw,lw]=rd(ci),ld=d.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:c=0,sticky:f="partial",hideWhenDetached:p=!1,updatePositionStrategy:m="optimized",onPlaced:v,...g}=e,y=od(ci,n),[x,w]=d.useState(null),b=ie(t,Z=>w(Z)),[S,C]=d.useState(null),E=sw(S),T=E?.width??0,A=E?.height??0,R=r+(s!=="center"?"-"+s:""),P=typeof c=="number"?c:{top:0,right:0,bottom:0,left:0,...c},I=Array.isArray(u)?u:[u],j=I.length>0,k={padding:P,boundary:I.filter(uw),altBoundary:j},{refs:B,floatingStyles:Y,placement:U,isPositioned:_,middlewareData:N}=Yb({strategy:"fixed",placement:R,whileElementsMounted:(...Z)=>Zu(...Z,{animationFrame:m==="always"}),elements:{reference:y.anchor},middleware:[qb({mainAxis:o+A,alignmentAxis:i}),l&&Zb({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?Qb():void 0,...k}),l&&Jb({...k}),ew({...k,apply:({elements:Z,rects:ke,availableWidth:ce,availableHeight:at})=>{const{width:be,height:$e}=ke.reference,Ue=Z.floating.style;Ue.setProperty("--radix-popper-available-width",`${ce}px`),Ue.setProperty("--radix-popper-available-height",`${at}px`),Ue.setProperty("--radix-popper-anchor-width",`${be}px`),Ue.setProperty("--radix-popper-anchor-height",`${$e}px`)}}),S&&nw({element:S,padding:a}),dw({arrowWidth:T,arrowHeight:A}),p&&tw({strategy:"referenceHidden",...k})]}),[M,H]=dd(U),K=tt(v);ft(()=>{_&&K?.()},[_,K]);const ve=N.arrow?.x,Fe=N.arrow?.y,Be=N.arrow?.centerOffset!==0,[Je,ye]=d.useState();return ft(()=>{x&&ye(window.getComputedStyle(x).zIndex)},[x]),h.jsx("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:_?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Je,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:h.jsx(aw,{scope:n,placedSide:M,onArrowChange:C,arrowX:ve,arrowY:Fe,shouldHideArrow:Be,children:h.jsx(te.div,{"data-side":M,"data-align":H,...g,ref:b,style:{...g.style,animation:_?void 0:"none"}})})})});ld.displayName=ci;var cd="PopperArrow",cw={top:"bottom",right:"left",bottom:"top",left:"right"},ud=d.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=lw(cd,r),i=cw[s.placedSide];return h.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:h.jsx(ow,{...o,ref:n,style:{...o.style,display:"block"}})})});ud.displayName=cd;function uw(e){return e!==null}var dw=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=dd(n),f={start:"0%",center:"50%",end:"100%"}[c],p=(o.arrow?.x??0)+a/2,m=(o.arrow?.y??0)+l/2;let v="",g="";return u==="bottom"?(v=i?f:`${p}px`,g=`${-l}px`):u==="top"?(v=i?f:`${p}px`,g=`${r.floating.height+l}px`):u==="right"?(v=`${-l}px`,g=i?f:`${m}px`):u==="left"&&(v=`${r.floating.width+l}px`,g=i?f:`${m}px`),{data:{x:v,y:g}}}});function dd(e){const[t,n="center"]=e.split("-");return[t,n]}var fd=sd,hd=ad,pd=ld,md=ud,fw=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),hw="VisuallyHidden",gd=d.forwardRef((e,t)=>h.jsx(te.span,{...e,ref:t,style:{...fw,...e.style}}));gd.displayName=hw;var pw=gd,[Wr,SC]=jt("Tooltip",[zr]),Kr=zr(),vd="TooltipProvider",mw=700,ss="tooltip.open",[gw,ui]=Wr(vd),yd=e=>{const{__scopeTooltip:t,delayDuration:n=mw,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,i=d.useRef(!0),a=d.useRef(!1),l=d.useRef(0);return d.useEffect(()=>{const u=l.current;return()=>window.clearTimeout(u)},[]),h.jsx(gw,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:d.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:d.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:d.useCallback(u=>{a.current=u},[]),disableHoverableContent:o,children:s})};yd.displayName=vd;var In="Tooltip",[vw,Hr]=Wr(In),xd=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:s,disableHoverableContent:i,delayDuration:a}=e,l=ui(In,e.__scopeTooltip),u=Kr(t),[c,f]=d.useState(null),p=Mt(),m=d.useRef(0),v=i??l.disableHoverableContent,g=a??l.delayDuration,y=d.useRef(!1),[x,w]=Ir({prop:r,defaultProp:o??!1,onChange:T=>{T?(l.onOpen(),document.dispatchEvent(new CustomEvent(ss))):l.onClose(),s?.(T)},caller:In}),b=d.useMemo(()=>x?y.current?"delayed-open":"instant-open":"closed",[x]),S=d.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y.current=!1,w(!0)},[w]),C=d.useCallback(()=>{window.clearTimeout(m.current),m.current=0,w(!1)},[w]),E=d.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{y.current=!0,w(!0),m.current=0},g)},[g,w]);return d.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),h.jsx(fd,{...u,children:h.jsx(vw,{scope:t,contentId:p,open:x,stateAttribute:b,trigger:c,onTriggerChange:f,onTriggerEnter:d.useCallback(()=>{l.isOpenDelayedRef.current?E():S()},[l.isOpenDelayedRef,E,S]),onTriggerLeave:d.useCallback(()=>{v?C():(window.clearTimeout(m.current),m.current=0)},[C,v]),onOpen:S,onClose:C,disableHoverableContent:v,children:n})})};xd.displayName=In;var is="TooltipTrigger",bd=d.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Hr(is,n),s=ui(is,n),i=Kr(n),a=d.useRef(null),l=ie(t,a,o.onTriggerChange),u=d.useRef(!1),c=d.useRef(!1),f=d.useCallback(()=>u.current=!1,[]);return d.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),h.jsx(hd,{asChild:!0,...i,children:h.jsx(te.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:$(e.onPointerMove,p=>{p.pointerType!=="touch"&&!c.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),c.current=!0)}),onPointerLeave:$(e.onPointerLeave,()=>{o.onTriggerLeave(),c.current=!1}),onPointerDown:$(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:$(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:$(e.onBlur,o.onClose),onClick:$(e.onClick,o.onClose)})})});bd.displayName=is;var yw="TooltipPortal",[CC,xw]=Wr(yw,{forceMount:void 0}),rn="TooltipContent",wd=d.forwardRef((e,t)=>{const n=xw(rn,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=Hr(rn,e.__scopeTooltip);return h.jsx(it,{present:r||i.open,children:i.disableHoverableContent?h.jsx(Sd,{side:o,...s,ref:t}):h.jsx(bw,{side:o,...s,ref:t})})}),bw=d.forwardRef((e,t)=>{const n=Hr(rn,e.__scopeTooltip),r=ui(rn,e.__scopeTooltip),o=d.useRef(null),s=ie(t,o),[i,a]=d.useState(null),{trigger:l,onClose:u}=n,c=o.current,{onPointerInTransitChange:f}=r,p=d.useCallback(()=>{a(null),f(!1)},[f]),m=d.useCallback((v,g)=>{const y=v.currentTarget,x={x:v.clientX,y:v.clientY},w=Ew(x,y.getBoundingClientRect()),b=Aw(x,w),S=Rw(g.getBoundingClientRect()),C=Mw([...b,...S]);a(C),f(!0)},[f]);return d.useEffect(()=>()=>p(),[p]),d.useEffect(()=>{if(l&&c){const v=y=>m(y,c),g=y=>m(y,l);return l.addEventListener("pointerleave",v),c.addEventListener("pointerleave",g),()=>{l.removeEventListener("pointerleave",v),c.removeEventListener("pointerleave",g)}}},[l,c,m,p]),d.useEffect(()=>{if(i){const v=g=>{const y=g.target,x={x:g.clientX,y:g.clientY},w=l?.contains(y)||c?.contains(y),b=!Pw(x,i);w?p():b&&(p(),u())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,c,i,u,p]),h.jsx(Sd,{...e,ref:s})}),[ww,Sw]=Wr(In,{isInside:!1}),Cw=$f("TooltipContent"),Sd=d.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=Hr(rn,n),u=Kr(n),{onClose:c}=l;return d.useEffect(()=>(document.addEventListener(ss,c),()=>document.removeEventListener(ss,c)),[c]),d.useEffect(()=>{if(l.trigger){const f=p=>{p.target?.contains(l.trigger)&&c()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[l.trigger,c]),h.jsx(Lr,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:c,children:h.jsxs(pd,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[h.jsx(Cw,{children:r}),h.jsx(ww,{scope:n,isInside:!0,children:h.jsx(pw,{id:l.contentId,role:"tooltip",children:o||r})})]})})});wd.displayName=rn;var Cd="TooltipArrow",Tw=d.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Kr(n);return Sw(Cd,n).isInside?null:h.jsx(md,{...o,...r,ref:t})});Tw.displayName=Cd;function Ew(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Aw(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Rw(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Pw(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s],l=t[i],u=a.x,c=a.y,f=l.x,p=l.y;c>r!=p>r&&n<(f-u)*(r-c)/(p-c)+u&&(o=!o)}return o}function Mw(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Dw(t)}function Dw(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Nw=yd,jw=xd,kw=bd,Td=wd;const _w=Nw,Ow=jw,Iw=kw,Ed=d.forwardRef(({className:e,sideOffset:t=4,...n},r)=>h.jsx(Td,{ref:r,sideOffset:t,className:O("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));Ed.displayName=Td.displayName;const Lw="sidebar_state",Vw=3600*24*7,Fw="20rem",Bw="22rem",$w="3rem",Uw="b",Ad=d.createContext(null);function Gr(){const e=d.useContext(Ad);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const Rd=d.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:s,...i},a)=>{const l=Ty(),[u,c]=d.useState(!1),[f,p]=d.useState(e),m=t??f,v=d.useCallback(w=>{const b=typeof w=="function"?w(m):w;n?n(b):p(b),document.cookie=`${Lw}=${b}; path=/; max-age=${Vw}`},[n,m]),g=d.useCallback(()=>l?c(w=>!w):v(w=>!w),[l,v,c]);d.useEffect(()=>{const w=b=>{b.key===Uw&&(b.metaKey||b.ctrlKey)&&(b.preventDefault(),g())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[g]);const y=m?"expanded":"collapsed",x=d.useMemo(()=>({state:y,open:m,setOpen:v,isMobile:l,openMobile:u,setOpenMobile:c,toggleSidebar:g}),[y,m,v,l,u,c,g]);return h.jsx(Ad.Provider,{value:x,children:h.jsx(_w,{delayDuration:0,children:h.jsx("div",{style:{"--sidebar-width":Fw,"--sidebar-width-icon":$w,...o},className:O("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:a,...i,children:s})})})});Rd.displayName="SidebarProvider";const zw=d.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...s},i)=>{const{isMobile:a,state:l,openMobile:u,setOpenMobile:c}=Gr();return n==="none"?h.jsx("div",{className:O("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...s,children:o}):a?h.jsx(Gx,{open:u,onOpenChange:c,...s,children:h.jsx(Bu,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":Bw},side:e,children:h.jsx("div",{className:"flex h-full w-full flex-col",children:o})})}):h.jsxs("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":l,"data-collapsible":l==="collapsed"?n:"","data-variant":t,"data-side":e,children:[h.jsx("div",{className:O("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),h.jsx("div",{className:O("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...s,children:h.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:o})})]})});zw.displayName="Sidebar";const Ww=d.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:o}=Gr();return h.jsxs(Ce,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:O("h-7 w-7",e),onClick:s=>{t?.(s),o()},...n,children:[h.jsx(vh,{}),h.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});Ww.displayName="SidebarTrigger";const Kw=d.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=Gr();return h.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:O("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});Kw.displayName="SidebarRail";const Hw=d.forwardRef(({className:e,...t},n)=>h.jsx("main",{ref:n,className:O("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));Hw.displayName="SidebarInset";const Gw=d.forwardRef(({className:e,...t},n)=>h.jsx(Or,{ref:n,"data-sidebar":"input",className:O("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));Gw.displayName="SidebarInput";const Yw=d.forwardRef(({className:e,...t},n)=>h.jsx("div",{ref:n,"data-sidebar":"header",className:O("flex flex-col gap-2 p-2",e),...t}));Yw.displayName="SidebarHeader";const Xw=d.forwardRef(({className:e,...t},n)=>h.jsx("div",{ref:n,"data-sidebar":"footer",className:O("flex flex-col gap-2 p-2",e),...t}));Xw.displayName="SidebarFooter";const qw=d.forwardRef(({className:e,...t},n)=>h.jsx(Kf,{ref:n,"data-sidebar":"separator",className:O("mx-2 w-auto bg-sidebar-border",e),...t}));qw.displayName="SidebarSeparator";const Zw=d.forwardRef(({className:e,...t},n)=>h.jsx("div",{ref:n,"data-sidebar":"content",className:O("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));Zw.displayName="SidebarContent";const Qw=d.forwardRef(({className:e,...t},n)=>h.jsx("div",{ref:n,"data-sidebar":"group",className:O("relative flex w-full min-w-0 flex-col p-2",e),...t}));Qw.displayName="SidebarGroup";const Jw=d.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Fn:"div";return h.jsx(o,{ref:r,"data-sidebar":"group-label",className:O("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});Jw.displayName="SidebarGroupLabel";const e0=d.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Fn:"button";return h.jsx(o,{ref:r,"data-sidebar":"group-action",className:O("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});e0.displayName="SidebarGroupAction";const t0=d.forwardRef(({className:e,...t},n)=>h.jsx("div",{ref:n,"data-sidebar":"group-content",className:O("w-full text-sm",e),...t}));t0.displayName="SidebarGroupContent";const n0=d.forwardRef(({className:e,...t},n)=>h.jsx("ul",{ref:n,"data-sidebar":"menu",className:O("flex w-full min-w-0 flex-col gap-1",e),...t}));n0.displayName="SidebarMenu";const r0=d.forwardRef(({className:e,...t},n)=>h.jsx("li",{ref:n,"data-sidebar":"menu-item",className:O("group/menu-item relative",e),...t}));r0.displayName="SidebarMenuItem";const o0=hs("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!size-8"}},defaultVariants:{variant:"default",size:"default"}}),s0=d.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:s,...i},a)=>{const l=e?Fn:"button",{isMobile:u,state:c}=Gr(),f=h.jsx(l,{ref:a,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:O(o0({variant:n,size:r}),s),...i});return o?(typeof o=="string"&&(o={children:o}),h.jsxs(Ow,{children:[h.jsx(Iw,{asChild:!0,children:f}),h.jsx(Ed,{side:"right",align:"center",hidden:c!=="collapsed"||u,...o})]})):f});s0.displayName="SidebarMenuButton";const i0=d.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},o)=>{const s=t?Fn:"button";return h.jsx(s,{ref:o,"data-sidebar":"menu-action",className:O("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});i0.displayName="SidebarMenuAction";const a0=d.forwardRef(({className:e,...t},n)=>h.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:O("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));a0.displayName="SidebarMenuBadge";const l0=d.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const o=d.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return h.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:O("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&h.jsx(qa,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),h.jsx(qa,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})});l0.displayName="SidebarMenuSkeleton";const c0=d.forwardRef(({className:e,...t},n)=>h.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:O("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));c0.displayName="SidebarMenuSub";const u0=d.forwardRef(({...e},t)=>h.jsx("li",{ref:t,...e}));u0.displayName="SidebarMenuSubItem";const d0=d.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...o},s)=>{const i=e?Fn:"a";return h.jsx(i,{ref:s,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:O("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...o})});d0.displayName="SidebarMenuSubButton";function f0({children:e,isCollapsed:t,onToggle:n}){const{conversations:r,currentConversationId:o,createConversation:s,deleteConversation:i,updateConversationTitle:a,setCurrentConversation:l}=Cy(),[u,c]=d.useState(null),[f,p]=d.useState(""),[m,v]=d.useState(!1),[g,y]=d.useState(!1),[x,w]=d.useState(!1),b=t!==void 0?t:m,S=()=>{s()},C=(j,k)=>{k.stopPropagation(),i(j)},E=(j,k,B)=>{B.stopPropagation(),c(j),p(k)},T=j=>{f.trim()&&a(j,f.trim()),c(null),p("")},A=()=>{c(null),p("")},R=()=>{b?(n?n():v(!1),setTimeout(()=>{y(!1)},150)):(y(!0),setTimeout(()=>{n?n():v(!0)},150))};d.useEffect(()=>{b?y(!0):setTimeout(()=>{y(!1)},300)},[b]),d.useEffect(()=>{const j=()=>{w(window.innerWidth<768)};return j(),window.addEventListener("resize",j),()=>window.removeEventListener("resize",j)},[]),d.useEffect(()=>{x&&!m&&t===void 0&&v(!0)},[x]),d.useEffect(()=>{const j=k=>{k.key==="b"&&(k.ctrlKey||k.metaKey)&&(k.preventDefault(),R())};return window.addEventListener("keydown",j),()=>window.removeEventListener("keydown",j)},[b]);const P=()=>{x&&!b&&R()},I=j=>{const k=j instanceof Date?j:new Date(j);if(isNaN(k.getTime()))return"无效日期";const Y=new Date().getTime()-k.getTime(),U=Math.floor(Y/(1e3*60*60*24));return U===0?"今天":U===1?"昨天":U<7?`${U}天前`:k.toLocaleDateString("zh-CN")};return h.jsxs(Rd,{defaultOpen:!0,children:[x&&!b&&h.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:P}),x&&h.jsx(Ce,{variant:"outline",size:"sm",className:"fixed top-4 left-4 z-50 md:hidden h-10 w-10 p-0",onClick:R,"aria-label":b?"展开侧边栏":"收起侧边栏",children:h.jsx(ch,{className:"h-4 w-4"})}),h.jsxs("div",{className:O("flex min-h-screen w-full",x?"relative":""),children:[h.jsx("div",{className:O("bg-sidebar border-r border-border transition-all duration-300 ease-in-out z-50",!x&&"flex-shrink-0",!x&&(b?"w-0 overflow-hidden":"w-80"),x&&"fixed top-0 left-0 h-full",x&&(b?"-translate-x-full":"translate-x-0 w-80")),children:h.jsxs("div",{className:"h-full flex flex-col",children:[h.jsx("div",{className:O("border-b px-4 h-16 py-3 transition-opacity duration-150 ease-in-out",g?"opacity-0":"opacity-100"),children:h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsx("h2",{className:"text-lg font-semibold",children:"对话历史"}),h.jsx(Ce,{onClick:S,size:"sm",variant:"outline",className:"h-8 w-8 p-0",children:h.jsx(wh,{className:"h-4 w-4"})})]})}),h.jsx("div",{className:O("flex-1 overflow-auto transition-opacity duration-150 ease-in-out",g?"opacity-0":"opacity-100"),children:h.jsx("div",{className:"p-2",children:r.length===0?h.jsx("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:"暂无对话历史"}):h.jsx("div",{className:"space-y-1",children:r.map(j=>h.jsxs("div",{className:O("group relative flex items-center gap-3 rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",o===j.id&&"bg-accent"),onClick:()=>l(j.id),children:[h.jsx(dh,{className:"h-4 w-4 flex-shrink-0"}),h.jsx("div",{className:"flex-1 min-w-0",children:u===j.id?h.jsxs("div",{className:"flex items-center gap-1",onClick:k=>k.stopPropagation(),children:[h.jsx(Or,{value:f,onChange:k=>p(k.target.value),className:"h-6 text-xs",onKeyDown:k=>{k.key==="Enter"?T(j.id):k.key==="Escape"&&A()},autoFocus:!0}),h.jsx(Ce,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>T(j.id),children:h.jsx(Al,{className:"h-3 w-3"})}),h.jsx(Ce,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:A,children:h.jsx(Dr,{className:"h-3 w-3"})})]}):h.jsxs(h.Fragment,{children:[h.jsx("div",{className:"font-medium truncate",children:j.title}),h.jsxs("div",{className:"text-xs text-muted-foreground",children:[I(j.updatedAt)," •"," ",j.messages.length," 条消息"]})]})}),u!==j.id&&h.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[h.jsx(Ce,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:k=>E(j.id,j.title,k),children:h.jsx(xh,{className:"h-3 w-3"})}),h.jsx(Ce,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0 text-destructive hover:text-destructive",onClick:k=>C(j.id,k),children:h.jsx(Rl,{className:"h-3 w-3"})})]})]},j.id))})})}),h.jsx("div",{className:O("border-t px-4 py-3 transition-opacity duration-150 ease-in-out",g?"opacity-0":"opacity-100"),children:h.jsxs("div",{className:"text-xs text-muted-foreground",children:["共 ",r.length," 个对话"]})})]})}),h.jsx("main",{className:O("flex-1 flex flex-col min-w-0 w-full relative",x&&"w-full"),children:e})]})]})}var Ro={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var il;function h0(){return il||(il=1,function(e){(function(){var t={}.hasOwnProperty;function n(){for(var s="",i=0;i<arguments.length;i++){var a=arguments[i];a&&(s=o(s,r(a)))}return s}function r(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return n.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var i="";for(var a in s)t.call(s,a)&&s[a]&&(i=o(i,a));return i}function o(s,i){return i?s?s+" "+i:s+i:s}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(Ro)),Ro.exports}var p0=h0();const as=vl(p0);var al={};const m0="react-tooltip-core-styles",g0="react-tooltip-base-styles",ll={core:!1,base:!1};function cl({css:e,id:t=g0,type:n="base",ref:r}){var o,s;if(!e||typeof document>"u"||ll[n]||n==="core"&&typeof process<"u"&&(!((o=process==null?void 0:al)===null||o===void 0)&&o.REACT_TOOLTIP_DISABLE_CORE_STYLES)||n!=="base"&&typeof process<"u"&&(!((s=process==null?void 0:al)===null||s===void 0)&&s.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;n==="core"&&(t=m0),r||(r={});const{insertAt:i}=r;if(document.getElementById(t))return;const a=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.id=t,l.type="text/css",i==="top"&&a.firstChild?a.insertBefore(l,a.firstChild):a.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e)),ll[n]=!0}const ul=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:n=null,place:r="top",offset:o=10,strategy:s="absolute",middlewares:i=[Qu(Number(o)),ed({fallbackAxisSideDirection:"start"}),Ju({padding:5})],border:a,arrowSize:l=8})=>{if(!e)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};if(t===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};const u=i;return n?(u.push(rs({element:n,padding:5})),os(e,t,{placement:r,strategy:s,middleware:u}).then(({x:c,y:f,placement:p,middlewareData:m})=>{var v,g;const y={left:`${c}px`,top:`${f}px`,border:a},{x,y:w}=(v=m.arrow)!==null&&v!==void 0?v:{x:0,y:0},b=(g={top:"bottom",right:"left",bottom:"top",left:"right"}[p.split("-")[0]])!==null&&g!==void 0?g:"bottom",S=a&&{borderBottom:a,borderRight:a};let C=0;if(a){const E=`${a}`.match(/(\d+)px/);C=E?.[1]?Number(E[1]):1}return{tooltipStyles:y,tooltipArrowStyles:{left:x!=null?`${x}px`:"",top:w!=null?`${w}px`:"",right:"",bottom:"",...S,[b]:`-${l/2+C}px`},place:p}})):os(e,t,{placement:"bottom",strategy:s,middleware:u}).then(({x:c,y:f,placement:p})=>({tooltipStyles:{left:`${c}px`,top:`${f}px`},tooltipArrowStyles:{},place:p}))},dl=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),fl=(e,t,n)=>{let r=null;const o=function(...s){const i=()=>{r=null};!r&&(e.apply(this,s),r=setTimeout(i,t))};return o.cancel=()=>{r&&(clearTimeout(r),r=null)},o},hl=e=>e!==null&&!Array.isArray(e)&&typeof e=="object",ls=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((o,s)=>ls(o,t[s]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!hl(e)||!hl(t))return e===t;const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(o=>ls(e[o],t[o]))},v0=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;const t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(n=>{const r=t.getPropertyValue(n);return r==="auto"||r==="scroll"})},pl=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(v0(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},y0=typeof window<"u"?d.useLayoutEffect:d.useEffect,Me=e=>{e.current&&(clearTimeout(e.current),e.current=null)},x0="DEFAULT_TOOLTIP_ID",b0={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},w0=d.createContext({getTooltipData:()=>b0});function Pd(e=x0){return d.useContext(w0).getTooltipData(e)}var Ht={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},Po={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const S0=({forwardRef:e,id:t,className:n,classNameArrow:r,variant:o="dark",anchorId:s,anchorSelect:i,place:a="top",offset:l=10,events:u=["hover"],openOnClick:c=!1,positionStrategy:f="absolute",middlewares:p,wrapper:m,delayShow:v=0,delayHide:g=0,float:y=!1,hidden:x=!1,noArrow:w=!1,clickable:b=!1,closeOnEsc:S=!1,closeOnScroll:C=!1,closeOnResize:E=!1,openEvents:T,closeEvents:A,globalCloseEvents:R,imperativeModeOnly:P,style:I,position:j,afterShow:k,afterHide:B,disableTooltip:Y,content:U,contentWrapperRef:_,isOpen:N,defaultIsOpen:M=!1,setIsOpen:H,activeAnchor:K,setActiveAnchor:ve,border:Fe,opacity:Be,arrowColor:Je,arrowSize:ye=8,role:Z="tooltip"})=>{var ke;const ce=d.useRef(null),at=d.useRef(null),be=d.useRef(null),$e=d.useRef(null),Ue=d.useRef(null),[lt,Zr]=d.useState({tooltipStyles:{},tooltipArrowStyles:{},place:a}),[we,Xn]=d.useState(!1),[yt,xt]=d.useState(!1),[Q,un]=d.useState(null),dn=d.useRef(!1),fn=d.useRef(null),{anchorRefs:hn,setActiveAnchor:qn}=Pd(t),Ot=d.useRef(!1),[ct,pn]=d.useState([]),bt=d.useRef(!1),It=c||u.includes("click"),Qr=It||T?.click||T?.dblclick||T?.mousedown,Lt=T?{...T}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!T&&It&&Object.assign(Lt,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const wt=A?{...A}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!A&&It&&Object.assign(wt,{mouseleave:!1,blur:!1,mouseout:!1});const _e=R?{...R}:{escape:S||!1,scroll:C||!1,resize:E||!1,clickOutsideAnchor:Qr||!1};P&&(Object.assign(Lt,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(wt,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(_e,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),y0(()=>(bt.current=!0,()=>{bt.current=!1}),[]);const re=D=>{bt.current&&(D&&xt(!0),setTimeout(()=>{bt.current&&(H?.(D),N===void 0&&Xn(D))},10))};d.useEffect(()=>{if(N===void 0)return()=>null;N&&xt(!0);const D=setTimeout(()=>{Xn(N)},10);return()=>{clearTimeout(D)}},[N]),d.useEffect(()=>{if(we!==dn.current)if(Me(Ue),dn.current=we,we)k?.();else{const D=(F=>{const z=F.match(/^([\d.]+)(ms|s)$/);if(!z)return 0;const[,se,fe]=z;return Number(se)*(fe==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));Ue.current=setTimeout(()=>{xt(!1),un(null),B?.()},D+25)}},[we]);const Zn=D=>{Zr(F=>ls(F,D)?F:D)},mn=(D=v)=>{Me(be),yt?re(!0):be.current=setTimeout(()=>{re(!0)},D)},Vt=(D=g)=>{Me($e),$e.current=setTimeout(()=>{Ot.current||re(!1)},D)},gn=D=>{var F;if(!D)return;const z=(F=D.currentTarget)!==null&&F!==void 0?F:D.target;if(!z?.isConnected)return ve(null),void qn({current:null});v?mn():re(!0),ve(z),qn({current:z}),Me($e)},Ft=()=>{b?Vt(g||100):g?Vt():re(!1),Me(be)},Bt=({x:D,y:F})=>{var z;const se={getBoundingClientRect:()=>({x:D,y:F,width:0,height:0,top:F,left:D,right:D,bottom:F})};ul({place:(z=Q?.place)!==null&&z!==void 0?z:a,offset:l,elementReference:se,tooltipReference:ce.current,tooltipArrowReference:at.current,strategy:f,middlewares:p,border:Fe,arrowSize:ye}).then(fe=>{Zn(fe)})},$t=D=>{if(!D)return;const F=D,z={x:F.clientX,y:F.clientY};Bt(z),fn.current=z},vn=D=>{var F;if(!we)return;const z=D.target;z.isConnected&&(!((F=ce.current)===null||F===void 0)&&F.contains(z)||[document.querySelector(`[id='${s}']`),...ct].some(se=>se?.contains(z))||(re(!1),Me(be)))},Qn=fl(gn,50),ae=fl(Ft,50),Ae=D=>{ae.cancel(),Qn(D)},L=()=>{Qn.cancel(),ae()},W=d.useCallback(()=>{var D,F;const z=(D=Q?.position)!==null&&D!==void 0?D:j;z?Bt(z):y?fn.current&&Bt(fn.current):K?.isConnected&&ul({place:(F=Q?.place)!==null&&F!==void 0?F:a,offset:l,elementReference:K,tooltipReference:ce.current,tooltipArrowReference:at.current,strategy:f,middlewares:p,border:Fe,arrowSize:ye}).then(se=>{bt.current&&Zn(se)})},[we,K,U,I,a,Q?.place,l,f,j,Q?.position,y,ye]);d.useEffect(()=>{var D,F;const z=new Set(hn);ct.forEach(G=>{Y?.(G)||z.add({current:G})});const se=document.querySelector(`[id='${s}']`);se&&!Y?.(se)&&z.add({current:se});const fe=()=>{re(!1)},ze=pl(K),We=pl(ce.current);_e.scroll&&(window.addEventListener("scroll",fe),ze?.addEventListener("scroll",fe),We?.addEventListener("scroll",fe));let pe=null;_e.resize?window.addEventListener("resize",fe):K&&ce.current&&(pe=Zu(K,ce.current,W,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const Re=G=>{G.key==="Escape"&&re(!1)};_e.escape&&window.addEventListener("keydown",Re),_e.clickOutsideAnchor&&window.addEventListener("click",vn);const X=[],Ut=G=>!!(G?.target&&K?.contains(G.target)),kf=G=>{we&&Ut(G)||gn(G)},_f=G=>{we&&Ut(G)&&Ft()},yi=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],xi=["click","dblclick","mousedown","mouseup"];Object.entries(Lt).forEach(([G,et])=>{et&&(yi.includes(G)?X.push({event:G,listener:Ae}):xi.includes(G)&&X.push({event:G,listener:kf}))}),Object.entries(wt).forEach(([G,et])=>{et&&(yi.includes(G)?X.push({event:G,listener:L}):xi.includes(G)&&X.push({event:G,listener:_f}))}),y&&X.push({event:"pointermove",listener:$t});const bi=()=>{Ot.current=!0},wi=()=>{Ot.current=!1,Ft()},Si=b&&(wt.mouseout||wt.mouseleave);return Si&&((D=ce.current)===null||D===void 0||D.addEventListener("mouseover",bi),(F=ce.current)===null||F===void 0||F.addEventListener("mouseout",wi)),X.forEach(({event:G,listener:et})=>{z.forEach(Jr=>{var yn;(yn=Jr.current)===null||yn===void 0||yn.addEventListener(G,et)})}),()=>{var G,et;_e.scroll&&(window.removeEventListener("scroll",fe),ze?.removeEventListener("scroll",fe),We?.removeEventListener("scroll",fe)),_e.resize?window.removeEventListener("resize",fe):pe?.(),_e.clickOutsideAnchor&&window.removeEventListener("click",vn),_e.escape&&window.removeEventListener("keydown",Re),Si&&((G=ce.current)===null||G===void 0||G.removeEventListener("mouseover",bi),(et=ce.current)===null||et===void 0||et.removeEventListener("mouseout",wi)),X.forEach(({event:Jr,listener:yn})=>{z.forEach(Of=>{var eo;(eo=Of.current)===null||eo===void 0||eo.removeEventListener(Jr,yn)})})}},[K,W,yt,hn,ct,T,A,R,It,v,g]),d.useEffect(()=>{var D,F;let z=(F=(D=Q?.anchorSelect)!==null&&D!==void 0?D:i)!==null&&F!==void 0?F:"";!z&&t&&(z=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);const se=new MutationObserver(fe=>{const ze=[],We=[];fe.forEach(pe=>{if(pe.type==="attributes"&&pe.attributeName==="data-tooltip-id"&&(pe.target.getAttribute("data-tooltip-id")===t?ze.push(pe.target):pe.oldValue===t&&We.push(pe.target)),pe.type==="childList"){if(K){const Re=[...pe.removedNodes].filter(X=>X.nodeType===1);if(z)try{We.push(...Re.filter(X=>X.matches(z))),We.push(...Re.flatMap(X=>[...X.querySelectorAll(z)]))}catch{}Re.some(X=>{var Ut;return!!(!((Ut=X?.contains)===null||Ut===void 0)&&Ut.call(X,K))&&(xt(!1),re(!1),ve(null),Me(be),Me($e),!0)})}if(z)try{const Re=[...pe.addedNodes].filter(X=>X.nodeType===1);ze.push(...Re.filter(X=>X.matches(z))),ze.push(...Re.flatMap(X=>[...X.querySelectorAll(z)]))}catch{}}}),(ze.length||We.length)&&pn(pe=>[...pe.filter(Re=>!We.includes(Re)),...ze])});return se.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{se.disconnect()}},[t,i,Q?.anchorSelect,K]),d.useEffect(()=>{W()},[W]),d.useEffect(()=>{if(!_?.current)return()=>null;const D=new ResizeObserver(()=>{setTimeout(()=>W())});return D.observe(_.current),()=>{D.disconnect()}},[U,_?.current]),d.useEffect(()=>{var D;const F=document.querySelector(`[id='${s}']`),z=[...ct,F];K&&z.includes(K)||ve((D=ct[0])!==null&&D!==void 0?D:F)},[s,ct,K]),d.useEffect(()=>(M&&re(!0),()=>{Me(be),Me($e)}),[]),d.useEffect(()=>{var D;let F=(D=Q?.anchorSelect)!==null&&D!==void 0?D:i;if(!F&&t&&(F=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),F)try{const z=Array.from(document.querySelectorAll(F));pn(z)}catch{pn([])}},[t,i,Q?.anchorSelect]),d.useEffect(()=>{be.current&&(Me(be),mn(v))},[v]);const Se=(ke=Q?.content)!==null&&ke!==void 0?ke:U,St=we&&Object.keys(lt.tooltipStyles).length>0;return d.useImperativeHandle(e,()=>({open:D=>{if(D?.anchorSelect)try{document.querySelector(D.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${D.anchorSelect}" is not a valid CSS selector`)}un(D??null),D?.delay?mn(D.delay):re(!0)},close:D=>{D?.delay?Vt(D.delay):re(!1)},activeAnchor:K,place:lt.place,isOpen:!!(yt&&!x&&Se&&St)})),yt&&!x&&Se?de.createElement(m,{id:t,role:Z,className:as("react-tooltip",Ht.tooltip,Po.tooltip,Po[o],n,`react-tooltip__place-${lt.place}`,Ht[St?"show":"closing"],St?"react-tooltip__show":"react-tooltip__closing",f==="fixed"&&Ht.fixed,b&&Ht.clickable),onTransitionEnd:D=>{Me(Ue),we||D.propertyName!=="opacity"||(xt(!1),un(null),B?.())},style:{...I,...lt.tooltipStyles,opacity:Be!==void 0&&St?Be:void 0},ref:ce},Se,de.createElement(m,{className:as("react-tooltip-arrow",Ht.arrow,Po.arrow,r,w&&Ht.noArrow),style:{...lt.tooltipArrowStyles,background:Je?`linear-gradient(to right bottom, transparent 50%, ${Je} 50%)`:void 0,"--rt-arrow-size":`${ye}px`},ref:at})):null},C0=({content:e})=>de.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),T0=de.forwardRef(({id:e,anchorId:t,anchorSelect:n,content:r,html:o,render:s,className:i,classNameArrow:a,variant:l="dark",place:u="top",offset:c=10,wrapper:f="div",children:p=null,events:m=["hover"],openOnClick:v=!1,positionStrategy:g="absolute",middlewares:y,delayShow:x=0,delayHide:w=0,float:b=!1,hidden:S=!1,noArrow:C=!1,clickable:E=!1,closeOnEsc:T=!1,closeOnScroll:A=!1,closeOnResize:R=!1,openEvents:P,closeEvents:I,globalCloseEvents:j,imperativeModeOnly:k=!1,style:B,position:Y,isOpen:U,defaultIsOpen:_=!1,disableStyleInjection:N=!1,border:M,opacity:H,arrowColor:K,arrowSize:ve,setIsOpen:Fe,afterShow:Be,afterHide:Je,disableTooltip:ye,role:Z="tooltip"},ke)=>{const[ce,at]=d.useState(r),[be,$e]=d.useState(o),[Ue,lt]=d.useState(u),[Zr,we]=d.useState(l),[Xn,yt]=d.useState(c),[xt,Q]=d.useState(x),[un,dn]=d.useState(w),[fn,hn]=d.useState(b),[qn,Ot]=d.useState(S),[ct,pn]=d.useState(f),[bt,It]=d.useState(m),[Qr,Lt]=d.useState(g),[wt,_e]=d.useState(null),[re,Zn]=d.useState(null),mn=d.useRef(N),{anchorRefs:Vt,activeAnchor:gn}=Pd(e),Ft=ae=>ae?.getAttributeNames().reduce((Ae,L)=>{var W;return L.startsWith("data-tooltip-")&&(Ae[L.replace(/^data-tooltip-/,"")]=(W=ae?.getAttribute(L))!==null&&W!==void 0?W:null),Ae},{}),Bt=ae=>{const Ae={place:L=>{var W;lt((W=L)!==null&&W!==void 0?W:u)},content:L=>{at(L??r)},html:L=>{$e(L??o)},variant:L=>{var W;we((W=L)!==null&&W!==void 0?W:l)},offset:L=>{yt(L===null?c:Number(L))},wrapper:L=>{var W;pn((W=L)!==null&&W!==void 0?W:f)},events:L=>{const W=L?.split(" ");It(W??m)},"position-strategy":L=>{var W;Lt((W=L)!==null&&W!==void 0?W:g)},"delay-show":L=>{Q(L===null?x:Number(L))},"delay-hide":L=>{dn(L===null?w:Number(L))},float:L=>{hn(L===null?b:L==="true")},hidden:L=>{Ot(L===null?S:L==="true")},"class-name":L=>{_e(L)}};Object.values(Ae).forEach(L=>L(null)),Object.entries(ae).forEach(([L,W])=>{var Se;(Se=Ae[L])===null||Se===void 0||Se.call(Ae,W)})};d.useEffect(()=>{at(r)},[r]),d.useEffect(()=>{$e(o)},[o]),d.useEffect(()=>{lt(u)},[u]),d.useEffect(()=>{we(l)},[l]),d.useEffect(()=>{yt(c)},[c]),d.useEffect(()=>{Q(x)},[x]),d.useEffect(()=>{dn(w)},[w]),d.useEffect(()=>{hn(b)},[b]),d.useEffect(()=>{Ot(S)},[S]),d.useEffect(()=>{Lt(g)},[g]),d.useEffect(()=>{mn.current!==N&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[N]),d.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:N==="core",disableBase:N}}))},[]),d.useEffect(()=>{var ae;const Ae=new Set(Vt);let L=n;if(!L&&e&&(L=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),L)try{document.querySelectorAll(L).forEach(F=>{Ae.add({current:F})})}catch{console.warn(`[react-tooltip] "${L}" is not a valid CSS selector`)}const W=document.querySelector(`[id='${t}']`);if(W&&Ae.add({current:W}),!Ae.size)return()=>null;const Se=(ae=re??W)!==null&&ae!==void 0?ae:gn.current,St=new MutationObserver(F=>{F.forEach(z=>{var se;if(!Se||z.type!=="attributes"||!(!((se=z.attributeName)===null||se===void 0)&&se.startsWith("data-tooltip-")))return;const fe=Ft(Se);Bt(fe)})}),D={attributes:!0,childList:!1,subtree:!1};if(Se){const F=Ft(Se);Bt(F),St.observe(Se,D)}return()=>{St.disconnect()}},[Vt,gn,re,t,n]),d.useEffect(()=>{B?.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),M&&!dl("border",`${M}`)&&console.warn(`[react-tooltip] "${M}" is not a valid \`border\`.`),B?.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),H&&!dl("opacity",`${H}`)&&console.warn(`[react-tooltip] "${H}" is not a valid \`opacity\`.`)},[]);let $t=p;const vn=d.useRef(null);if(s){const ae=s({content:re?.getAttribute("data-tooltip-content")||ce||null,activeAnchor:re});$t=ae?de.createElement("div",{ref:vn,className:"react-tooltip-content-wrapper"},ae):null}else ce&&($t=ce);be&&($t=de.createElement(C0,{content:be}));const Qn={forwardRef:ke,id:e,anchorId:t,anchorSelect:n,className:as(i,wt),classNameArrow:a,content:$t,contentWrapperRef:vn,place:Ue,variant:Zr,offset:Xn,wrapper:ct,events:bt,openOnClick:v,positionStrategy:Qr,middlewares:y,delayShow:xt,delayHide:un,float:fn,hidden:qn,noArrow:C,clickable:E,closeOnEsc:T,closeOnScroll:A,closeOnResize:R,openEvents:P,closeEvents:I,globalCloseEvents:j,imperativeModeOnly:k,style:B,position:Y,isOpen:U,defaultIsOpen:_,border:M,opacity:H,arrowColor:K,arrowSize:ve,setIsOpen:Fe,afterShow:Be,afterHide:Je,disableTooltip:ye,activeAnchor:re,setActiveAnchor:ae=>Zn(ae),role:Z};return de.createElement(S0,{...Qn})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||cl({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||cl({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});const E0=({content:e,side:t="top",children:n})=>{const r=d.useMemo(()=>{const o="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";return Array.from({length:6},()=>o[Math.floor(Math.random()*o.length)]).join("")},[]);return h.jsxs("div",{children:[h.jsx("style",{children:`
          :root {
            --context-card-border: #ebebeb
          }
          .dark {
            --context-card-border: #2e2e2e
          }
        `}),h.jsx("div",{id:r,className:"font-sans",children:n}),h.jsx(T0,{anchorSelect:`#${r}`,place:t,opacity:1,border:"1px solid var(--context-card-border)",className:"!font-sans !text-center !text-base !rounded-lg !bg-background-100 !text-gray-1000",children:e})]})},A0={Trigger:E0};function Md(e){const t=e+"CollectionProvider",[n,r]=jt(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:y,children:x}=g,w=de.useRef(null),b=de.useRef(new Map).current;return h.jsx(o,{scope:y,itemMap:b,collectionRef:w,children:x})};i.displayName=t;const a=e+"CollectionSlot",l=gr(a),u=de.forwardRef((g,y)=>{const{scope:x,children:w}=g,b=s(a,x),S=ie(y,b.collectionRef);return h.jsx(l,{ref:S,children:w})});u.displayName=a;const c=e+"CollectionItemSlot",f="data-radix-collection-item",p=gr(c),m=de.forwardRef((g,y)=>{const{scope:x,children:w,...b}=g,S=de.useRef(null),C=ie(y,S),E=s(c,x);return de.useEffect(()=>(E.itemMap.set(S,{ref:S,...b}),()=>void E.itemMap.delete(S))),h.jsx(p,{[f]:"",ref:C,children:w})});m.displayName=c;function v(g){const y=s(e+"CollectionConsumer",g);return de.useCallback(()=>{const w=y.collectionRef.current;if(!w)return[];const b=Array.from(w.querySelectorAll(`[${f}]`));return Array.from(y.itemMap.values()).sort((E,T)=>b.indexOf(E.ref.current)-b.indexOf(T.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:i,Slot:u,ItemSlot:m},v,r]}var R0=d.createContext(void 0);function Dd(e){const t=d.useContext(R0);return e||t||"ltr"}var Mo="rovingFocusGroup.onEntryFocus",P0={bubbles:!1,cancelable:!0},Hn="RovingFocusGroup",[cs,Nd,M0]=Md(Hn),[D0,jd]=jt(Hn,[M0]),[N0,j0]=D0(Hn),kd=d.forwardRef((e,t)=>h.jsx(cs.Provider,{scope:e.__scopeRovingFocusGroup,children:h.jsx(cs.Slot,{scope:e.__scopeRovingFocusGroup,children:h.jsx(k0,{...e,ref:t})})}));kd.displayName=Hn;var k0=d.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:c=!1,...f}=e,p=d.useRef(null),m=ie(t,p),v=Dd(s),[g,y]=Ir({prop:i,defaultProp:a??null,onChange:l,caller:Hn}),[x,w]=d.useState(!1),b=tt(u),S=Nd(n),C=d.useRef(!1),[E,T]=d.useState(0);return d.useEffect(()=>{const A=p.current;if(A)return A.addEventListener(Mo,b),()=>A.removeEventListener(Mo,b)},[b]),h.jsx(N0,{scope:n,orientation:r,dir:v,loop:o,currentTabStopId:g,onItemFocus:d.useCallback(A=>y(A),[y]),onItemShiftTab:d.useCallback(()=>w(!0),[]),onFocusableItemAdd:d.useCallback(()=>T(A=>A+1),[]),onFocusableItemRemove:d.useCallback(()=>T(A=>A-1),[]),children:h.jsx(te.div,{tabIndex:x||E===0?-1:0,"data-orientation":r,...f,ref:m,style:{outline:"none",...e.style},onMouseDown:$(e.onMouseDown,()=>{C.current=!0}),onFocus:$(e.onFocus,A=>{const R=!C.current;if(A.target===A.currentTarget&&R&&!x){const P=new CustomEvent(Mo,P0);if(A.currentTarget.dispatchEvent(P),!P.defaultPrevented){const I=S().filter(U=>U.focusable),j=I.find(U=>U.active),k=I.find(U=>U.id===g),Y=[j,k,...I].filter(Boolean).map(U=>U.ref.current);Id(Y,c)}}C.current=!1}),onBlur:$(e.onBlur,()=>w(!1))})})}),_d="RovingFocusGroupItem",Od=d.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,children:i,...a}=e,l=Mt(),u=s||l,c=j0(_d,n),f=c.currentTabStopId===u,p=Nd(n),{onFocusableItemAdd:m,onFocusableItemRemove:v,currentTabStopId:g}=c;return d.useEffect(()=>{if(r)return m(),()=>v()},[r,m,v]),h.jsx(cs.ItemSlot,{scope:n,id:u,focusable:r,active:o,children:h.jsx(te.span,{tabIndex:f?0:-1,"data-orientation":c.orientation,...a,ref:t,onMouseDown:$(e.onMouseDown,y=>{r?c.onItemFocus(u):y.preventDefault()}),onFocus:$(e.onFocus,()=>c.onItemFocus(u)),onKeyDown:$(e.onKeyDown,y=>{if(y.key==="Tab"&&y.shiftKey){c.onItemShiftTab();return}if(y.target!==y.currentTarget)return;const x=I0(y,c.orientation,c.dir);if(x!==void 0){if(y.metaKey||y.ctrlKey||y.altKey||y.shiftKey)return;y.preventDefault();let b=p().filter(S=>S.focusable).map(S=>S.ref.current);if(x==="last")b.reverse();else if(x==="prev"||x==="next"){x==="prev"&&b.reverse();const S=b.indexOf(y.currentTarget);b=c.loop?L0(b,S+1):b.slice(S+1)}setTimeout(()=>Id(b))}}),children:typeof i=="function"?i({isCurrentTabStop:f,hasTabStop:g!=null}):i})})});Od.displayName=_d;var _0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O0(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function I0(e,t,n){const r=O0(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return _0[r]}function Id(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function L0(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var V0=kd,F0=Od,us=["Enter"," "],B0=["ArrowDown","PageUp","Home"],Ld=["ArrowUp","PageDown","End"],$0=[...B0,...Ld],U0={ltr:[...us,"ArrowRight"],rtl:[...us,"ArrowLeft"]},z0={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Gn="Menu",[Ln,W0,K0]=Md(Gn),[kt,Vd]=jt(Gn,[K0,zr,jd]),Yr=zr(),Fd=jd(),[H0,_t]=kt(Gn),[G0,Yn]=kt(Gn),Bd=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:i=!0}=e,a=Yr(t),[l,u]=d.useState(null),c=d.useRef(!1),f=tt(s),p=Dd(o);return d.useEffect(()=>{const m=()=>{c.current=!0,document.addEventListener("pointerdown",v,{capture:!0,once:!0}),document.addEventListener("pointermove",v,{capture:!0,once:!0})},v=()=>c.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",v,{capture:!0}),document.removeEventListener("pointermove",v,{capture:!0})}},[]),h.jsx(fd,{...a,children:h.jsx(H0,{scope:t,open:n,onOpenChange:f,content:l,onContentChange:u,children:h.jsx(G0,{scope:t,onClose:d.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:c,dir:p,modal:i,children:r})})})};Bd.displayName=Gn;var Y0="MenuAnchor",di=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Yr(n);return h.jsx(hd,{...o,...r,ref:t})});di.displayName=Y0;var fi="MenuPortal",[X0,$d]=kt(fi,{forceMount:void 0}),Ud=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,s=_t(fi,t);return h.jsx(X0,{scope:t,forceMount:n,children:h.jsx(it,{present:n||s.open,children:h.jsx(qs,{asChild:!0,container:o,children:r})})})};Ud.displayName=fi;var je="MenuContent",[q0,hi]=kt(je),zd=d.forwardRef((e,t)=>{const n=$d(je,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=_t(je,e.__scopeMenu),i=Yn(je,e.__scopeMenu);return h.jsx(Ln.Provider,{scope:e.__scopeMenu,children:h.jsx(it,{present:r||s.open,children:h.jsx(Ln.Slot,{scope:e.__scopeMenu,children:i.modal?h.jsx(Z0,{...o,ref:t}):h.jsx(Q0,{...o,ref:t})})})})}),Z0=d.forwardRef((e,t)=>{const n=_t(je,e.__scopeMenu),r=d.useRef(null),o=ie(t,r);return d.useEffect(()=>{const s=r.current;if(s)return xu(s)},[]),h.jsx(pi,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:$(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q0=d.forwardRef((e,t)=>{const n=_t(je,e.__scopeMenu);return h.jsx(pi,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),J0=gr("MenuContent.ScrollLock"),pi=d.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:m,disableOutsideScroll:v,...g}=e,y=_t(je,n),x=Yn(je,n),w=Yr(n),b=Fd(n),S=W0(n),[C,E]=d.useState(null),T=d.useRef(null),A=ie(t,T,y.onContentChange),R=d.useRef(0),P=d.useRef(""),I=d.useRef(0),j=d.useRef(null),k=d.useRef("right"),B=d.useRef(0),Y=v?Zs:d.Fragment,U=v?{as:J0,allowPinchZoom:!0}:void 0,_=M=>{const H=P.current+M,K=S().filter(Z=>!Z.disabled),ve=document.activeElement,Fe=K.find(Z=>Z.ref.current===ve)?.textValue,Be=K.map(Z=>Z.textValue),Je=dS(Be,H,Fe),ye=K.find(Z=>Z.textValue===Je)?.ref.current;(function Z(ke){P.current=ke,window.clearTimeout(R.current),ke!==""&&(R.current=window.setTimeout(()=>Z(""),1e3))})(H),ye&&setTimeout(()=>ye.focus())};d.useEffect(()=>()=>window.clearTimeout(R.current),[]),uu();const N=d.useCallback(M=>k.current===j.current?.side&&hS(M,j.current?.area),[]);return h.jsx(q0,{scope:n,searchRef:P,onItemEnter:d.useCallback(M=>{N(M)&&M.preventDefault()},[N]),onItemLeave:d.useCallback(M=>{N(M)||(T.current?.focus(),E(null))},[N]),onTriggerLeave:d.useCallback(M=>{N(M)&&M.preventDefault()},[N]),pointerGraceTimerRef:I,onPointerGraceIntentChange:d.useCallback(M=>{j.current=M},[]),children:h.jsx(Y,{...U,children:h.jsx(Xs,{asChild:!0,trapped:o,onMountAutoFocus:$(s,M=>{M.preventDefault(),T.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:h.jsx(Lr,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:m,children:h.jsx(V0,{asChild:!0,...b,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:C,onCurrentTabStopIdChange:E,onEntryFocus:$(l,M=>{x.isUsingKeyboardRef.current||M.preventDefault()}),preventScrollOnEntryFocus:!0,children:h.jsx(pd,{role:"menu","aria-orientation":"vertical","data-state":sf(y.open),"data-radix-menu-content":"",dir:x.dir,...w,...g,ref:A,style:{outline:"none",...g.style},onKeyDown:$(g.onKeyDown,M=>{const K=M.target.closest("[data-radix-menu-content]")===M.currentTarget,ve=M.ctrlKey||M.altKey||M.metaKey,Fe=M.key.length===1;K&&(M.key==="Tab"&&M.preventDefault(),!ve&&Fe&&_(M.key));const Be=T.current;if(M.target!==Be||!$0.includes(M.key))return;M.preventDefault();const ye=S().filter(Z=>!Z.disabled).map(Z=>Z.ref.current);Ld.includes(M.key)&&ye.reverse(),cS(ye)}),onBlur:$(e.onBlur,M=>{M.currentTarget.contains(M.target)||(window.clearTimeout(R.current),P.current="")}),onPointerMove:$(e.onPointerMove,Vn(M=>{const H=M.target,K=B.current!==M.clientX;if(M.currentTarget.contains(H)&&K){const ve=M.clientX>B.current?"right":"left";k.current=ve,B.current=M.clientX}}))})})})})})})});zd.displayName=je;var eS="MenuGroup",mi=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return h.jsx(te.div,{role:"group",...r,ref:t})});mi.displayName=eS;var tS="MenuLabel",Wd=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return h.jsx(te.div,{...r,ref:t})});Wd.displayName=tS;var Pr="MenuItem",ml="menu.itemSelect",Xr=d.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,s=d.useRef(null),i=Yn(Pr,e.__scopeMenu),a=hi(Pr,e.__scopeMenu),l=ie(t,s),u=d.useRef(!1),c=()=>{const f=s.current;if(!n&&f){const p=new CustomEvent(ml,{bubbles:!0,cancelable:!0});f.addEventListener(ml,m=>r?.(m),{once:!0}),Tl(f,p),p.defaultPrevented?u.current=!1:i.onClose()}};return h.jsx(Kd,{...o,ref:l,disabled:n,onClick:$(e.onClick,c),onPointerDown:f=>{e.onPointerDown?.(f),u.current=!0},onPointerUp:$(e.onPointerUp,f=>{u.current||f.currentTarget?.click()}),onKeyDown:$(e.onKeyDown,f=>{const p=a.searchRef.current!=="";n||p&&f.key===" "||us.includes(f.key)&&(f.currentTarget.click(),f.preventDefault())})})});Xr.displayName=Pr;var Kd=d.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...s}=e,i=hi(Pr,n),a=Fd(n),l=d.useRef(null),u=ie(t,l),[c,f]=d.useState(!1),[p,m]=d.useState("");return d.useEffect(()=>{const v=l.current;v&&m((v.textContent??"").trim())},[s.children]),h.jsx(Ln.ItemSlot,{scope:n,disabled:r,textValue:o??p,children:h.jsx(F0,{asChild:!0,...a,focusable:!r,children:h.jsx(te.div,{role:"menuitem","data-highlighted":c?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:u,onPointerMove:$(e.onPointerMove,Vn(v=>{r?i.onItemLeave(v):(i.onItemEnter(v),v.defaultPrevented||v.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:$(e.onPointerLeave,Vn(v=>i.onItemLeave(v))),onFocus:$(e.onFocus,()=>f(!0)),onBlur:$(e.onBlur,()=>f(!1))})})})}),nS="MenuCheckboxItem",Hd=d.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return h.jsx(Zd,{scope:e.__scopeMenu,checked:n,children:h.jsx(Xr,{role:"menuitemcheckbox","aria-checked":Mr(n)?"mixed":n,...o,ref:t,"data-state":vi(n),onSelect:$(o.onSelect,()=>r?.(Mr(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Hd.displayName=nS;var Gd="MenuRadioGroup",[rS,oS]=kt(Gd,{value:void 0,onValueChange:()=>{}}),Yd=d.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,s=tt(r);return h.jsx(rS,{scope:e.__scopeMenu,value:n,onValueChange:s,children:h.jsx(mi,{...o,ref:t})})});Yd.displayName=Gd;var Xd="MenuRadioItem",qd=d.forwardRef((e,t)=>{const{value:n,...r}=e,o=oS(Xd,e.__scopeMenu),s=n===o.value;return h.jsx(Zd,{scope:e.__scopeMenu,checked:s,children:h.jsx(Xr,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":vi(s),onSelect:$(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});qd.displayName=Xd;var gi="MenuItemIndicator",[Zd,sS]=kt(gi,{checked:!1}),Qd=d.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,s=sS(gi,n);return h.jsx(it,{present:r||Mr(s.checked)||s.checked===!0,children:h.jsx(te.span,{...o,ref:t,"data-state":vi(s.checked)})})});Qd.displayName=gi;var iS="MenuSeparator",Jd=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return h.jsx(te.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});Jd.displayName=iS;var aS="MenuArrow",ef=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Yr(n);return h.jsx(md,{...o,...r,ref:t})});ef.displayName=aS;var lS="MenuSub",[TC,tf]=kt(lS),wn="MenuSubTrigger",nf=d.forwardRef((e,t)=>{const n=_t(wn,e.__scopeMenu),r=Yn(wn,e.__scopeMenu),o=tf(wn,e.__scopeMenu),s=hi(wn,e.__scopeMenu),i=d.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},c=d.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return d.useEffect(()=>c,[c]),d.useEffect(()=>{const f=a.current;return()=>{window.clearTimeout(f),l(null)}},[a,l]),h.jsx(di,{asChild:!0,...u,children:h.jsx(Kd,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":sf(n.open),...e,ref:Cl(t,o.onTriggerChange),onClick:f=>{e.onClick?.(f),!(e.disabled||f.defaultPrevented)&&(f.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:$(e.onPointerMove,Vn(f=>{s.onItemEnter(f),!f.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(s.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),c()},100))})),onPointerLeave:$(e.onPointerLeave,Vn(f=>{c();const p=n.content?.getBoundingClientRect();if(p){const m=n.content?.dataset.side,v=m==="right",g=v?-5:5,y=p[v?"left":"right"],x=p[v?"right":"left"];s.onPointerGraceIntentChange({area:[{x:f.clientX+g,y:f.clientY},{x:y,y:p.top},{x,y:p.top},{x,y:p.bottom},{x:y,y:p.bottom}],side:m}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(f),f.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:$(e.onKeyDown,f=>{const p=s.searchRef.current!=="";e.disabled||p&&f.key===" "||U0[r.dir].includes(f.key)&&(n.onOpenChange(!0),n.content?.focus(),f.preventDefault())})})})});nf.displayName=wn;var rf="MenuSubContent",of=d.forwardRef((e,t)=>{const n=$d(je,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=_t(je,e.__scopeMenu),i=Yn(je,e.__scopeMenu),a=tf(rf,e.__scopeMenu),l=d.useRef(null),u=ie(t,l);return h.jsx(Ln.Provider,{scope:e.__scopeMenu,children:h.jsx(it,{present:r||s.open,children:h.jsx(Ln.Slot,{scope:e.__scopeMenu,children:h.jsx(pi,{id:a.contentId,"aria-labelledby":a.triggerId,...o,ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:c=>{i.isUsingKeyboardRef.current&&l.current?.focus(),c.preventDefault()},onCloseAutoFocus:c=>c.preventDefault(),onFocusOutside:$(e.onFocusOutside,c=>{c.target!==a.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:$(e.onEscapeKeyDown,c=>{i.onClose(),c.preventDefault()}),onKeyDown:$(e.onKeyDown,c=>{const f=c.currentTarget.contains(c.target),p=z0[i.dir].includes(c.key);f&&p&&(s.onOpenChange(!1),a.trigger?.focus(),c.preventDefault())})})})})})});of.displayName=rf;function sf(e){return e?"open":"closed"}function Mr(e){return e==="indeterminate"}function vi(e){return Mr(e)?"indeterminate":e?"checked":"unchecked"}function cS(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function uS(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function dS(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=uS(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function fS(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s],l=t[i],u=a.x,c=a.y,f=l.x,p=l.y;c>r!=p>r&&n<(f-u)*(r-c)/(p-c)+u&&(o=!o)}return o}function hS(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return fS(n,t)}function Vn(e){return t=>t.pointerType==="mouse"?e(t):void 0}var pS=Bd,mS=di,gS=Ud,vS=zd,yS=mi,xS=Wd,bS=Xr,wS=Hd,SS=Yd,CS=qd,TS=Qd,ES=Jd,AS=ef,RS=nf,PS=of,qr="DropdownMenu",[MS,EC]=jt(qr,[Vd]),ge=Vd(),[DS,af]=MS(qr),lf=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:i,modal:a=!0}=e,l=ge(t),u=d.useRef(null),[c,f]=Ir({prop:o,defaultProp:s??!1,onChange:i,caller:qr});return h.jsx(DS,{scope:t,triggerId:Mt(),triggerRef:u,contentId:Mt(),open:c,onOpenChange:f,onOpenToggle:d.useCallback(()=>f(p=>!p),[f]),modal:a,children:h.jsx(pS,{...l,open:c,onOpenChange:f,dir:r,modal:a,children:n})})};lf.displayName=qr;var cf="DropdownMenuTrigger",uf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,s=af(cf,n),i=ge(n);return h.jsx(mS,{asChild:!0,...i,children:h.jsx(te.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Cl(t,s.triggerRef),onPointerDown:$(e.onPointerDown,a=>{!r&&a.button===0&&a.ctrlKey===!1&&(s.onOpenToggle(),s.open||a.preventDefault())}),onKeyDown:$(e.onKeyDown,a=>{r||(["Enter"," "].includes(a.key)&&s.onOpenToggle(),a.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});uf.displayName=cf;var NS="DropdownMenuPortal",df=e=>{const{__scopeDropdownMenu:t,...n}=e,r=ge(t);return h.jsx(gS,{...r,...n})};df.displayName=NS;var ff="DropdownMenuContent",hf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=af(ff,n),s=ge(n),i=d.useRef(!1);return h.jsx(vS,{id:o.contentId,"aria-labelledby":o.triggerId,...s,...r,ref:t,onCloseAutoFocus:$(e.onCloseAutoFocus,a=>{i.current||o.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:$(e.onInteractOutside,a=>{const l=a.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,c=l.button===2||u;(!o.modal||c)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});hf.displayName=ff;var jS="DropdownMenuGroup",kS=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(yS,{...o,...r,ref:t})});kS.displayName=jS;var _S="DropdownMenuLabel",pf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(xS,{...o,...r,ref:t})});pf.displayName=_S;var OS="DropdownMenuItem",mf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(bS,{...o,...r,ref:t})});mf.displayName=OS;var IS="DropdownMenuCheckboxItem",gf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(wS,{...o,...r,ref:t})});gf.displayName=IS;var LS="DropdownMenuRadioGroup",VS=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(SS,{...o,...r,ref:t})});VS.displayName=LS;var FS="DropdownMenuRadioItem",vf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(CS,{...o,...r,ref:t})});vf.displayName=FS;var BS="DropdownMenuItemIndicator",yf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(TS,{...o,...r,ref:t})});yf.displayName=BS;var $S="DropdownMenuSeparator",xf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(ES,{...o,...r,ref:t})});xf.displayName=$S;var US="DropdownMenuArrow",zS=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(AS,{...o,...r,ref:t})});zS.displayName=US;var WS="DropdownMenuSubTrigger",bf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(RS,{...o,...r,ref:t})});bf.displayName=WS;var KS="DropdownMenuSubContent",wf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return h.jsx(PS,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});wf.displayName=KS;var HS=lf,GS=uf,YS=df,Sf=hf,Cf=pf,Tf=mf,Ef=gf,Af=vf,Rf=yf,Pf=xf,Mf=bf,Df=wf;const XS=HS,qS=GS,ZS=d.forwardRef(({className:e,inset:t,children:n,...r},o)=>h.jsxs(Mf,{ref:o,className:O("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,h.jsx(Zf,{className:"ml-auto h-4 w-4"})]}));ZS.displayName=Mf.displayName;const QS=d.forwardRef(({className:e,...t},n)=>h.jsx(Df,{ref:n,className:O("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));QS.displayName=Df.displayName;const Nf=d.forwardRef(({className:e,sideOffset:t=4,...n},r)=>h.jsx(YS,{children:h.jsx(Sf,{ref:r,sideOffset:t,className:O("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));Nf.displayName=Sf.displayName;const ds=d.forwardRef(({className:e,inset:t,...n},r)=>h.jsx(Tf,{ref:r,className:O("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));ds.displayName=Tf.displayName;const JS=d.forwardRef(({className:e,children:t,checked:n,...r},o)=>h.jsxs(Ef,{ref:o,className:O("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[h.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:h.jsx(Rf,{children:h.jsx(Al,{className:"h-4 w-4"})})}),t]}));JS.displayName=Ef.displayName;const eC=d.forwardRef(({className:e,children:t,...n},r)=>h.jsxs(Af,{ref:r,className:O("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[h.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:h.jsx(Rf,{children:h.jsx(th,{className:"h-2 w-2 fill-current"})})}),t]}));eC.displayName=Af.displayName;const jf=d.forwardRef(({className:e,inset:t,...n},r)=>h.jsx(Cf,{ref:r,className:O("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));jf.displayName=Cf.displayName;const fs=d.forwardRef(({className:e,...t},n)=>h.jsx(Pf,{ref:n,className:O("-mx-1 my-1 h-px bg-muted",e),...t}));fs.displayName=Pf.displayName;function tC({className:e}){const t=xl(),{logout:n}=El(),{user:r}=Wf(),o=a=>a.split(" ").map(l=>l[0]).join("").toUpperCase().slice(0,2),s=()=>{t({to:"/user"})},i=()=>{n(),t({to:"/auth/login"})};return h.jsxs(XS,{children:[h.jsx(qS,{asChild:!0,children:h.jsx(Ce,{variant:"ghost",size:"sm",className:`h-8 w-8 rounded-full p-0 ${e}`,children:h.jsxs(bl,{className:"h-8 w-8",children:[h.jsx(wl,{src:r?.avatar||"",alt:r?.name||"用户"}),h.jsx(Sl,{className:"text-xs",children:o(r?.name||"用户")})]})})}),h.jsxs(Nf,{align:"end",className:"w-56",children:[h.jsx(jf,{className:"font-normal",children:h.jsx("div",{className:"flex flex-col space-y-1",children:h.jsx("p",{className:"text-sm font-medium leading-none",children:r?.name||"用户"})})}),h.jsx(fs,{}),h.jsxs(ds,{onClick:s,children:[h.jsx(Bf,{className:"mr-2 h-4 w-4"}),h.jsx("span",{children:"账号管理"})]}),h.jsx(fs,{}),h.jsxs(ds,{onClick:i,children:[h.jsx(ah,{className:"mr-2 h-4 w-4"}),h.jsx("span",{children:"退出登录"})]})]})]})}function nC(){return h.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[h.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:h.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),h.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:h.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),h.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:h.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}function rC({variant:e="received",layout:t="default",className:n,children:r}){return h.jsx("div",{className:O("flex items-start gap-2 mb-4",e==="sent"&&"flex-row-reverse",n),children:r})}function oC({variant:e="received",isLoading:t,className:n,children:r}){return h.jsx("div",{className:O("rounded-lg p-3",e==="sent"?"bg-primary text-primary-foreground":"bg-muted",n),children:t?h.jsx("div",{className:"flex items-center space-x-2",children:h.jsx(nC,{})}):r})}function sC({src:e,fallback:t="AI",className:n}){return h.jsxs(bl,{className:O("h-8 w-8",n),children:[e&&h.jsx(wl,{src:e}),h.jsx(Sl,{children:t})]})}function iC({icon:e,onClick:t,className:n}){return h.jsx(Ce,{variant:"ghost",size:"icon",className:O("h-6 w-6",n),onClick:t,children:e})}function aC({className:e,children:t}){return h.jsx("div",{className:O("flex items-center gap-1 mt-2",e),children:t})}function lC({children:e,redirectTo:t="/auth/login"}){const{isAuthenticated:n,isLoading:r}=El(),o=xl();return de.useEffect(()=>{if(!r&&!n){const s=window.location.pathname;s!==t&&localStorage.setItem("redirectAfterLogin",s),o({to:t})}},[r,n,o,t]),r?h.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:h.jsxs("div",{className:"text-center",children:[h.jsx(Do,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),h.jsx("p",{className:"text-gray-600",children:"正在验证登录状态..."})]})}):n?h.jsx(h.Fragment,{children:e}):h.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:h.jsxs("div",{className:"text-center",children:[h.jsx(Do,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),h.jsx("p",{className:"text-gray-600",children:"正在跳转到登录页面..."})]})})}const gl=["知识库查询","方案改进"];function cC(){const[e,t]=d.useState(gl[0]),[n,r]=d.useState(!1),[o,s]=d.useState([]),[i,a]=d.useState(null),[l,u]=d.useState({}),c="sk-5ff58b88af7343b7bbb388079e1442f2",f="622fbd2ef57c413baafa29527d205414",[p,m]=d.useState(null),v=()=>{r(!n)},g=(S,C)=>{u(E=>{const T=E[S]||new Set,A=new Set(T);return A.has(C)?A.delete(C):A.add(C),{...E,[S]:A}})},y=async(S,C)=>{console.log("=== API调用调试信息 ==="),console.log("API_KEY:",c),console.log("APP_ID:",f),console.log("环境变量 VITE_DASHSCOPE_API_KEY:","sk-5ff58b88af7343b7bbb388079e1442f2"),console.log("环境变量 VITE_YOUR_APP_ID:","622fbd2ef57c413baafa29527d205414"),console.log("========================");const E=`https://dashscope.aliyuncs.com/api/v1/apps/${f}/completion`,T={input:{prompt:S},parameters:{},debug:{}};C&&(T.input.session_id=C);const A=await fetch(E,{method:"POST",headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"},body:JSON.stringify(T)});if(!A.ok)throw new Error(`API请求失败: ${A.status} ${A.statusText}`);return await A.json()},x=async S=>{const C={id:Date.now().toString(),content:S,sender:"user",timestamp:new Date};s(T=>[...T,C]);const E={id:(Date.now()+1).toString(),content:"",sender:"ai",timestamp:new Date,isLoading:!0};s(T=>[...T,E]);try{const T=await y(S,i||void 0);T.output.session_id&&a(T.output.session_id);const A={id:E.id,content:T.output.text,sender:"ai",timestamp:new Date,isLoading:!1,docReferences:T.output.doc_references};s(R=>R.map(P=>P.id===E.id?A:P))}catch(T){console.error("API调用失败:",T);const A={id:E.id,content:`抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${T instanceof Error?T.message:"未知错误"}`,sender:"ai",timestamp:new Date,isLoading:!1};s(R=>R.map(P=>P.id===E.id?A:P))}},w=async S=>{try{await navigator.clipboard.writeText(S),console.log("复制成功")}catch(C){console.error("复制失败:",C);const E=document.createElement("textarea");E.value=S,document.body.appendChild(E),E.select(),document.execCommand("copy"),document.body.removeChild(E)}},b=()=>{if(!p){alert("请先选择一个文件");return}alert("文件上传功能暂未实现")};return h.jsx(f0,{isCollapsed:n,onToggle:v,children:h.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[h.jsx("div",{className:"bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0",children:h.jsxs("div",{className:"flex items-center justify-between",children:[h.jsx(Ce,{variant:"ghost",size:"sm",onClick:v,className:"h-8 w-8 p-0",title:n?"展开侧边栏 (Ctrl+B)":"收起侧边栏 (Ctrl+B)",children:n?h.jsx(mh,{className:"h-4 w-4"}):h.jsx(hh,{className:"h-4 w-4"})}),h.jsx("div",{className:"flex justify-center flex-1",children:h.jsx("div",{className:"flex w-fit rounded-full bg-muted p-1",children:gl.map(S=>h.jsx(Sy,{text:S,selected:e===S,setSelected:t,discount:S==="方案改进"},S))})}),h.jsx(tC,{className:"ml-4"})]})}),h.jsx("main",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:e==="知识库查询"?h.jsxs(h.Fragment,{children:[h.jsx("div",{className:"flex-1 overflow-y-auto p-6 pb-0 min-h-0",children:o.length===0?h.jsxs("div",{className:"text-center text-gray-500 mt-20",children:[h.jsx("p",{className:"text-lg mb-2",children:"👋 欢迎使用库无忧"}),h.jsx("p",{children:"开始对话，快速检索石化油储行业"}),i&&h.jsxs("p",{className:"text-sm mt-4 text-blue-600",children:["🔗 多轮对话已启用 (会话ID: ",i.slice(0,8),"...)"]})]}):h.jsx("div",{className:"max-w-4xl mx-auto space-y-4",children:o.map(S=>{const C=S.sender==="user"?"sent":"received";return h.jsxs("div",{className:"space-y-2",children:[S.sender==="ai"&&S.docReferences&&S.docReferences.length>0&&h.jsx("div",{className:"space-y-2 mb-2 ml-12",children:h.jsx("div",{className:"flex flex-wrap gap-2",children:S.docReferences.map((E,T)=>{const A=l[S.id]?.has(T)||!1;return h.jsxs("div",{className:"space-y-1",children:[h.jsx(A0.Trigger,{content:E.doc_name,side:"top",children:h.jsx(Hf,{variant:"outline",className:"text-xs cursor-pointer hover:bg-gray-100",onClick:()=>g(S.id,T),children:E.index_id})}),A&&h.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-md border text-sm",children:[h.jsx("div",{className:"font-medium text-gray-700 mb-1",children:E.doc_name}),h.jsx("div",{className:"text-gray-600",children:E.text})]})]},T)})})}),h.jsxs(rC,{variant:C,children:[h.jsx(sC,{src:C==="sent"?"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=64&h=64&q=80&crop=faces&fit=crop":"https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=64&h=64&q=80&crop=faces&fit=crop",fallback:C==="sent"?"用户":"AI"}),h.jsxs("div",{className:"flex-1",children:[h.jsx(oC,{variant:C,isLoading:S.isLoading,children:S.content}),S.sender==="ai"&&!S.isLoading&&h.jsx(aC,{children:h.jsx(iC,{icon:h.jsx(rh,{className:"h-3 w-3"}),onClick:()=>w(S.content)})})]})]})]},S.id)})})}),h.jsx("div",{className:"w-full flex justify-center flex-shrink-0 p-6 pt-0",children:h.jsx("div",{className:"w-full max-w-3xl",children:h.jsx(xy,{onSend:x})})})]}):h.jsx(h.Fragment,{children:h.jsxs("div",{className:"flex-1 flex flex-col justify-center items-center p-6",children:[h.jsxs("div",{className:"text-center text-gray-500 mb-6",children:[h.jsx("p",{className:"text-lg mb-2",children:"📄 方案改进"}),h.jsx("p",{children:"上传文件进行智能分析和优化建议"})]}),h.jsxs("div",{className:"w-full max-w-md space-y-4",children:[h.jsx(wy,{onFileSelect:m}),p&&h.jsxs("div",{className:"text-center text-sm text-gray-600 bg-gray-50 p-3 rounded-md",children:[h.jsxs("p",{children:["已选择文件: ",h.jsx("span",{className:"font-medium",children:p.name})]}),h.jsxs("p",{children:["文件大小: ",(p.size/1024/1024).toFixed(2)," MB"]})]}),h.jsx(Ce,{onClick:b,disabled:!p,className:"w-full",children:"上传文件（演示）"})]})]})})})]})})}const AC=function(){return h.jsx(lC,{children:h.jsx(cC,{})})};export{AC as component};
