import{r as o}from"./main-DKMnXhyP.js";function m(){const[n,t]=o.useState(null),[c,u]=o.useState(!0),[g,r]=o.useState(!1),l=()=>{try{const e=localStorage.getItem("user"),s=localStorage.getItem("authToken");if(e&&s){const a=JSON.parse(e);t(a),r(!0)}else t(null),r(!1)}catch(e){console.error("检查认证状态失败:",e),t(null),r(!1)}finally{u(!1)}},h=(e,s)=>{try{localStorage.setItem("user",JSON.stringify(e)),localStorage.setItem("authToken",s),t(e),r(!0)}catch(a){throw console.error("登录失败:",a),new Error("登录信息保存失败")}},i=()=>{try{localStorage.removeItem("user"),localStorage.removeItem("authToken"),t(null),r(!1)}catch(e){console.error("登出失败:",e)}},S=()=>{try{return localStorage.getItem("authToken")}catch(e){return console.error("获取认证令牌失败:",e),null}};return o.useEffect(()=>{l()},[]),{user:n,isLoading:c,isAuthenticated:g,login:h,logout:i,getAuthToken:S,checkAuthStatus:l}}export{m as u};
