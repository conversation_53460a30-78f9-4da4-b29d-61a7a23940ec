import{r as s,j as d}from"./main-CJJWzuV9.js";import{c as h,a as m}from"./index-DFx3G0N8.js";function p(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function g(...e){return t=>{let n=!1;const o=e.map(r=>{const i=p(r,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let r=0;r<o.length;r++){const i=o[r];typeof i=="function"?i():p(e[r],null)}}}}function _(...e){return s.useCallback(g(...e),e)}function y(e){const t=S(e),n=s.forwardRef((o,r)=>{const{children:i,...l}=o,a=s.Children.toArray(i),c=a.find(w);if(c){const u=c.props.children,v=a.map(f=>f===c?s.Children.count(u)>1?s.Children.only(null):s.isValidElement(u)?u.props.children:null:f);return d.jsx(t,{...l,ref:r,children:s.isValidElement(u)?s.cloneElement(u,void 0,v):null})}return d.jsx(t,{...l,ref:r,children:i})});return n.displayName=`${e}.Slot`,n}var x=y("Slot");function S(e){const t=s.forwardRef((n,o)=>{const{children:r,...i}=n;if(s.isValidElement(r)){const l=E(r),a=C(i,r.props);return r.type!==s.Fragment&&(a.ref=o?g(o,l):l),s.cloneElement(r,a)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var b=Symbol("radix.slottable");function V(e){const t=({children:n})=>d.jsx(d.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=b,t}function w(e){return s.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===b}function C(e,t){const n={...t};for(const o in t){const r=e[o],i=t[o];/^on[A-Z]/.test(o)?r&&i?n[o]=(...a)=>{const c=i(...a);return r(...a),c}:r&&(n[o]=r):o==="style"?n[o]={...r,...i}:o==="className"&&(n[o]=[r,i].filter(Boolean).join(" "))}return{...e,...n}}function E(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const k=m("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",glow:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function I({className:e,variant:t,size:n,asChild:o=!1,...r}){const i=o?x:"button";return d.jsx(i,{"data-slot":"button",className:h(k({variant:t,size:n,className:e})),...r})}export{I as B,x as S,V as a,g as b,y as c,_ as u};
