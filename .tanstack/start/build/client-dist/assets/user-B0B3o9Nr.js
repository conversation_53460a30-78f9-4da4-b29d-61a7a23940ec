import{g as Qr}from"./main-DKMnXhyP.js";class R extends Error{code;rawCode;constructor(e,s,r,n){s!==void 0&&(e=`${s}: ${e}`),super(e,{cause:n}),this.code=s,this.rawCode=r,this.name="LibsqlError"}}function Er(t){const e=Or.exec(t);if(e===null)throw new R(`The URL '${t}' is not in a valid format`,"URL_INVALID");const s=e.groups,r=s.scheme,n=s.authority!==void 0?Pr(s.authority):void 0,i=de(s.path),o=s.query!==void 0?Dr(s.query):void 0,a=s.fragment!==void 0?de(s.fragment):void 0;return{scheme:r,authority:n,path:i,query:o,fragment:a}}const Or=(()=>{const t="(?<scheme>[A-Za-z][A-Za-z.+-]*)",e="(?<authority>[^/?#]*)",s="(?<path>[^?#]*)",r="(?<query>[^#]*)",n="(?<fragment>.*)";return new RegExp(`^${t}:(//${e})?${s}(\\?${r})?(#${n})?$`,"su")})();function Pr(t){const e=Ur.exec(t);if(e===null)throw new R("The authority part of the URL is not in a valid format","URL_INVALID");const s=e.groups,r=de(s.host_br??s.host),n=s.port?parseInt(s.port,10):void 0,i=s.username!==void 0?{username:de(s.username),password:s.password!==void 0?de(s.password):void 0}:void 0;return{host:r,port:n,userinfo:i}}const Ur=new RegExp("^((?<username>[^:]*)(:(?<password>.*))?@)?((?<host>[^:\\[\\]]*)|(\\[(?<host_br>[^\\[\\]]*)\\]))(:(?<port>[0-9]*))?$","su");function Dr(t){const e=t.split("&"),s=[];for(const r of e){if(r==="")continue;let n,i;const o=r.indexOf("=");o<0?(n=r,i=""):(n=r.substring(0,o),i=r.substring(o+1)),s.push({key:de(n.replaceAll("+"," ")),value:de(i.replaceAll("+"," "))})}return{pairs:s}}function de(t){try{return decodeURIComponent(t)}catch(e){throw e instanceof URIError?new R(`URL component has invalid percent encoding: ${e}`,"URL_INVALID",void 0,e):e}}function qt(t,e,s){if(e===void 0)throw new R(`URL with scheme ${JSON.stringify(t+":")} requires authority (the "//" part)`,"URL_INVALID");const r=`${t}:`,n=kr(e.host),i=Mr(e.port),a=`//${jr(e.userinfo)}${n}${i}`;let l=s.split("/").map(encodeURIComponent).join("/");return l!==""&&!l.startsWith("/")&&(l="/"+l),new URL(`${r}${a}${l}`)}function kr(t){return t.includes(":")?`[${encodeURI(t)}]`:encodeURI(t)}function Mr(t){return t!==void 0?`:${t}`:""}function jr(t){if(t===void 0)return"";const e=encodeURIComponent(t.username),s=t.password!==void 0?`:${encodeURIComponent(t.password)}`:"";return`${e}${s}@`}const Ls="3.7.7",Fr=Ls,Le=typeof Buffer=="function",is=typeof TextDecoder=="function"?new TextDecoder:void 0,os=typeof TextEncoder=="function"?new TextEncoder:void 0,Vr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ae=Array.prototype.slice.call(Vr),Ke=(t=>{let e={};return t.forEach((s,r)=>e[s]=r),e})(Ae),zr=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,A=String.fromCharCode.bind(String),as=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):t=>new Uint8Array(Array.prototype.slice.call(t,0)),Ns=t=>t.replace(/=/g,"").replace(/[+\/]/g,e=>e=="+"?"-":"_"),vs=t=>t.replace(/[^A-Za-z0-9\+\/]/g,""),$s=t=>{let e,s,r,n,i="";const o=t.length%3;for(let a=0;a<t.length;){if((s=t.charCodeAt(a++))>255||(r=t.charCodeAt(a++))>255||(n=t.charCodeAt(a++))>255)throw new TypeError("invalid character found");e=s<<16|r<<8|n,i+=Ae[e>>18&63]+Ae[e>>12&63]+Ae[e>>6&63]+Ae[e&63]}return o?i.slice(0,o-3)+"===".substring(o):i},Dt=typeof btoa=="function"?t=>btoa(t):Le?t=>Buffer.from(t,"binary").toString("base64"):$s,_t=Le?t=>Buffer.from(t).toString("base64"):t=>{let s=[];for(let r=0,n=t.length;r<n;r+=4096)s.push(A.apply(null,t.subarray(r,r+4096)));return Dt(s.join(""))},Ge=(t,e=!1)=>e?Ns(_t(t)):_t(t),Kr=t=>{if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?A(192|e>>>6)+A(128|e&63):A(224|e>>>12&15)+A(128|e>>>6&63)+A(128|e&63)}else{var e=65536+(t.charCodeAt(0)-55296)*1024+(t.charCodeAt(1)-56320);return A(240|e>>>18&7)+A(128|e>>>12&63)+A(128|e>>>6&63)+A(128|e&63)}},Jr=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,As=t=>t.replace(Jr,Kr),ls=Le?t=>Buffer.from(t,"utf8").toString("base64"):os?t=>_t(os.encode(t)):t=>Dt(As(t)),_e=(t,e=!1)=>e?Ns(ls(t)):ls(t),us=t=>_e(t,!0),Wr=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Hr=t=>{switch(t.length){case 4:var e=(7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3),s=e-65536;return A((s>>>10)+55296)+A((s&1023)+56320);case 3:return A((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return A((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},Bs=t=>t.replace(Wr,Hr),Qs=t=>{if(t=t.replace(/\s+/g,""),!zr.test(t))throw new TypeError("malformed base64.");t+="==".slice(2-(t.length&3));let e,s="",r,n;for(let i=0;i<t.length;)e=Ke[t.charAt(i++)]<<18|Ke[t.charAt(i++)]<<12|(r=Ke[t.charAt(i++)])<<6|(n=Ke[t.charAt(i++)]),s+=r===64?A(e>>16&255):n===64?A(e>>16&255,e>>8&255):A(e>>16&255,e>>8&255,e&255);return s},kt=typeof atob=="function"?t=>atob(vs(t)):Le?t=>Buffer.from(t,"base64").toString("binary"):Qs,Es=Le?t=>as(Buffer.from(t,"base64")):t=>as(kt(t).split("").map(e=>e.charCodeAt(0))),Os=t=>Es(Ps(t)),Gr=Le?t=>Buffer.from(t,"base64").toString("utf8"):is?t=>is.decode(Es(t)):t=>Bs(kt(t)),Ps=t=>vs(t.replace(/[-_]/g,e=>e=="-"?"+":"/")),Ct=t=>Gr(Ps(t)),Yr=t=>{if(typeof t!="string")return!1;const e=t.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(e)||!/[^\s0-9a-zA-Z\-_]/.test(e)},Us=t=>({value:t,enumerable:!1,writable:!0,configurable:!0}),Ds=function(){const t=(e,s)=>Object.defineProperty(String.prototype,e,Us(s));t("fromBase64",function(){return Ct(this)}),t("toBase64",function(e){return _e(this,e)}),t("toBase64URI",function(){return _e(this,!0)}),t("toBase64URL",function(){return _e(this,!0)}),t("toUint8Array",function(){return Os(this)})},ks=function(){const t=(e,s)=>Object.defineProperty(Uint8Array.prototype,e,Us(s));t("toBase64",function(e){return Ge(this,e)}),t("toBase64URI",function(){return Ge(this,!0)}),t("toBase64URL",function(){return Ge(this,!0)})},Zr=()=>{Ds(),ks()},Mt={version:Ls,VERSION:Fr,atob:kt,atobPolyfill:Qs,btoa:Dt,btoaPolyfill:$s,fromBase64:Ct,toBase64:_e,encode:_e,encodeURI:us,encodeURL:us,utob:As,btou:Bs,decode:Ct,isValid:Yr,fromUint8Array:Ge,toUint8Array:Os,extendString:Ds,extendUint8Array:ks,extendBuiltins:Zr},Pe="https://github.com/libsql/libsql-client-ts#supported-urls";function xt(t){if(t==="write")return"BEGIN IMMEDIATE";if(t==="read")return"BEGIN TRANSACTION READONLY";if(t==="deferred")return"BEGIN DEFERRED";throw RangeError('Unknown transaction mode, supported values are "write", "read" and "deferred"')}class Xr{columns;columnTypes;rows;rowsAffected;lastInsertRowid;constructor(e,s,r,n,i){this.columns=e,this.columnTypes=s,this.rows=r,this.rowsAffected=n,this.lastInsertRowid=i}toJSON(){return{columns:this.columns,columnTypes:this.columnTypes,rows:this.rows.map(en),rowsAffected:this.rowsAffected,lastInsertRowid:this.lastInsertRowid!==void 0?""+this.lastInsertRowid:null}}}function en(t){return Array.prototype.map.call(t,tn)}function tn(t){return typeof t=="bigint"?""+t:t instanceof ArrayBuffer?Mt.fromUint8Array(new Uint8Array(t)):t}const cs=":memory:";function sn(t,e){if(typeof t!="object")throw new TypeError(`Expected client configuration as object, got ${typeof t}`);let{url:s,authToken:r,tls:n,intMode:i,concurrency:o}=t;o=Math.max(0,o||20),i??="number";let a=[];s===cs&&(s="file::memory:");const l=Er(s),h=l.scheme.toLowerCase(),c=h==="file"&&l.path===cs&&l.authority===void 0;let p;c?p={cache:{values:["shared","private"],update:(w,C)=>a.push(`${w}=${C}`)}}:p={tls:{values:["0","1"],update:(w,C)=>n=C==="1"},authToken:{update:(w,C)=>r=C}};for(const{key:w,value:C}of l.query?.pairs??[]){if(!Object.hasOwn(p,w))throw new R(`Unsupported URL query parameter ${JSON.stringify(w)}`,"URL_PARAM_NOT_SUPPORTED");const T=p[w];if(T.values!==void 0&&!T.values.includes(C))throw new R(`Unknown value for the "${w}" query argument: ${JSON.stringify(C)}. Supported values are: [${T.values.map(O=>'"'+O+'"').join(", ")}]`,"URL_INVALID");T.update!==void 0&&T?.update(w,C)}const y=a.length===0?"":`?${a.join("&")}`,_=l.path+y;let S;if(h==="libsql")if(n===!1){if(l.authority?.port===void 0)throw new R('A "libsql:" URL with ?tls=0 must specify an explicit port',"URL_INVALID");S="http"}else S="https";else S=h;if(S==="http"||S==="ws"?n??=!1:n??=!0,S!=="http"&&S!=="ws"&&S!=="https"&&S!=="wss"&&S!=="file")throw new R(`The client supports only "libsql:", "wss:", "ws:", "https:", "http:" and "file:" URLs, got ${JSON.stringify(l.scheme+":")}. For more information, please read ${Pe}`,"URL_SCHEME_NOT_SUPPORTED");if(i!=="number"&&i!=="bigint"&&i!=="string")throw new TypeError(`Invalid value for intMode, expected "number", "bigint" or "string", got ${JSON.stringify(i)}`);if(l.fragment!==void 0)throw new R(`URL fragments are not supported: ${JSON.stringify("#"+l.fragment)}`,"URL_INVALID");return c?{scheme:"file",tls:!1,path:_,intMode:i,concurrency:o,syncUrl:t.syncUrl,syncInterval:t.syncInterval,readYourWrites:t.readYourWrites,offline:t.offline,fetch:t.fetch,authToken:void 0,encryptionKey:void 0,authority:void 0}:{scheme:S,tls:n,authority:l.authority,path:_,authToken:r,intMode:i,concurrency:o,encryptionKey:t.encryptionKey,syncUrl:t.syncUrl,syncInterval:t.syncInterval,readYourWrites:t.readYourWrites,offline:t.offline,fetch:t.fetch}}let ge;typeof WebSocket<"u"?ge=WebSocket:typeof global<"u"?ge=global.WebSocket:typeof window<"u"?ge=window.WebSocket:typeof self<"u"&&(ge=self.WebSocket);class Ms{constructor(){this.intMode="number"}intMode}class L extends Error{constructor(e){super(e),this.name="ClientError"}}class q extends L{constructor(e){super(e),this.name="ProtoError"}}class js extends L{code;proto;constructor(e,s){super(e),this.name="ResponseError",this.code=s.code,this.proto=s,this.stack=void 0}}class F extends L{constructor(e,s){s!==void 0?(super(`${e}: ${s}`),this.cause=s):super(e),this.name="ClosedError"}}class Fs extends L{constructor(e){super(e),this.name="WebSocketUnsupportedError"}}class Rt extends L{constructor(e){super(e),this.name="WebSocketError"}}class Ye extends L{status;constructor(e,s){super(e),this.status=s,this.name="HttpServerError"}}class Ce extends L{constructor(e){super(e),this.name="ProtocolVersionError"}}class ae extends L{constructor(e){super(e),this.name="InternalError"}}class Ne extends L{constructor(e){super(e),this.name="MisuseError"}}function J(t){if(typeof t=="string")return t;throw ve(t,"string")}function W(t){if(t!=null){if(typeof t=="string")return t;throw ve(t,"string or null")}}function fe(t){if(typeof t=="number")return t;throw ve(t,"number")}function Ue(t){if(typeof t=="boolean")return t;throw ve(t,"boolean")}function et(t){if(Array.isArray(t))return t;throw ve(t,"array")}function Q(t){if(t!==null&&typeof t=="object"&&!Array.isArray(t))return t;throw ve(t,"object")}function le(t,e){return et(t).map(s=>e(Q(s)))}function ve(t,e){if(t===void 0)return new q(`Expected ${e}, but the property was missing`);let s=typeof t;return t===null?s="null":Array.isArray(t)&&(s="array"),new q(`Expected ${e}, received ${s}`)}function jt(t,e){return e(Q(t))}class rn{#e;#t;constructor(e){this.#e=e,this.#t=!1}begin(){this.#e.push("{"),this.#t=!0}end(){this.#e.push("}"),this.#t=!1}#s(e){this.#t?(this.#e.push('"'),this.#t=!1):this.#e.push(',"'),this.#e.push(e),this.#e.push('":')}string(e,s){this.#s(e),this.#e.push(JSON.stringify(s))}stringRaw(e,s){this.#s(e),this.#e.push('"'),this.#e.push(s),this.#e.push('"')}number(e,s){this.#s(e),this.#e.push(""+s)}boolean(e,s){this.#s(e),this.#e.push(s?"true":"false")}object(e,s,r){this.#s(e),this.begin(),r(this,s),this.end()}arrayObjects(e,s,r){this.#s(e),this.#e.push("[");for(let n=0;n<s.length;++n)n!==0&&this.#e.push(","),this.begin(),r(this,s[n]),this.end();this.#e.push("]")}}function Vs(t,e){const s=[],r=new rn(s);return r.begin(),e(r,t),r.end(),s.join("")}const Ee=0,Tt=1,It=2,nn=5;class on{#e;#t;#s;constructor(e){this.#e=e,this.#t=new DataView(e.buffer,e.byteOffset,e.byteLength),this.#s=0}varint(){let e=0;for(let s=0;;s+=7){const r=this.#e[this.#s++];if(e|=(r&127)<<s,!(r&128))break}return e}varintBig(){let e=0n;for(let s=0n;;s+=7n){const r=this.#e[this.#s++];if(e|=BigInt(r&127)<<s,!(r&128))break}return e}bytes(e){const s=new Uint8Array(this.#e.buffer,this.#e.byteOffset+this.#s,e);return this.#s+=e,s}double(){const e=this.#t.getFloat64(this.#s,!0);return this.#s+=8,e}skipVarint(){for(;this.#e[this.#s++]&128;);}skip(e){this.#s+=e}eof(){return this.#s>=this.#e.byteLength}}class an{#e;#t;constructor(e){this.#e=e,this.#t=-1}setup(e){this.#t=e}#s(e){if(this.#t!==e)throw new q(`Expected wire type ${e}, got ${this.#t}`);this.#t=-1}bytes(){this.#s(It);const e=this.#e.varint();return this.#e.bytes(e)}string(){return new TextDecoder().decode(this.bytes())}message(e){return ht(this.bytes(),e)}int32(){return this.#s(Ee),this.#e.varint()}uint32(){return this.int32()}bool(){return this.int32()!==0}uint64(){return this.#s(Ee),this.#e.varintBig()}sint64(){const e=this.uint64();return e>>1n^-(e&1n)}double(){return this.#s(Tt),this.#e.double()}maybeSkip(){if(!(this.#t<0)){if(this.#t===Ee)this.#e.skipVarint();else if(this.#t===Tt)this.#e.skip(8);else if(this.#t===It){const e=this.#e.varint();this.#e.skip(e)}else if(this.#t===nn)this.#e.skip(4);else throw new q(`Unexpected wire type ${this.#t}`);this.#t=-1}}}function ht(t,e){const s=new on(t),r=new an(s);let n=e.default();for(;!s.eof();){const i=s.varint(),o=i>>3,a=i&7;r.setup(a);const l=e[o];if(l!==void 0){const h=l(r,n);h!==void 0&&(n=h)}r.maybeSkip()}return n}class Ft{#e;#t;#s;#r;constructor(){this.#e=new ArrayBuffer(256),this.#t=new Uint8Array(this.#e),this.#s=new DataView(this.#e),this.#r=0}#i(e){if(this.#r+e<=this.#e.byteLength)return;let s=this.#e.byteLength;for(;s<this.#r+e;)s*=2;const r=new ArrayBuffer(s),n=new Uint8Array(r),i=new DataView(r);n.set(new Uint8Array(this.#e,0,this.#r)),this.#e=r,this.#t=n,this.#s=i}#n(e){this.#i(5),e=0|e;do{let s=e&127;e>>>=7,s|=e?128:0,this.#t[this.#r++]=s}while(e)}#a(e){this.#i(10),e=e&0xffffffffffffffffn;do{let s=Number(e&0x7fn);e>>=7n,s|=e?128:0,this.#t[this.#r++]=s}while(e)}#o(e,s){this.#n(e<<3|s)}bytes(e,s){this.#o(e,It),this.#n(s.byteLength),this.#i(s.byteLength),this.#t.set(s,this.#r),this.#r+=s.byteLength}string(e,s){this.bytes(e,new TextEncoder().encode(s))}message(e,s,r){const n=new Ft;r(n,s),this.bytes(e,n.data())}int32(e,s){this.#o(e,Ee),this.#n(s)}uint32(e,s){this.int32(e,s)}bool(e,s){this.int32(e,s?1:0)}sint64(e,s){this.#o(e,Ee),this.#a(s<<1n^s>>63n)}double(e,s){this.#o(e,Tt),this.#i(8),this.#s.setFloat64(this.#r,s,!0),this.#r+=8}data(){return new Uint8Array(this.#e,0,this.#r)}}function zs(t,e){const s=new Ft;return e(s,t),s.data()}class Be{#e;#t;constructor(){this.#e=new Set,this.#t=new Set}alloc(){for(const s of this.#t)return this.#t.delete(s),this.#e.add(s),this.#e.has(this.#e.size-1)||this.#t.add(this.#e.size-1),s;const e=this.#e.size;return this.#e.add(e),e}free(e){if(!this.#e.delete(e))throw new ae("Freeing an id that is not allocated");this.#t.delete(this.#e.size),e<this.#e.size&&this.#t.add(e)}}function v(t,e){throw new ae(e)}function Oe(t){if(t===null)return null;if(typeof t=="string")return t;if(typeof t=="number"){if(!Number.isFinite(t))throw new RangeError("Only finite numbers (not Infinity or NaN) can be passed as arguments");return t}else if(typeof t=="bigint"){if(t<ln||t>un)throw new RangeError("This bigint value is too large to be represented as a 64-bit integer and passed as argument");return t}else{if(typeof t=="boolean")return t?1n:0n;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return t;if(t instanceof Date)return+t.valueOf();if(typeof t=="object")return""+t.toString();throw new TypeError("Unsupported type of value")}}const ln=-9223372036854775808n,un=9223372036854775807n;function Ks(t,e){if(t===null)return null;if(typeof t=="number")return t;if(typeof t=="string")return t;if(typeof t=="bigint")if(e==="number"){const s=Number(t);if(!Number.isSafeInteger(s))throw new RangeError("Received integer which is too large to be safely represented as a JavaScript number");return s}else{if(e==="bigint")return t;if(e==="string")return""+t;throw new Ne("Invalid value for IntMode")}else{if(t instanceof Uint8Array)return t.slice().buffer;throw t===void 0?new q("Received unrecognized type of Value"):v(t,"Impossible type of Value")}}function ke(t){return{affectedRowCount:t.affectedRowCount,lastInsertRowid:t.lastInsertRowid,columnNames:t.cols.map(e=>e.name),columnDecltypes:t.cols.map(e=>e.decltype)}}function Js(t,e){const s=ke(t),r=t.rows.map(n=>Gs(s.columnNames,n,e));return{...s,rows:r}}function Ws(t,e){const s=ke(t);let r;return t.rows.length>0&&(r=Gs(s.columnNames,t.rows[0],e)),{...s,row:r}}function Hs(t,e){const s=ke(t);let r;return t.rows.length>0&&s.columnNames.length>0&&(r=Ks(t.rows[0][0],e)),{...s,value:r}}function Gs(t,e,s){const r={};Object.defineProperty(r,"length",{value:e.length});for(let n=0;n<e.length;++n){const i=Ks(e[n],s);Object.defineProperty(r,n,{value:i});const o=t[n];o!==void 0&&!Object.hasOwn(r,o)&&Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0})}return r}function xe(t){return new js(t.message,t)}class Vt{#e;#t;#s;constructor(e,s){this.#e=e,this.#t=s,this.#s=void 0}_getSqlId(e){if(this.#e!==e)throw new Ne("Attempted to use SQL text opened with other object");if(this.#s!==void 0)throw new F("SQL text is closed",this.#s);return this.#t}close(){this._setClosed(new L("SQL text was manually closed"))}_setClosed(e){this.#s===void 0&&(this.#s=e,this.#e._closeSql(this.#t))}get closed(){return this.#s!==void 0}}function Lt(t,e){return e instanceof Vt?{sqlId:e._getSqlId(t)}:{sql:""+e}}class tt{#e;#t;constructor(){this.#e=[],this.#t=[]}get length(){return this.#e.length+this.#t.length}push(e){this.#e.push(e)}shift(){return this.#t.length===0&&this.#e.length>0&&(this.#t=this.#e.reverse(),this.#e=[]),this.#t.pop()}first(){return this.#t.length!==0?this.#t[this.#t.length-1]:this.#e[0]}}let Ys=class{sql;_args;_namedArgs;constructor(e){this.sql=e,this._args=[],this._namedArgs=new Map}bindIndexes(e){this._args.length=0;for(const s of e)this._args.push(Oe(s));return this}bindIndex(e,s){if(e!==(e|0)||e<=0)throw new RangeError("Index of a positional argument must be positive integer");for(;this._args.length<e;)this._args.push(null);return this._args[e-1]=Oe(s),this}bindName(e,s){return this._namedArgs.set(e,Oe(s)),this}unbindAll(){return this._args.length=0,this._namedArgs.clear(),this}};function Zs(t,e,s){let r,n=[],i=[];if(e instanceof Ys){r=e.sql,n=e._args;for(const[l,h]of e._namedArgs.entries())i.push({name:l,value:h})}else Array.isArray(e)?(r=e[0],Array.isArray(e[1])?n=e[1].map(l=>Oe(l)):i=Object.entries(e[1]).map(([l,h])=>({name:l,value:Oe(h)}))):r=e;const{sql:o,sqlId:a}=Lt(t,r);return{sql:o,sqlId:a,args:n,namedArgs:i,wantRows:s}}let cn=class{_stream;#e;_steps;#t;constructor(e,s){this._stream=e,this.#e=s,this._steps=[],this.#t=!1}step(){return new fn(this)}execute(){if(this.#t)throw new Ne("This batch has already been executed");this.#t=!0;const e={steps:this._steps.map(s=>s.proto)};return this.#e?dn(this._stream,this._steps,e):hn(this._stream,this._steps,e)}};function hn(t,e,s){return t._batch(s).then(r=>{for(let n=0;n<e.length;++n){const i=r.stepResults.get(n),o=r.stepErrors.get(n);e[n].callback(i,o)}})}async function dn(t,e,s){const r=await t._openCursor(s);try{let n=0,i,o=[];for(;;){const a=await r.next();if(a===void 0)break;if(a.type==="step_begin"){if(a.step<n||a.step>=e.length)throw new q("Server produced StepBeginEntry for unexpected step");if(i!==void 0)throw new q("Server produced StepBeginEntry before terminating previous step");for(let l=n;l<a.step;++l)e[l].callback(void 0,void 0);n=a.step+1,i=a,o=[]}else if(a.type==="step_end"){if(i===void 0)throw new q("Server produced StepEndEntry but no step is active");const l={cols:i.cols,rows:o,affectedRowCount:a.affectedRowCount,lastInsertRowid:a.lastInsertRowid};e[i.step].callback(l,void 0),i=void 0,o=[]}else if(a.type==="step_error"){if(i===void 0){if(a.step>=e.length)throw new q("Server produced StepErrorEntry for unexpected step");for(let l=n;l<a.step;++l)e[l].callback(void 0,void 0)}else{if(a.step!==i.step)throw new q("Server produced StepErrorEntry for unexpected step");i=void 0,o=[]}e[a.step].callback(void 0,a.error),n=a.step+1}else if(a.type==="row"){if(i===void 0)throw new q("Server produced RowEntry but no step is active");o.push(a.row)}else throw a.type==="error"?xe(a.error):a.type==="none"?new q("Server produced unrecognized CursorEntry"):v(a,"Impossible CursorEntry")}if(i!==void 0)throw new q("Server closed Cursor before terminating active step");for(let a=n;a<e.length;++a)e[a].callback(void 0,void 0)}finally{r.close()}}let fn=class{_batch;#e;_index;constructor(e){this._batch=e,this.#e=[],this._index=void 0}condition(e){return this.#e.push(e._proto),this}query(e){return this.#t(e,!0,Js)}queryRow(e){return this.#t(e,!0,Ws)}queryValue(e){return this.#t(e,!0,Hs)}run(e){return this.#t(e,!1,ke)}#t(e,s,r){if(this._index!==void 0)throw new Ne("This BatchStep has already been added to the batch");const n=Zs(this._batch._stream._sqlOwner(),e,s);let i;this.#e.length===0?i=void 0:this.#e.length===1?i=this.#e[0]:i={type:"and",conds:this.#e.slice()};const o={stmt:n,condition:i};return new Promise((a,l)=>{const h=(c,p)=>{c!==void 0&&p!==void 0?l(new q("Server returned both result and error")):p!==void 0?l(xe(p)):a(c!==void 0?r(c,this._batch._stream.intMode):void 0)};this._index=this._batch._steps.length,this._batch._steps.push({proto:o,callback:h})})}},D=class ce{_batch;_proto;constructor(e,s){this._batch=e,this._proto=s}static ok(e){return new ce(e._batch,{type:"ok",step:hs(e)})}static error(e){return new ce(e._batch,{type:"error",step:hs(e)})}static not(e){return new ce(e._batch,{type:"not",cond:e._proto})}static and(e,s){for(const r of s)ds(e,r);return new ce(e,{type:"and",conds:s.map(r=>r._proto)})}static or(e,s){for(const r of s)ds(e,r);return new ce(e,{type:"or",conds:s.map(r=>r._proto)})}static isAutocommit(e){return e._stream.client()._ensureVersion(3,"BatchCond.isAutocommit()"),new ce(e,{type:"is_autocommit"})}};function hs(t){if(t._index===void 0)throw new Ne("Cannot add a condition referencing a step that has not been added to the batch");return t._index}function ds(t,e){if(e._batch!==t)throw new Ne("Cannot mix BatchCond objects for different Batch objects")}function pn(t){return{paramNames:t.params.map(e=>e.name),columns:t.cols,isExplain:t.isExplain,isReadonly:t.isReadonly}}class Xs{constructor(e){this.intMode=e}query(e){return this.#e(e,!0,Js)}queryRow(e){return this.#e(e,!0,Ws)}queryValue(e){return this.#e(e,!0,Hs)}run(e){return this.#e(e,!1,ke)}#e(e,s,r){const n=Zs(this._sqlOwner(),e,s);return this._execute(n).then(i=>r(i,this.intMode))}batch(e=!1){return new cn(this,e)}describe(e){const s=Lt(this._sqlOwner(),e);return this._describe(s).then(pn)}sequence(e){const s=Lt(this._sqlOwner(),e);return this._sequence(s)}intMode}class er{}const mn=1e3,yn=10;class bn extends er{#e;#t;#s;#r;#i;#n;#a;constructor(e,s,r){super(),this.#e=e,this.#t=s,this.#s=r,this.#r=new tt,this.#i=new tt,this.#n=void 0,this.#a=!1}async next(){for(;;){if(this.#n!==void 0)throw new F("Cursor is closed",this.#n);for(;!this.#a&&this.#i.length<yn;)this.#i.push(this.#o());const e=this.#r.shift();if(this.#a||e!==void 0)return e;await this.#i.shift().then(s=>{if(s!==void 0){for(const r of s.entries)this.#r.push(r);this.#a||=s.done}})}}#o(){return this.#t._sendCursorRequest(this,{type:"fetch_cursor",cursorId:this.#s,maxCount:mn}).then(e=>e,e=>{this._setClosed(e)})}_setClosed(e){this.#n===void 0&&(this.#n=e,this.#t._sendCursorRequest(this,{type:"close_cursor",cursorId:this.#s}).catch(()=>{}),this.#t._cursorClosed(this))}close(){this._setClosed(new L("Cursor was manually closed"))}get closed(){return this.#n!==void 0}}class zt extends Xs{#e;#t;#s;#r;#i;#n;static open(e){const s=e._streamIdAlloc.alloc(),r=new zt(e,s),n=()=>{},i=a=>r.#u(a),o={type:"open_stream",streamId:s};return e._sendRequest(o,{responseCallback:n,errorCallback:i}),r}constructor(e,s){super(e.intMode),this.#e=e,this.#t=s,this.#s=new tt,this.#r=void 0,this.#i=!1,this.#n=void 0}client(){return this.#e}_sqlOwner(){return this.#e}_execute(e){return this.#a({type:"execute",streamId:this.#t,stmt:e}).then(s=>s.result)}_batch(e){return this.#a({type:"batch",streamId:this.#t,batch:e}).then(s=>s.result)}_describe(e){return this.#e._ensureVersion(2,"describe()"),this.#a({type:"describe",streamId:this.#t,sql:e.sql,sqlId:e.sqlId}).then(s=>s.result)}_sequence(e){return this.#e._ensureVersion(2,"sequence()"),this.#a({type:"sequence",streamId:this.#t,sql:e.sql,sqlId:e.sqlId}).then(s=>{})}getAutocommit(){return this.#e._ensureVersion(3,"getAutocommit()"),this.#a({type:"get_autocommit",streamId:this.#t}).then(e=>e.isAutocommit)}#a(e){return new Promise((s,r)=>{this.#o({type:"request",request:e,responseCallback:s,errorCallback:r})})}_openCursor(e){return this.#e._ensureVersion(3,"cursor"),new Promise((s,r)=>{this.#o({type:"cursor",batch:e,cursorCallback:s,errorCallback:r})})}_sendCursorRequest(e,s){if(e!==this.#r)throw new ae("Cursor not associated with the stream attempted to execute a request");return new Promise((r,n)=>{this.#n!==void 0?n(new F("Stream is closed",this.#n)):this.#e._sendRequest(s,{responseCallback:r,errorCallback:n})})}_cursorClosed(e){if(e!==this.#r)throw new ae("Cursor was closed, but it was not associated with the stream");this.#r=void 0,this.#l()}#o(e){this.#n!==void 0?e.errorCallback(new F("Stream is closed",this.#n)):this.#i?e.errorCallback(new F("Stream is closing",void 0)):(this.#s.push(e),this.#l())}#l(){for(;;){const e=this.#s.first();if(e===void 0&&this.#r===void 0&&this.#i){this.#u(new L("Stream was gracefully closed"));break}else if(e?.type==="request"&&this.#r===void 0){const{request:s,responseCallback:r,errorCallback:n}=e;this.#s.shift(),this.#e._sendRequest(s,{responseCallback:r,errorCallback:n})}else if(e?.type==="cursor"&&this.#r===void 0){const{batch:s,cursorCallback:r}=e;this.#s.shift();const n=this.#e._cursorIdAlloc.alloc(),i=new bn(this.#e,this,n),o={type:"open_cursor",streamId:this.#t,cursorId:n,batch:s},a=()=>{},l=h=>i._setClosed(h);this.#e._sendRequest(o,{responseCallback:a,errorCallback:l}),this.#r=i,r(i)}else break}}#u(e){if(this.#n!==void 0)return;for(this.#n=e,this.#r!==void 0&&this.#r._setClosed(e);;){const i=this.#s.shift();if(i!==void 0)i.errorCallback(e);else break}const s={type:"close_stream",streamId:this.#t},r=()=>this.#e._streamIdAlloc.free(this.#t),n=()=>{};this.#e._sendRequest(s,{responseCallback:r,errorCallback:n})}close(){this.#u(new L("Stream was manually closed"))}closeGracefully(){this.#i=!0,this.#l()}get closed(){return this.#n!==void 0||this.#i}}function Kt(t,e){e.sql!==void 0&&t.string("sql",e.sql),e.sqlId!==void 0&&t.number("sql_id",e.sqlId),t.arrayObjects("args",e.args,tr),t.arrayObjects("named_args",e.namedArgs,wn),t.boolean("want_rows",e.wantRows)}function wn(t,e){t.string("name",e.name),t.object("value",e.value,tr)}function st(t,e){t.arrayObjects("steps",e.steps,gn)}function gn(t,e){e.condition!==void 0&&t.object("condition",e.condition,Nt),t.object("stmt",e.stmt,Kt)}function Nt(t,e){if(t.stringRaw("type",e.type),e.type==="ok"||e.type==="error")t.number("step",e.step);else if(e.type==="not")t.object("cond",e.cond,Nt);else if(e.type==="and"||e.type==="or")t.arrayObjects("conds",e.conds,Nt);else if(e.type!=="is_autocommit")throw v(e,"Impossible type of BatchCond")}function tr(t,e){if(e===null)t.stringRaw("type","null");else if(typeof e=="bigint")t.stringRaw("type","integer"),t.stringRaw("value",""+e);else if(typeof e=="number")t.stringRaw("type","float"),t.number("value",e);else if(typeof e=="string")t.stringRaw("type","text"),t.string("value",e);else if(e instanceof Uint8Array)t.stringRaw("type","blob"),t.stringRaw("base64",Mt.fromUint8Array(e));else if(e!==void 0)throw v(e,"Impossible type of Value")}function Sn(t,e){if(t.stringRaw("type",e.type),e.type==="hello")e.jwt!==void 0&&t.string("jwt",e.jwt);else if(e.type==="request")t.number("request_id",e.requestId),t.object("request",e.request,qn);else throw v(e,"Impossible type of ClientMsg")}function qn(t,e){if(t.stringRaw("type",e.type),e.type==="open_stream")t.number("stream_id",e.streamId);else if(e.type==="close_stream")t.number("stream_id",e.streamId);else if(e.type==="execute")t.number("stream_id",e.streamId),t.object("stmt",e.stmt,Kt);else if(e.type==="batch")t.number("stream_id",e.streamId),t.object("batch",e.batch,st);else if(e.type==="open_cursor")t.number("stream_id",e.streamId),t.number("cursor_id",e.cursorId),t.object("batch",e.batch,st);else if(e.type==="close_cursor")t.number("cursor_id",e.cursorId);else if(e.type==="fetch_cursor")t.number("cursor_id",e.cursorId),t.number("max_count",e.maxCount);else if(e.type==="sequence")t.number("stream_id",e.streamId),e.sql!==void 0&&t.string("sql",e.sql),e.sqlId!==void 0&&t.number("sql_id",e.sqlId);else if(e.type==="describe")t.number("stream_id",e.streamId),e.sql!==void 0&&t.string("sql",e.sql),e.sqlId!==void 0&&t.number("sql_id",e.sqlId);else if(e.type==="store_sql")t.number("sql_id",e.sqlId),t.string("sql",e.sql);else if(e.type==="close_sql")t.number("sql_id",e.sqlId);else if(e.type==="get_autocommit")t.number("stream_id",e.streamId);else throw v(e,"Impossible type of Request")}function Jt(t,e){e.sql!==void 0&&t.string(1,e.sql),e.sqlId!==void 0&&t.int32(2,e.sqlId);for(const s of e.args)t.message(3,s,sr);for(const s of e.namedArgs)t.message(4,s,_n);t.bool(5,e.wantRows)}function _n(t,e){t.string(1,e.name),t.message(2,e.value,sr)}function dt(t,e){for(const s of e.steps)t.message(1,s,Cn)}function Cn(t,e){e.condition!==void 0&&t.message(1,e.condition,Wt),t.message(2,e.stmt,Jt)}function Wt(t,e){if(e.type==="ok")t.uint32(1,e.step);else if(e.type==="error")t.uint32(2,e.step);else if(e.type==="not")t.message(3,e.cond,Wt);else if(e.type==="and")t.message(4,e.conds,fs);else if(e.type==="or")t.message(5,e.conds,fs);else if(e.type==="is_autocommit")t.message(6,void 0,rr);else throw v(e,"Impossible type of BatchCond")}function fs(t,e){for(const s of e)t.message(1,s,Wt)}function sr(t,e){if(e===null)t.message(1,void 0,rr);else if(typeof e=="bigint")t.sint64(2,e);else if(typeof e=="number")t.double(3,e);else if(typeof e=="string")t.string(4,e);else if(e instanceof Uint8Array)t.bytes(5,e);else if(e!==void 0)throw v(e,"Impossible type of Value")}function rr(t,e){}function xn(t,e){if(e.type==="hello")t.message(1,e,Rn);else if(e.type==="request")t.message(2,e,Tn);else throw v(e,"Impossible type of ClientMsg")}function Rn(t,e){e.jwt!==void 0&&t.string(1,e.jwt)}function Tn(t,e){t.int32(1,e.requestId);const s=e.request;if(s.type==="open_stream")t.message(2,s,In);else if(s.type==="close_stream")t.message(3,s,Ln);else if(s.type==="execute")t.message(4,s,Nn);else if(s.type==="batch")t.message(5,s,vn);else if(s.type==="open_cursor")t.message(6,s,$n);else if(s.type==="close_cursor")t.message(7,s,An);else if(s.type==="fetch_cursor")t.message(8,s,Bn);else if(s.type==="sequence")t.message(9,s,Qn);else if(s.type==="describe")t.message(10,s,En);else if(s.type==="store_sql")t.message(11,s,On);else if(s.type==="close_sql")t.message(12,s,Pn);else if(s.type==="get_autocommit")t.message(13,s,Un);else throw v(s,"Impossible type of Request")}function In(t,e){t.int32(1,e.streamId)}function Ln(t,e){t.int32(1,e.streamId)}function Nn(t,e){t.int32(1,e.streamId),t.message(2,e.stmt,Jt)}function vn(t,e){t.int32(1,e.streamId),t.message(2,e.batch,dt)}function $n(t,e){t.int32(1,e.streamId),t.int32(2,e.cursorId),t.message(3,e.batch,dt)}function An(t,e){t.int32(1,e.cursorId)}function Bn(t,e){t.int32(1,e.cursorId),t.uint32(2,e.maxCount)}function Qn(t,e){t.int32(1,e.streamId),e.sql!==void 0&&t.string(2,e.sql),e.sqlId!==void 0&&t.int32(3,e.sqlId)}function En(t,e){t.int32(1,e.streamId),e.sql!==void 0&&t.string(2,e.sql),e.sqlId!==void 0&&t.int32(3,e.sqlId)}function On(t,e){t.int32(1,e.sqlId),t.string(2,e.sql)}function Pn(t,e){t.int32(1,e.sqlId)}function Un(t,e){t.int32(1,e.streamId)}function Re(t){const e=J(t.message),s=W(t.code);return{message:e,code:s}}function Ht(t){const e=le(t.cols,nr),s=et(t.rows).map(o=>le(o,lr)),r=fe(t.affected_row_count),n=W(t.last_insert_rowid),i=n!==void 0?BigInt(n):void 0;return{cols:e,rows:s,affectedRowCount:r,lastInsertRowid:i}}function nr(t){const e=W(t.name),s=W(t.decltype);return{name:e,decltype:s}}function ir(t){const e=new Map;et(t.step_results).forEach((r,n)=>{r!==null&&e.set(n,Ht(Q(r)))});const s=new Map;return et(t.step_errors).forEach((r,n)=>{r!==null&&s.set(n,Re(Q(r)))}),{stepResults:e,stepErrors:s}}function or(t){const e=J(t.type);if(e==="step_begin"){const s=fe(t.step),r=le(t.cols,nr);return{type:"step_begin",step:s,cols:r}}else if(e==="step_end"){const s=fe(t.affected_row_count),r=W(t.last_insert_rowid),n=r!==void 0?BigInt(r):void 0;return{type:"step_end",affectedRowCount:s,lastInsertRowid:n}}else if(e==="step_error"){const s=fe(t.step),r=Re(Q(t.error));return{type:"step_error",step:s,error:r}}else{if(e==="row")return{type:"row",row:le(t.row,lr)};if(e==="error")return{type:"error",error:Re(Q(t.error))};throw new q("Unexpected type of CursorEntry")}}function ar(t){const e=le(t.params,Dn),s=le(t.cols,kn),r=Ue(t.is_explain),n=Ue(t.is_readonly);return{params:e,cols:s,isExplain:r,isReadonly:n}}function Dn(t){return{name:W(t.name)}}function kn(t){const e=J(t.name),s=W(t.decltype);return{name:e,decltype:s}}function lr(t){const e=J(t.type);if(e==="null")return null;if(e==="integer"){const s=J(t.value);return BigInt(s)}else{if(e==="float")return fe(t.value);if(e==="text")return J(t.value);if(e==="blob")return Mt.toUint8Array(J(t.base64));throw new q("Unexpected type of Value")}}function Mn(t){const e=J(t.type);if(e==="hello_ok")return{type:"hello_ok"};if(e==="hello_error")return{type:"hello_error",error:Re(Q(t.error))};if(e==="response_ok"){const s=fe(t.request_id),r=jn(Q(t.response));return{type:"response_ok",requestId:s,response:r}}else if(e==="response_error"){const s=fe(t.request_id),r=Re(Q(t.error));return{type:"response_error",requestId:s,error:r}}else throw new q("Unexpected type of ServerMsg")}function jn(t){const e=J(t.type);if(e==="open_stream")return{type:"open_stream"};if(e==="close_stream")return{type:"close_stream"};if(e==="execute")return{type:"execute",result:Ht(Q(t.result))};if(e==="batch")return{type:"batch",result:ir(Q(t.result))};if(e==="open_cursor")return{type:"open_cursor"};if(e==="close_cursor")return{type:"close_cursor"};if(e==="fetch_cursor"){const s=le(t.entries,or),r=Ue(t.done);return{type:"fetch_cursor",entries:s,done:r}}else{if(e==="sequence")return{type:"sequence"};if(e==="describe")return{type:"describe",result:ar(Q(t.result))};if(e==="store_sql")return{type:"store_sql"};if(e==="close_sql")return{type:"close_sql"};if(e==="get_autocommit")return{type:"get_autocommit",isAutocommit:Ue(t.is_autocommit)};throw new q("Unexpected type of Response")}}const Y={default(){return{message:"",code:void 0}},1(t,e){e.message=t.string()},2(t,e){e.code=t.string()}},Te={default(){return{cols:[],rows:[],affectedRowCount:0,lastInsertRowid:void 0}},1(t,e){e.cols.push(t.message(ur))},2(t,e){e.rows.push(t.message(cr))},3(t,e){e.affectedRowCount=Number(t.uint64())},4(t,e){e.lastInsertRowid=t.sint64()}},ur={default(){return{name:void 0,decltype:void 0}},1(t,e){e.name=t.string()},2(t,e){e.decltype=t.string()}},cr={default(){return[]},1(t,e){e.push(t.message(Gn))}},rt={default(){return{stepResults:new Map,stepErrors:new Map}},1(t,e){const[s,r]=t.message(Fn);e.stepResults.set(s,r)},2(t,e){const[s,r]=t.message(Vn);e.stepErrors.set(s,r)}},Fn={default(){return[0,Te.default()]},1(t,e){e[0]=t.uint32()},2(t,e){e[1]=t.message(Te)}},Vn={default(){return[0,Y.default()]},1(t,e){e[0]=t.uint32()},2(t,e){e[1]=t.message(Y)}},hr={default(){return{type:"none"}},1(t){return t.message(zn)},2(t){return t.message(Kn)},3(t){return t.message(Jn)},4(t){return{type:"row",row:t.message(cr)}},5(t){return{type:"error",error:t.message(Y)}}},zn={default(){return{type:"step_begin",step:0,cols:[]}},1(t,e){e.step=t.uint32()},2(t,e){e.cols.push(t.message(ur))}},Kn={default(){return{type:"step_end",affectedRowCount:0,lastInsertRowid:void 0}},1(t,e){e.affectedRowCount=t.uint32()},2(t,e){e.lastInsertRowid=t.uint64()}},Jn={default(){return{type:"step_error",step:0,error:Y.default()}},1(t,e){e.step=t.uint32()},2(t,e){e.error=t.message(Y)}},nt={default(){return{params:[],cols:[],isExplain:!1,isReadonly:!1}},1(t,e){e.params.push(t.message(Wn))},2(t,e){e.cols.push(t.message(Hn))},3(t,e){e.isExplain=t.bool()},4(t,e){e.isReadonly=t.bool()}},Wn={default(){return{name:void 0}},1(t,e){e.name=t.string()}},Hn={default(){return{name:"",decltype:void 0}},1(t,e){e.name=t.string()},2(t,e){e.decltype=t.string()}},Gn={default(){},1(t){return null},2(t){return t.sint64()},3(t){return t.double()},4(t){return t.string()},5(t){return t.bytes()}},Yn={default(){return{type:"none"}},1(t){return{type:"hello_ok"}},2(t){return t.message(Zn)},3(t){return t.message(ei)},4(t){return t.message(Xn)}},Zn={default(){return{type:"hello_error",error:Y.default()}},1(t,e){e.error=t.message(Y)}},Xn={default(){return{type:"response_error",requestId:0,error:Y.default()}},1(t,e){e.requestId=t.int32()},2(t,e){e.error=t.message(Y)}},ei={default(){return{type:"response_ok",requestId:0,response:{type:"none"}}},1(t,e){e.requestId=t.int32()},2(t,e){e.response={type:"open_stream"}},3(t,e){e.response={type:"close_stream"}},4(t,e){e.response=t.message(ti)},5(t,e){e.response=t.message(si)},6(t,e){e.response={type:"open_cursor"}},7(t,e){e.response={type:"close_cursor"}},8(t,e){e.response=t.message(ri)},9(t,e){e.response={type:"sequence"}},10(t,e){e.response=t.message(ni)},11(t,e){e.response={type:"store_sql"}},12(t,e){e.response={type:"close_sql"}},13(t,e){e.response=t.message(ii)}},ti={default(){return{type:"execute",result:Te.default()}},1(t,e){e.result=t.message(Te)}},si={default(){return{type:"batch",result:rt.default()}},1(t,e){e.result=t.message(rt)}},ri={default(){return{type:"fetch_cursor",entries:[],done:!1}},1(t,e){e.entries.push(t.message(hr))},2(t,e){e.done=t.bool()}},ni={default(){return{type:"describe",result:nt.default()}},1(t,e){e.result=t.message(nt)}},ii={default(){return{type:"get_autocommit",isAutocommit:!1}},1(t,e){e.isAutocommit=t.bool()}},oi=new Map([["hrana2",{version:2,encoding:"json"}],["hrana1",{version:1,encoding:"json"}]]),dr=new Map([["hrana3-protobuf",{version:3,encoding:"protobuf"}],["hrana3",{version:3,encoding:"json"}],["hrana2",{version:2,encoding:"json"}],["hrana1",{version:1,encoding:"json"}]]);let ai=class extends Ms{#e;#t;#s;#r;#i;#n;#a;#o;#l;_streamIdAlloc;_cursorIdAlloc;#u;constructor(e,s){super(),this.#e=e,this.#t=[],this.#s=!1,this.#r=void 0,this.#i=!1,this.#n=void 0,this.#a=!1,this.#o=new Map,this.#l=new Be,this._streamIdAlloc=new Be,this._cursorIdAlloc=new Be,this.#u=new Be,this.#e.binaryType="arraybuffer",this.#e.addEventListener("open",()=>this.#p()),this.#e.addEventListener("close",r=>this.#f(r)),this.#e.addEventListener("error",r=>this.#m(r)),this.#e.addEventListener("message",r=>this.#b(r)),this.#h({type:"hello",jwt:s})}#h(e){if(this.#r!==void 0)throw new ae("Trying to send a message on a closed client");if(this.#s)this.#d(e);else{const s=()=>this.#d(e),r=()=>{};this.#t.push({openCallback:s,errorCallback:r})}}#p(){const e=this.#e.protocol;if(e===void 0){this.#c(new L("The `WebSocket.protocol` property is undefined. This most likely means that the WebSocket implementation provided by the environment is broken. If you are using Miniflare 2, please update to Miniflare 3, which fixes this problem."));return}else if(e==="")this.#n={version:1,encoding:"json"};else if(this.#n=dr.get(e),this.#n===void 0){this.#c(new q(`Unrecognized WebSocket subprotocol: ${JSON.stringify(e)}`));return}for(const s of this.#t)s.openCallback();this.#t.length=0,this.#s=!0}#d(e){const s=this.#n.encoding;if(s==="json"){const r=Vs(e,Sn);this.#e.send(r)}else if(s==="protobuf"){const r=zs(e,xn);this.#e.send(r)}else throw v(s,"Impossible encoding")}getVersion(){return new Promise((e,s)=>{if(this.#a=!0,this.#r!==void 0)s(this.#r);else if(this.#s)e(this.#n.version);else{const r=()=>e(this.#n.version);this.#t.push({openCallback:r,errorCallback:s})}})}_ensureVersion(e,s){if(this.#n===void 0||!this.#a)throw new Ce(`${s} is supported only on protocol version ${e} and higher, but the version supported by the WebSocket server is not yet known. Use Client.getVersion() to wait until the version is available.`);if(this.#n.version<e)throw new Ce(`${s} is supported on protocol version ${e} and higher, but the WebSocket server only supports version ${this.#n.version}`)}_sendRequest(e,s){if(this.#r!==void 0){s.errorCallback(new F("Client is closed",this.#r));return}const r=this.#l.alloc();this.#o.set(r,{...s,type:e.type}),this.#h({type:"request",requestId:r,request:e})}#m(e){const r=e.message??"WebSocket was closed due to an error";this.#c(new Rt(r))}#f(e){let s=`WebSocket was closed with code ${e.code}`;e.reason&&(s+=`: ${e.reason}`),this.#c(new Rt(s))}#c(e){if(this.#r===void 0){this.#r=e;for(const s of this.#t)s.errorCallback(e);this.#t.length=0;for(const[s,r]of this.#o.entries())r.errorCallback(e),this.#l.free(s);this.#o.clear(),this.#e.close()}}#b(e){if(this.#r===void 0)try{let s;const r=this.#n.encoding;if(r==="json"){if(typeof e.data!="string"){this.#e.close(3003,"Only text messages are accepted with JSON encoding"),this.#c(new q("Received non-text message from server with JSON encoding"));return}s=jt(JSON.parse(e.data),Mn)}else if(r==="protobuf"){if(!(e.data instanceof ArrayBuffer)){this.#e.close(3003,"Only binary messages are accepted with Protobuf encoding"),this.#c(new q("Received non-binary message from server with Protobuf encoding"));return}s=ht(new Uint8Array(e.data),Yn)}else throw v(r,"Impossible encoding");this.#y(s)}catch(s){this.#e.close(3007,"Could not handle message"),this.#c(s)}}#y(e){if(e.type==="none")throw new q("Received an unrecognized ServerMsg");if(e.type==="hello_ok"||e.type==="hello_error"){if(this.#i)throw new q("Received a duplicated hello response");if(this.#i=!0,e.type==="hello_error")throw xe(e.error);return}else if(!this.#i)throw new q("Received a non-hello message before a hello response");if(e.type==="response_ok"){const s=e.requestId,r=this.#o.get(s);if(this.#o.delete(s),r===void 0)throw new q("Received unexpected OK response");this.#l.free(s);try{if(r.type!==e.response.type)throw console.dir({responseState:r,msg:e}),new q("Received unexpected type of response");r.responseCallback(e.response)}catch(n){throw r.errorCallback(n),n}}else if(e.type==="response_error"){const s=e.requestId,r=this.#o.get(s);if(this.#o.delete(s),r===void 0)throw new q("Received unexpected error response");this.#l.free(s),r.errorCallback(xe(e.error))}else throw v(e,"Impossible ServerMsg type")}openStream(){return zt.open(this)}storeSql(e){this._ensureVersion(2,"storeSql()");const s=this.#u.alloc(),r=new Vt(this,s),n=()=>{},i=a=>r._setClosed(a),o={type:"store_sql",sqlId:s,sql:e};return this._sendRequest(o,{responseCallback:n,errorCallback:i}),r}_closeSql(e){if(this.#r!==void 0)return;const s=()=>this.#u.free(e),r=i=>this.#c(i),n={type:"close_sql",sqlId:e};this._sendRequest(n,{responseCallback:s,errorCallback:r})}close(){this.#c(new L("Client was manually closed"))}get closed(){return this.#r!==void 0}};const li=fetch,fr=Request,ui=Headers;let Se;if(typeof queueMicrotask<"u")Se=queueMicrotask;else{const t=Promise.resolve();Se=e=>{t.then(e)}}class ci{#e;#t;#s;constructor(e){this.#e=new Uint8Array(new ArrayBuffer(e)),this.#t=0,this.#s=0}get length(){return this.#s-this.#t}data(){return this.#e.slice(this.#t,this.#s)}push(e){this.#r(e.byteLength),this.#e.set(e,this.#s),this.#s+=e.byteLength}#r(e){if(this.#s+e<=this.#e.byteLength)return;const s=this.#s-this.#t;if(s+e<=this.#e.byteLength&&2*this.#s>=this.#e.byteLength)this.#e.copyWithin(0,this.#t,this.#s);else{let r=this.#e.byteLength;do r*=2;while(s+e>r);const n=new Uint8Array(new ArrayBuffer(r));n.set(this.#e.slice(this.#t,this.#s),0),this.#e=n}this.#s=s,this.#t=0}shift(e){this.#t+=e}}function hi(t){const e=W(t.baton),s=W(t.base_url),r=le(t.results,di);return{baton:e,baseUrl:s,results:r}}function di(t){const e=J(t.type);if(e==="ok")return{type:"ok",response:fi(Q(t.response))};if(e==="error")return{type:"error",error:Re(Q(t.error))};throw new q("Unexpected type of StreamResult")}function fi(t){const e=J(t.type);if(e==="close")return{type:"close"};if(e==="execute")return{type:"execute",result:Ht(Q(t.result))};if(e==="batch")return{type:"batch",result:ir(Q(t.result))};if(e==="sequence")return{type:"sequence"};if(e==="describe")return{type:"describe",result:ar(Q(t.result))};if(e==="store_sql")return{type:"store_sql"};if(e==="close_sql")return{type:"close_sql"};if(e==="get_autocommit")return{type:"get_autocommit",isAutocommit:Ue(t.is_autocommit)};throw new q("Unexpected type of StreamResponse")}function pi(t){const e=W(t.baton),s=W(t.base_url);return{baton:e,baseUrl:s}}const mi={default(){return{baton:void 0,baseUrl:void 0,results:[]}},1(t,e){e.baton=t.string()},2(t,e){e.baseUrl=t.string()},3(t,e){e.results.push(t.message(yi))}},yi={default(){return{type:"none"}},1(t){return{type:"ok",response:t.message(bi)}},2(t){return{type:"error",error:t.message(Y)}}},bi={default(){return{type:"none"}},1(t){return{type:"close"}},2(t){return t.message(wi)},3(t){return t.message(gi)},4(t){return{type:"sequence"}},5(t){return t.message(Si)},6(t){return{type:"store_sql"}},7(t){return{type:"close_sql"}},8(t){return t.message(qi)}},wi={default(){return{type:"execute",result:Te.default()}},1(t,e){e.result=t.message(Te)}},gi={default(){return{type:"batch",result:rt.default()}},1(t,e){e.result=t.message(rt)}},Si={default(){return{type:"describe",result:nt.default()}},1(t,e){e.result=t.message(nt)}},qi={default(){return{type:"get_autocommit",isAutocommit:!1}},1(t,e){e.isAutocommit=t.bool()}},_i={default(){return{baton:void 0,baseUrl:void 0}},1(t,e){e.baton=t.string()},2(t,e){e.baseUrl=t.string()}};class Ci extends er{#e;#t;#s;#r;#i;#n;constructor(e,s){super(),this.#e=e,this.#t=s,this.#s=void 0,this.#r=new ci(16*1024),this.#i=void 0,this.#n=!1}async open(e){if(e.body===null)throw new q("No response body for cursor request");this.#s=e.body.getReader();const s=await this.#a(pi,_i);if(s===void 0)throw new q("Empty response to cursor request");return s}next(){return this.#a(or,hr)}close(){this._setClosed(new L("Cursor was manually closed"))}_setClosed(e){this.#i===void 0&&(this.#i=e,this.#e._cursorClosed(this),this.#s!==void 0&&this.#s.cancel())}get closed(){return this.#i!==void 0}async#a(e,s){for(;;){if(this.#n)return;if(this.#i!==void 0)throw new F("Cursor is closed",this.#i);if(this.#t==="json"){const i=this.#o();if(i!==void 0){const o=new TextDecoder().decode(i),a=JSON.parse(o);return jt(a,e)}}else if(this.#t==="protobuf"){const i=this.#l();if(i!==void 0)return ht(i,s)}else throw v(this.#t,"Impossible encoding");if(this.#s===void 0)throw new ae("Attempted to read from HTTP cursor before it was opened");const{value:r,done:n}=await this.#s.read();if(n&&this.#r.length===0)this.#n=!0;else{if(n)throw new q("Unexpected end of cursor stream");this.#r.push(r)}}}#o(){const e=this.#r.data(),r=e.indexOf(10);if(r<0)return;const n=e.slice(0,r);return this.#r.shift(r+1),n}#l(){const e=this.#r.data();let s=0,r=0;for(;;){if(r>=e.byteLength)return;const i=e[r];if(s|=(i&127)<<7*r,r+=1,!(i&128))break}if(e.byteLength<r+s)return;const n=e.slice(r,r+s);return this.#r.shift(r+s),n}}function xi(t,e){e.baton!==void 0&&t.string("baton",e.baton),t.arrayObjects("requests",e.requests,Ri)}function Ri(t,e){if(t.stringRaw("type",e.type),e.type!=="close"){if(e.type==="execute")t.object("stmt",e.stmt,Kt);else if(e.type==="batch")t.object("batch",e.batch,st);else if(e.type==="sequence")e.sql!==void 0&&t.string("sql",e.sql),e.sqlId!==void 0&&t.number("sql_id",e.sqlId);else if(e.type==="describe")e.sql!==void 0&&t.string("sql",e.sql),e.sqlId!==void 0&&t.number("sql_id",e.sqlId);else if(e.type==="store_sql")t.number("sql_id",e.sqlId),t.string("sql",e.sql);else if(e.type==="close_sql")t.number("sql_id",e.sqlId);else if(e.type!=="get_autocommit")throw v(e,"Impossible type of StreamRequest")}}function Ti(t,e){e.baton!==void 0&&t.string("baton",e.baton),t.object("batch",e.batch,st)}function Ii(t,e){e.baton!==void 0&&t.string(1,e.baton);for(const s of e.requests)t.message(2,s,Li)}function Li(t,e){if(e.type==="close")t.message(1,e,Ni);else if(e.type==="execute")t.message(2,e,vi);else if(e.type==="batch")t.message(3,e,$i);else if(e.type==="sequence")t.message(4,e,Ai);else if(e.type==="describe")t.message(5,e,Bi);else if(e.type==="store_sql")t.message(6,e,Qi);else if(e.type==="close_sql")t.message(7,e,Ei);else if(e.type==="get_autocommit")t.message(8,e,Oi);else throw v(e,"Impossible type of StreamRequest")}function Ni(t,e){}function vi(t,e){t.message(1,e.stmt,Jt)}function $i(t,e){t.message(1,e.batch,dt)}function Ai(t,e){e.sql!==void 0&&t.string(1,e.sql),e.sqlId!==void 0&&t.int32(2,e.sqlId)}function Bi(t,e){e.sql!==void 0&&t.string(1,e.sql),e.sqlId!==void 0&&t.int32(2,e.sqlId)}function Qi(t,e){t.int32(1,e.sqlId),t.string(2,e.sql)}function Ei(t,e){t.int32(1,e.sqlId)}function Oi(t,e){}function Pi(t,e){e.baton!==void 0&&t.string(1,e.baton),t.message(2,e.batch,dt)}class Ui extends Xs{#e;#t;#s;#r;#i;#n;#a;#o;#l;#u;#h;#p;constructor(e,s,r,n){super(e.intMode),this.#e=e,this.#t=s.toString(),this.#s=r,this.#r=n,this.#i=void 0,this.#n=new tt,this.#a=!1,this.#l=!1,this.#u=!1,this.#h=void 0,this.#p=new Be}client(){return this.#e}_sqlOwner(){return this}storeSql(e){const s=this.#p.alloc();return this.#d({type:"store_sql",sqlId:s,sql:e}).then(()=>{},r=>this._setClosed(r)),new Vt(this,s)}_closeSql(e){this.#h===void 0&&this.#d({type:"close_sql",sqlId:e}).then(()=>this.#p.free(e),s=>this._setClosed(s))}_execute(e){return this.#d({type:"execute",stmt:e}).then(s=>s.result)}_batch(e){return this.#d({type:"batch",batch:e}).then(s=>s.result)}_describe(e){return this.#d({type:"describe",sql:e.sql,sqlId:e.sqlId}).then(s=>s.result)}_sequence(e){return this.#d({type:"sequence",sql:e.sql,sqlId:e.sqlId}).then(s=>{})}getAutocommit(){return this.#e._ensureVersion(3,"getAutocommit()"),this.#d({type:"get_autocommit"}).then(e=>e.isAutocommit)}#d(e){return new Promise((s,r)=>{this.#m({type:"pipeline",request:e,responseCallback:s,errorCallback:r})})}_openCursor(e){return new Promise((s,r)=>{this.#m({type:"cursor",batch:e,cursorCallback:s,errorCallback:r})})}_cursorClosed(e){if(e!==this.#o)throw new ae("Cursor was closed, but it was not associated with the stream");this.#o=void 0,Se(()=>this.#f())}close(){this._setClosed(new L("Stream was manually closed"))}closeGracefully(){this.#l=!0,Se(()=>this.#f())}get closed(){return this.#h!==void 0||this.#l}_setClosed(e){if(this.#h===void 0){for(this.#h=e,this.#o!==void 0&&this.#o._setClosed(e),this.#e._streamClosed(this);;){const s=this.#n.shift();if(s!==void 0)s.errorCallback(e);else break}(this.#i!==void 0||this.#a)&&!this.#u&&(this.#n.push({type:"pipeline",request:{type:"close"},responseCallback:()=>{},errorCallback:()=>{}}),this.#u=!0,Se(()=>this.#f()))}}#m(e){if(this.#h!==void 0)throw new F("Stream is closed",this.#h);if(this.#l)throw new F("Stream is closing",void 0);this.#n.push(e),Se(()=>this.#f())}#f(){if(this.#a||this.#o!==void 0)return;if(this.#l&&this.#n.length===0){this._setClosed(new L("Stream was gracefully closed"));return}const e=this.#e._endpoint;if(e===void 0){this.#e._endpointPromise.then(()=>this.#f(),r=>this._setClosed(r));return}const s=this.#n.shift();if(s!==void 0)if(s.type==="pipeline"){const r=[s];for(;;){const n=this.#n.first();if(n!==void 0&&n.type==="pipeline")r.push(n),this.#n.shift();else if(n===void 0&&this.#l&&!this.#u){r.push({type:"pipeline",request:{type:"close"},responseCallback:()=>{},errorCallback:()=>{}}),this.#u=!0;break}else break}this.#c(e,r)}else if(s.type==="cursor")this.#b(e,s);else throw v(s,"Impossible type of QueueEntry")}#c(e,s){this.#y(()=>this.#g(s,e),r=>ki(r,e.encoding),r=>r.baton,r=>r.baseUrl,r=>Di(s,r),r=>s.forEach(n=>n.errorCallback(r)))}#b(e,s){const r=new Ci(this,e.encoding);this.#o=r,this.#y(()=>this.#S(s,e),n=>r.open(n),n=>n.baton,n=>n.baseUrl,n=>s.cursorCallback(r),n=>s.errorCallback(n))}#y(e,s,r,n,i,o){let a;try{const l=e(),h=this.#r;a=h(l)}catch(l){a=Promise.reject(l)}this.#a=!0,a.then(l=>l.ok?s(l):Mi(l).then(h=>{throw h})).then(l=>{this.#i=r(l),this.#t=n(l)??this.#t,i(l)}).catch(l=>{this._setClosed(l),o(l)}).finally(()=>{this.#a=!1,this.#f()})}#g(e,s){return this.#w(new URL(s.pipelinePath,this.#t),{baton:this.#i,requests:e.map(r=>r.request)},s.encoding,xi,Ii)}#S(e,s){if(s.cursorPath===void 0)throw new Ce(`Cursors are supported only on protocol version 3 and higher, but the HTTP server only supports version ${s.version}.`);return this.#w(new URL(s.cursorPath,this.#t),{baton:this.#i,batch:e.batch},s.encoding,Ti,Pi)}#w(e,s,r,n,i){let o,a;if(r==="json")o=Vs(s,n),a="application/json";else if(r==="protobuf")o=zs(s,i),a="application/x-protobuf";else throw v(r,"Impossible encoding");const l=new ui;return l.set("content-type",a),this.#s!==void 0&&l.set("authorization",`Bearer ${this.#s}`),new fr(e.toString(),{method:"POST",headers:l,body:o})}}function Di(t,e){if(e.results.length!==t.length)throw new q("Server returned unexpected number of pipeline results");for(let s=0;s<t.length;++s){const r=e.results[s],n=t[s];if(r.type==="ok"){if(r.response.type!==n.request.type)throw new q("Received unexpected type of response");n.responseCallback(r.response)}else if(r.type==="error")n.errorCallback(xe(r.error));else throw r.type==="none"?new q("Received unrecognized type of StreamResult"):v(r,"Received impossible type of StreamResult")}}async function ki(t,e){if(e==="json"){const s=await t.json();return jt(s,hi)}if(e==="protobuf"){const s=await t.arrayBuffer();return ht(new Uint8Array(s),mi)}throw await t.body?.cancel(),v(e,"Impossible encoding")}async function Mi(t){const e=t.headers.get("content-type")??"text/plain";let s=`Server returned HTTP status ${t.status}`;if(e==="application/json"){const r=await t.json();return"message"in r?xe(r):new Ye(s,t.status)}if(e==="text/plain"){const r=(await t.text()).trim();return r!==""&&(s+=`: ${r}`),new Ye(s,t.status)}return await t.body?.cancel(),new Ye(s,t.status)}const ji=[{versionPath:"v3-protobuf",pipelinePath:"v3-protobuf/pipeline",cursorPath:"v3-protobuf/cursor",version:3,encoding:"protobuf"}],vt={versionPath:"v2",pipelinePath:"v2/pipeline",cursorPath:void 0,version:2,encoding:"json"};let Fi=class extends Ms{#e;#t;#s;#r;#i;_endpointPromise;_endpoint;constructor(e,s,r,n=2){super(),this.#e=e,this.#t=s,this.#s=r??li,this.#r=void 0,this.#i=new Set,n==3?(this._endpointPromise=Vi(this.#s,this.#e),this._endpointPromise.then(i=>this._endpoint=i,i=>this.#n(i))):(this._endpointPromise=Promise.resolve(vt),this._endpointPromise.then(i=>this._endpoint=i,i=>this.#n(i)))}async getVersion(){return this._endpoint!==void 0?this._endpoint.version:(await this._endpointPromise).version}_ensureVersion(e,s){if(!(e<=vt.version)){if(this._endpoint===void 0)throw new Ce(`${s} is supported only on protocol version ${e} and higher, but the version supported by the HTTP server is not yet known. Use Client.getVersion() to wait until the version is available.`);if(this._endpoint.version<e)throw new Ce(`${s} is supported only on protocol version ${e} and higher, but the HTTP server only supports version ${this._endpoint.version}.`)}}openStream(){if(this.#r!==void 0)throw new F("Client is closed",this.#r);const e=new Ui(this,this.#e,this.#t,this.#s);return this.#i.add(e),e}_streamClosed(e){this.#i.delete(e)}close(){this.#n(new L("Client was manually closed"))}get closed(){return this.#r!==void 0}#n(e){if(this.#r===void 0){this.#r=e;for(const s of Array.from(this.#i))s._setClosed(new F("Client was closed",e))}}};async function Vi(t,e){const s=t;for(const r of ji){const n=new URL(r.versionPath,e),i=new fr(n.toString(),{method:"GET"}),o=await s(i);if(await o.arrayBuffer(),o.ok)return r}return vt}function pr(t,e,s=2){if(typeof ge>"u")throw new Fs("WebSockets are not supported in this environment");var r=void 0;s==3?r=Array.from(dr.keys()):r=Array.from(oi.keys());const n=new ge(t,r);return new ai(n,e)}function zi(t,e,s,r=2){return new Fi(t instanceof URL?t:new URL(t),e,s,r)}class mr{#e;#t;#s;constructor(e,s){this.#e=e,this.#t=s,this.#s=void 0}execute(e){return this.batch([e]).then(s=>s[0])}async batch(e){const s=this._getStream();if(s.closed)throw new R("Cannot execute statements because the transaction is closed","TRANSACTION_CLOSED");try{const r=e.map(pe);let n;if(this.#s===void 0){this._getSqlCache().apply(r);const o=s.batch(this.#t>=3),a=o.step(),l=a.run(xt(this.#e));let h=a;n=r.map(c=>{const p=o.step().condition(D.ok(h));this.#t>=3&&p.condition(D.not(D.isAutocommit(o)));const y=p.query(c);return y.catch(()=>{}),h=p,y}),this.#s=o.execute().then(()=>l).then(()=>{});try{await this.#s}catch(c){throw this.close(),c}}else{this.#t<3&&await this.#s,this._getSqlCache().apply(r);const o=s.batch(this.#t>=3);let a;n=r.map(l=>{const h=o.step();a!==void 0&&h.condition(D.ok(a)),this.#t>=3&&h.condition(D.not(D.isAutocommit(o)));const c=h.query(l);return c.catch(()=>{}),a=h,c}),await o.execute()}const i=[];for(const o of n){const a=await o;if(a===void 0)throw new R("Statement in a transaction was not executed, probably because the transaction has been rolled back","TRANSACTION_CLOSED");i.push(ft(a))}return i}catch(r){throw N(r)}}async executeMultiple(e){const s=this._getStream();if(s.closed)throw new R("Cannot execute statements because the transaction is closed","TRANSACTION_CLOSED");try{if(this.#s===void 0){this.#s=s.run(xt(this.#e)).then(()=>{});try{await this.#s}catch(r){throw this.close(),r}}else await this.#s;await s.sequence(e)}catch(r){throw N(r)}}async rollback(){try{const e=this._getStream();if(e.closed||this.#s===void 0)return;const s=e.run("ROLLBACK").catch(r=>{throw N(r)});e.closeGracefully(),await s}catch(e){throw N(e)}finally{this.close()}}async commit(){try{const e=this._getStream();if(e.closed)throw new R("Cannot commit the transaction because it is already closed","TRANSACTION_CLOSED");if(this.#s!==void 0)await this.#s;else return;const s=e.run("COMMIT").catch(r=>{throw N(r)});e.closeGracefully(),await s}catch(e){throw N(e)}finally{this.close()}}}async function it(t,e,s,r,n=!1){n&&s.step().run("PRAGMA foreign_keys=off");const i=s.step(),o=i.run(xt(t));let a=i;const l=r.map(_=>{const S=s.step().condition(D.ok(a));e>=3&&S.condition(D.not(D.isAutocommit(s)));const w=S.query(_);return a=S,w}),h=s.step().condition(D.ok(a));e>=3&&h.condition(D.not(D.isAutocommit(s)));const c=h.run("COMMIT");s.step().condition(D.not(D.ok(h))).run("ROLLBACK").catch(_=>{}),n&&s.step().run("PRAGMA foreign_keys=on"),await s.execute();const y=[];await o;for(const _ of l){const S=await _;if(S===void 0)throw new R("Statement in a batch was not executed, probably because the transaction has been rolled back","TRANSACTION_CLOSED");y.push(ft(S))}return await c,y}function pe(t){let e,s;Array.isArray(t)?[e,s]=t:typeof t=="string"?e=t:(e=t.sql,s=t.args);const r=new Ys(e);if(s)if(Array.isArray(s))r.bindIndexes(s);else for(const[n,i]of Object.entries(s))r.bindName(n,i);return r}function ft(t){const e=t.columnNames.map(o=>o??""),s=t.columnDecltypes.map(o=>o??""),r=t.rows,n=t.affectedRowCount,i=t.lastInsertRowid!==void 0?t.lastInsertRowid:void 0;return new Xr(e,s,r,n,i)}function N(t){if(t instanceof L){const e=yr(t);return new R(t.message,e,void 0,t)}return t}function yr(t){return t instanceof js&&t.code!==void 0?t.code:t instanceof q?"HRANA_PROTO_ERROR":t instanceof F?t.cause instanceof L?yr(t.cause):"HRANA_CLOSED_ERROR":t instanceof Rt?"HRANA_WEBSOCKET_ERROR":t instanceof Ye?"SERVER_ERROR":t instanceof Ce?"PROTOCOL_VERSION_ERROR":t instanceof ae?"INTERNAL_ERROR":"UNKNOWN"}class Gt{#e;#t;capacity;constructor(e,s){this.#e=e,this.#t=new Ki,this.capacity=s}apply(e){if(this.capacity<=0)return;const s=new Set;for(const r of e){if(typeof r.sql!="string")continue;const n=r.sql;if(n.length>=5e3)continue;let i=this.#t.get(n);if(i===void 0){for(;this.#t.size+1>this.capacity;){const[o,a]=this.#t.peekLru();if(s.has(a))break;a.close(),this.#t.delete(o)}this.#t.size+1<=this.capacity&&(i=this.#e.storeSql(n),this.#t.set(n,i))}i!==void 0&&(r.sql=i,s.add(i))}}}class Ki{#e;constructor(){this.#e=new Map}get(e){const s=this.#e.get(e);return s!==void 0&&(this.#e.delete(e),this.#e.set(e,s)),s}set(e,s){this.#e.set(e,s)}peekLru(){for(const e of this.#e.entries())return e}delete(e){this.#e.delete(e)}get size(){return this.#e.size}}var bt,ps;function Ji(){if(ps)return bt;ps=1;function t(r){var n=0,i=[];function o(){n--,n<r&&a()}function a(){var p=i.shift();c.queue=i.length,p&&h(p.fn).then(p.resolve).catch(p.reject)}function l(p){return new Promise(function(y,_){i.push({fn:p,resolve:y,reject:_}),c.queue=i.length})}function h(p){n++;try{return Promise.resolve(p()).then(function(y){return o(),y},function(y){throw o(),y})}catch(y){return o(),Promise.reject(y)}}var c=function(p){return n>=r?l(p):h(p)};return c}function e(r,n){var i=!1,o=this;return Promise.all(r.map(function(){var a=arguments;return o(function(){if(!i)return n.apply(void 0,a).catch(function(l){throw i=!0,l})})}))}function s(r){return r.queue=0,r.map=e,r}return bt=function(r){return s(r?t(r):function(n){return n()})},bt}var Wi=Ji();const br=Qr(Wi);function Hi(t){if(t.scheme!=="wss"&&t.scheme!=="ws")throw new R(`The WebSocket client supports only "libsql:", "wss:" and "ws:" URLs, got ${JSON.stringify(t.scheme+":")}. For more information, please read ${Pe}`,"URL_SCHEME_NOT_SUPPORTED");if(t.encryptionKey!==void 0)throw new R("Encryption key is not supported by the remote client.","ENCRYPTION_KEY_NOT_SUPPORTED");if(t.scheme==="ws"&&t.tls)throw new R('A "ws:" URL cannot opt into TLS by using ?tls=1',"URL_INVALID");if(t.scheme==="wss"&&!t.tls)throw new R('A "wss:" URL cannot opt out of TLS by using ?tls=0',"URL_INVALID");const e=qt(t.scheme,t.authority,t.path);let s;try{s=pr(e,t.authToken)}catch(r){if(r instanceof Fs){const n=t.scheme==="wss"?"https":"http",i=qt(n,t.authority,t.path);throw new R(`This environment does not support WebSockets, please switch to the HTTP client by using a "${n}:" URL (${JSON.stringify(i)}). For more information, please read ${Pe}`,"WEBSOCKETS_NOT_SUPPORTED")}throw N(r)}return new Zi(s,e,t.authToken,t.intMode,t.concurrency)}const Gi=60*1e3,Yi=100;class Zi{#e;#t;#s;#r;#i;closed;protocol;#n;#a;constructor(e,s,r,n,i){this.#e=s,this.#t=r,this.#s=n,this.#r=this.#l(e),this.#i=void 0,this.closed=!1,this.protocol="ws",this.#a=br(i)}async limit(e){return this.#a(e)}async execute(e,s){let r;return typeof e=="string"?r={sql:e,args:s||[]}:r=e,this.limit(async()=>{const n=await this.#o();try{const i=pe(r);n.conn.sqlCache.apply([i]);const o=n.stream.query(i);n.stream.closeGracefully();const a=await o;return ft(a)}catch(i){throw N(i)}finally{this._closeStream(n)}})}async batch(e,s="deferred"){return this.limit(async()=>{const r=await this.#o();try{const i=e.map(c=>Array.isArray(c)?{sql:c[0],args:c[1]||[]}:c).map(pe),o=await r.conn.client.getVersion();r.conn.sqlCache.apply(i);const a=r.stream.batch(o>=3);return await it(s,o,a,i)}catch(n){throw N(n)}finally{this._closeStream(r)}})}async migrate(e){return this.limit(async()=>{const s=await this.#o();try{const r=e.map(pe),n=await s.conn.client.getVersion(),i=s.stream.batch(n>=3);return await it("deferred",n,i,r,!0)}catch(r){throw N(r)}finally{this._closeStream(s)}})}async transaction(e="write"){return this.limit(async()=>{const s=await this.#o();try{const r=await s.conn.client.getVersion();return new Xi(this,s,e,r)}catch(r){throw this._closeStream(s),N(r)}})}async executeMultiple(e){return this.limit(async()=>{const s=await this.#o();try{const r=s.stream.sequence(e);s.stream.closeGracefully(),await r}catch(r){throw N(r)}finally{this._closeStream(s)}})}sync(){throw new R("sync not supported in ws mode","SYNC_NOT_SUPPORTED")}async#o(){if(this.closed)throw new R("The client is closed","CLIENT_CLOSED");if(new Date().valueOf()-this.#r.openTime.valueOf()>Gi&&this.#i===void 0){const n=this.#l();this.#i=n,n.client.getVersion().then(i=>{this.#r!==n&&this.#r.streamStates.size===0&&this.#r.client.close(),this.#r=n,this.#i=void 0},i=>{this.#i=void 0})}if(this.#r.client.closed)try{this.#i!==void 0?this.#r=this.#i:this.#r=this.#l()}catch(n){throw N(n)}const r=this.#r;try{r.useSqlCache===void 0&&(r.useSqlCache=await r.client.getVersion()>=2,r.useSqlCache&&(r.sqlCache.capacity=Yi));const n=r.client.openStream();n.intMode=this.#s;const i={conn:r,stream:n};return r.streamStates.add(i),i}catch(n){throw N(n)}}#l(e){try{return e??=pr(this.#e,this.#t),{client:e,useSqlCache:void 0,sqlCache:new Gt(e,0),openTime:new Date,streamStates:new Set}}catch(s){throw N(s)}}_closeStream(e){e.stream.close();const s=e.conn;s.streamStates.delete(e),s.streamStates.size===0&&s!==this.#r&&s.client.close()}close(){this.#r.client.close(),this.closed=!0}}class Xi extends mr{#e;#t;constructor(e,s,r,n){super(r,n),this.#e=e,this.#t=s}_getStream(){return this.#t.stream}_getSqlCache(){return this.#t.conn.sqlCache}close(){this.#e._closeStream(this.#t)}get closed(){return this.#t.stream.closed}}function eo(t){if(t.scheme!=="https"&&t.scheme!=="http")throw new R(`The HTTP client supports only "libsql:", "https:" and "http:" URLs, got ${JSON.stringify(t.scheme+":")}. For more information, please read ${Pe}`,"URL_SCHEME_NOT_SUPPORTED");if(t.encryptionKey!==void 0)throw new R("Encryption key is not supported by the remote client.","ENCRYPTION_KEY_NOT_SUPPORTED");if(t.scheme==="http"&&t.tls)throw new R('A "http:" URL cannot opt into TLS by using ?tls=1',"URL_INVALID");if(t.scheme==="https"&&!t.tls)throw new R('A "https:" URL cannot opt out of TLS by using ?tls=0',"URL_INVALID");const e=qt(t.scheme,t.authority,t.path);return new to(e,t.authToken,t.intMode,t.fetch,t.concurrency)}const wr=30;class to{#e;protocol;#t;#s;constructor(e,s,r,n,i){this.#e=zi(e,s,n),this.#e.intMode=r,this.protocol="http",this.#t=s,this.#s=br(i)}async limit(e){return this.#s(e)}async execute(e,s){let r;return typeof e=="string"?r={sql:e,args:s||[]}:r=e,this.limit(async()=>{try{const n=pe(r);let i;const o=this.#e.openStream();try{i=o.query(n)}finally{o.closeGracefully()}const a=await i;return ft(a)}catch(n){throw N(n)}})}async batch(e,s="deferred"){return this.limit(async()=>{try{const n=e.map(h=>Array.isArray(h)?{sql:h[0],args:h[1]||[]}:h).map(pe),i=await this.#e.getVersion();let o;const a=this.#e.openStream();try{new Gt(a,wr).apply(n);const c=a.batch(!1);o=it(s,i,c,n)}finally{a.closeGracefully()}return await o}catch(r){throw N(r)}})}async migrate(e){return this.limit(async()=>{try{const s=e.map(pe),r=await this.#e.getVersion();let n;const i=this.#e.openStream();try{const a=i.batch(!1);n=it("deferred",r,a,s,!0)}finally{i.closeGracefully()}return await n}catch(s){throw N(s)}})}async transaction(e="write"){return this.limit(async()=>{try{const s=await this.#e.getVersion();return new so(this.#e.openStream(),e,s)}catch(s){throw N(s)}})}async executeMultiple(e){return this.limit(async()=>{try{let s;const r=this.#e.openStream();try{s=r.sequence(e)}finally{r.closeGracefully()}await s}catch(s){throw N(s)}})}sync(){throw new R("sync not supported in http mode","SYNC_NOT_SUPPORTED")}close(){this.#e.close()}get closed(){return this.#e.closed}}class so extends mr{#e;#t;constructor(e,s,r){super(s,r),this.#e=e,this.#t=new Gt(e,wr)}_getStream(){return this.#e}_getSqlCache(){return this.#t}close(){this.#e.close()}get closed(){return this.#e.closed}}function Ze(t){return ro(sn(t))}function ro(t){if(t.scheme==="ws"||t.scheme==="wss")return Hi(t);if(t.scheme==="http"||t.scheme==="https")return eo(t);throw new R(`The client that uses Web standard APIs supports only "libsql:", "wss:", "ws:", "https:" and "http:" URLs, got ${JSON.stringify(t.scheme+":")}. For more information, please read ${Pe}`,"URL_SCHEME_NOT_SUPPORTED")}const f=Symbol.for("drizzle:entityKind");function d(t,e){if(!t||typeof t!="object")return!1;if(t instanceof e)return!0;if(!Object.prototype.hasOwnProperty.call(e,f))throw new Error(`Class "${e.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let s=Object.getPrototypeOf(t).constructor;if(s)for(;s;){if(f in s&&s[f]===e[f])return!0;s=Object.getPrototypeOf(s)}return!1}class ${constructor(e,s){this.table=e,this.config=s,this.name=s.name,this.keyAsName=s.keyAsName,this.notNull=s.notNull,this.default=s.default,this.defaultFn=s.defaultFn,this.onUpdateFn=s.onUpdateFn,this.hasDefault=s.hasDefault,this.primary=s.primaryKey,this.isUnique=s.isUnique,this.uniqueName=s.uniqueName,this.uniqueType=s.uniqueType,this.dataType=s.dataType,this.columnType=s.columnType,this.generated=s.generated,this.generatedIdentity=s.generatedIdentity}static[f]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return this.config.generated!==void 0&&this.config.generated.type!=="byDefault"}}class no{static[f]="ColumnBuilder";config;constructor(e,s,r){this.config={name:e,keyAsName:e==="",notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:s,columnType:r,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){this.config.name===""&&(this.config.name=e)}}const oe=Symbol.for("drizzle:Name"),ms=Symbol.for("drizzle:isPgEnum");function io(t){return!!t&&typeof t=="function"&&ms in t&&t[ms]===!0}class V{static[f]="Subquery";constructor(e,s,r,n=!1,i=[]){this._={brand:"Subquery",sql:e,selectedFields:s,alias:r,isWith:n,usedTables:i}}}class gr extends V{static[f]="WithSubquery"}const oo={startActiveSpan(t,e){return e()}},E=Symbol.for("drizzle:ViewBaseConfig"),Xe=Symbol.for("drizzle:Schema"),$t=Symbol.for("drizzle:Columns"),ys=Symbol.for("drizzle:ExtraConfigColumns"),wt=Symbol.for("drizzle:OriginalName"),gt=Symbol.for("drizzle:BaseName"),ot=Symbol.for("drizzle:IsAlias"),bs=Symbol.for("drizzle:ExtraConfigBuilder"),ao=Symbol.for("drizzle:IsDrizzleTable");class m{static[f]="Table";static Symbol={Name:oe,Schema:Xe,OriginalName:wt,Columns:$t,ExtraConfigColumns:ys,BaseName:gt,IsAlias:ot,ExtraConfigBuilder:bs};[oe];[wt];[Xe];[$t];[ys];[gt];[ot]=!1;[ao]=!0;[bs]=void 0;constructor(e,s,r){this[oe]=this[wt]=e,this[Xe]=s,this[gt]=r}}function qe(t){return t[oe]}function De(t){return`${t[Xe]??"public"}.${t[oe]}`}function Sr(t){return t!=null&&typeof t.getSQL=="function"}function lo(t){const e={sql:"",params:[]};for(const s of t)e.sql+=s.sql,e.params.push(...s.params),s.typings?.length&&(e.typings||(e.typings=[]),e.typings.push(...s.typings));return e}class B{static[f]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new b([this])}}class b{constructor(e){this.queryChunks=e;for(const s of e)if(d(s,m)){const r=s[m.Symbol.Schema];this.usedTables.push(r===void 0?s[m.Symbol.Name]:r+"."+s[m.Symbol.Name])}}static[f]="SQL";decoder=qr;shouldInlineParams=!1;usedTables=[];append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return oo.startActiveSpan("drizzle.buildSQL",s=>{const r=this.buildQueryFromSourceParams(this.queryChunks,e);return s?.setAttributes({"drizzle.query.text":r.sql,"drizzle.query.params":JSON.stringify(r.params)}),r})}buildQueryFromSourceParams(e,s){const r=Object.assign({},s,{inlineParams:s.inlineParams||this.shouldInlineParams,paramStartIndex:s.paramStartIndex||{value:0}}),{casing:n,escapeName:i,escapeParam:o,prepareTyping:a,inlineParams:l,paramStartIndex:h}=r;return lo(e.map(c=>{if(d(c,B))return{sql:c.value.join(""),params:[]};if(d(c,At))return{sql:i(c.value),params:[]};if(c===void 0)return{sql:"",params:[]};if(Array.isArray(c)){const p=[new B("(")];for(const[y,_]of c.entries())p.push(_),y<c.length-1&&p.push(new B(", "));return p.push(new B(")")),this.buildQueryFromSourceParams(p,r)}if(d(c,b))return this.buildQueryFromSourceParams(c.queryChunks,{...r,inlineParams:l||c.shouldInlineParams});if(d(c,m)){const p=c[m.Symbol.Schema],y=c[m.Symbol.Name];return{sql:p===void 0||c[ot]?i(y):i(p)+"."+i(y),params:[]}}if(d(c,$)){const p=n.getColumnCasing(c);if(s.invokeSource==="indexes")return{sql:i(p),params:[]};const y=c.table[m.Symbol.Schema];return{sql:c.table[ot]||y===void 0?i(c.table[m.Symbol.Name])+"."+i(p):i(y)+"."+i(c.table[m.Symbol.Name])+"."+i(p),params:[]}}if(d(c,$e)){const p=c[E].schema,y=c[E].name;return{sql:p===void 0||c[E].isAlias?i(y):i(p)+"."+i(y),params:[]}}if(d(c,ue)){if(d(c.value,Ie))return{sql:o(h.value++,c),params:[c],typings:["none"]};const p=c.value===null?null:c.encoder.mapToDriverValue(c.value);if(d(p,b))return this.buildQueryFromSourceParams([p],r);if(l)return{sql:this.mapInlineParam(p,r),params:[]};let y=["none"];return a&&(y=[a(c.encoder)]),{sql:o(h.value++,p),params:[p],typings:y}}return d(c,Ie)?{sql:o(h.value++,c),params:[c],typings:["none"]}:d(c,b.Aliased)&&c.fieldAlias!==void 0?{sql:i(c.fieldAlias),params:[]}:d(c,V)?c._.isWith?{sql:i(c._.alias),params:[]}:this.buildQueryFromSourceParams([new B("("),c._.sql,new B(") "),new At(c._.alias)],r):io(c)?c.schema?{sql:i(c.schema)+"."+i(c.enumName),params:[]}:{sql:i(c.enumName),params:[]}:Sr(c)?c.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([c.getSQL()],r):this.buildQueryFromSourceParams([new B("("),c.getSQL(),new B(")")],r):l?{sql:this.mapInlineParam(c,r),params:[]}:{sql:o(h.value++,c),params:[c],typings:["none"]}}))}mapInlineParam(e,{escapeString:s}){if(e===null)return"null";if(typeof e=="number"||typeof e=="boolean")return e.toString();if(typeof e=="string")return s(e);if(typeof e=="object"){const r=e.toString();return s(r==="[object Object]"?JSON.stringify(e):r)}throw new Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return e===void 0?this:new b.Aliased(this,e)}mapWith(e){return this.decoder=typeof e=="function"?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class At{constructor(e){this.value=e}static[f]="Name";brand;getSQL(){return new b([this])}}function uo(t){return typeof t=="object"&&t!==null&&"mapToDriverValue"in t&&typeof t.mapToDriverValue=="function"}const qr={mapFromDriverValue:t=>t},_r={mapToDriverValue:t=>t};({...qr,..._r});class ue{constructor(e,s=_r){this.value=e,this.encoder=s}static[f]="Param";brand;getSQL(){return new b([this])}}function u(t,...e){const s=[];(e.length>0||t.length>0&&t[0]!=="")&&s.push(new B(t[0]));for(const[r,n]of e.entries())s.push(n,new B(t[r+1]));return new b(s)}(t=>{function e(){return new b([])}t.empty=e;function s(l){return new b(l)}t.fromList=s;function r(l){return new b([new B(l)])}t.raw=r;function n(l,h){const c=[];for(const[p,y]of l.entries())p>0&&h!==void 0&&c.push(h),c.push(y);return new b(c)}t.join=n;function i(l){return new At(l)}t.identifier=i;function o(l){return new Ie(l)}t.placeholder=o;function a(l,h){return new ue(l,h)}t.param=a})(u||(u={}));(t=>{class e{constructor(r,n){this.sql=r,this.fieldAlias=n}static[f]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new e(this.sql,this.fieldAlias)}}t.Aliased=e})(b||(b={}));class Ie{constructor(e){this.name=e}static[f]="Placeholder";getSQL(){return new b([this])}}function Je(t,e){return t.map(s=>{if(d(s,Ie)){if(!(s.name in e))throw new Error(`No value for placeholder "${s.name}" was provided`);return e[s.name]}if(d(s,ue)&&d(s.value,Ie)){if(!(s.value.name in e))throw new Error(`No value for placeholder "${s.value.name}" was provided`);return s.encoder.mapToDriverValue(e[s.value.name])}return s})}const co=Symbol.for("drizzle:IsDrizzleView");class $e{static[f]="View";[E];[co]=!0;constructor({name:e,schema:s,selectedFields:r,query:n}){this[E]={name:e,originalName:e,schema:s,selectedFields:r,query:n,isExisting:!n,isAlias:!1}}getSQL(){return new b([this])}}$.prototype.getSQL=function(){return new b([this])};m.prototype.getSQL=function(){return new b([this])};V.prototype.getSQL=function(){return new b([this])};function ws(t,e,s){const r={},n=t.reduce((i,{path:o,field:a},l)=>{let h;d(a,$)?h=a:d(a,b)?h=a.decoder:h=a.sql.decoder;let c=i;for(const[p,y]of o.entries())if(p<o.length-1)y in c||(c[y]={}),c=c[y];else{const _=e[l],S=c[y]=_===null?null:h.mapFromDriverValue(_);if(s&&d(a,$)&&o.length===2){const w=o[0];w in r?typeof r[w]=="string"&&r[w]!==qe(a.table)&&(r[w]=!1):r[w]=S===null?qe(a.table):!1}}return i},{});if(s&&Object.keys(r).length>0)for(const[i,o]of Object.entries(r))typeof o=="string"&&!s[o]&&(n[i]=null);return n}function me(t,e){return Object.entries(t).reduce((s,[r,n])=>{if(typeof r!="string")return s;const i=e?[...e,r]:[r];return d(n,$)||d(n,b)||d(n,b.Aliased)?s.push({path:i,field:n}):d(n,m)?s.push(...me(n[m.Symbol.Columns],i)):s.push(...me(n,i)),s},[])}function Yt(t,e){const s=Object.keys(t),r=Object.keys(e);if(s.length!==r.length)return!1;for(const[n,i]of s.entries())if(i!==r[n])return!1;return!0}function Cr(t,e){const s=Object.entries(e).filter(([,r])=>r!==void 0).map(([r,n])=>d(n,b)||d(n,$)?[r,n]:[r,new ue(n,t[m.Symbol.Columns][r])]);if(s.length===0)throw new Error("No values to set");return Object.fromEntries(s)}function ho(t,e){for(const s of e)for(const r of Object.getOwnPropertyNames(s.prototype))r!=="constructor"&&Object.defineProperty(t.prototype,r,Object.getOwnPropertyDescriptor(s.prototype,r)||Object.create(null))}function fo(t){return t[m.Symbol.Columns]}function Bt(t){return d(t,V)?t._.alias:d(t,$e)?t[E].name:d(t,b)?void 0:t[m.Symbol.IsAlias]?t[m.Symbol.Name]:t[m.Symbol.BaseName]}function Me(t,e){return{name:typeof t=="string"&&t.length>0?t:"",config:typeof t=="object"?t:e}}function po(t){if(typeof t!="object"||t===null||t.constructor.name!=="Object")return!1;if("logger"in t){const e=typeof t.logger;return!(e!=="boolean"&&(e!=="object"||typeof t.logger.logQuery!="function")&&e!=="undefined")}if("schema"in t){const e=typeof t.schema;return!(e!=="object"&&e!=="undefined")}if("casing"in t){const e=typeof t.casing;return!(e!=="string"&&e!=="undefined")}if("mode"in t)return!(t.mode!=="default"||t.mode!=="planetscale"||t.mode!==void 0);if("connection"in t){const e=typeof t.connection;return!(e!=="string"&&e!=="object"&&e!=="undefined")}if("client"in t){const e=typeof t.client;return!(e!=="object"&&e!=="function"&&e!=="undefined")}return Object.keys(t).length===0}class mo{static[f]="ConsoleLogWriter";write(e){console.log(e)}}class yo{static[f]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new mo}logQuery(e,s){const r=s.map(i=>{try{return JSON.stringify(i)}catch{return String(i)}}),n=r.length?` -- params: [${r.join(", ")}]`:"";this.writer.write(`Query: ${e}${n}`)}}class bo{static[f]="NoopLogger";logQuery(){}}const gs=Symbol.for("drizzle:PgInlineForeignKeys"),Ss=Symbol.for("drizzle:EnableRLS");class wo extends m{static[f]="PgTable";static Symbol=Object.assign({},m.Symbol,{InlineForeignKeys:gs,EnableRLS:Ss});[gs]=[];[Ss]=!1;[m.Symbol.ExtraConfigBuilder]=void 0;[m.Symbol.ExtraConfigColumns]={}}class go{static[f]="PgPrimaryKeyBuilder";columns;name;constructor(e,s){this.columns=e,this.name=s}build(e){return new So(e,this.columns,this.name)}}class So{constructor(e,s,r){this.table=e,this.columns=s,this.name=r}static[f]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[wo.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}function M(t,e){return uo(e)&&!Sr(t)&&!d(t,ue)&&!d(t,Ie)&&!d(t,$)&&!d(t,m)&&!d(t,$e)?new ue(t,e):t}const je=(t,e)=>u`${t} = ${M(e,t)}`,qo=(t,e)=>u`${t} <> ${M(e,t)}`;function Qt(...t){const e=t.filter(s=>s!==void 0);if(e.length!==0)return e.length===1?new b(e):new b([new B("("),u.join(e,new B(" and ")),new B(")")])}function _o(...t){const e=t.filter(s=>s!==void 0);if(e.length!==0)return e.length===1?new b(e):new b([new B("("),u.join(e,new B(" or ")),new B(")")])}function Co(t){return u`not ${t}`}const xo=(t,e)=>u`${t} > ${M(e,t)}`,Ro=(t,e)=>u`${t} >= ${M(e,t)}`,To=(t,e)=>u`${t} < ${M(e,t)}`,Io=(t,e)=>u`${t} <= ${M(e,t)}`;function Lo(t,e){return Array.isArray(e)?e.length===0?u`false`:u`${t} in ${e.map(s=>M(s,t))}`:u`${t} in ${M(e,t)}`}function No(t,e){return Array.isArray(e)?e.length===0?u`true`:u`${t} not in ${e.map(s=>M(s,t))}`:u`${t} not in ${M(e,t)}`}function vo(t){return u`${t} is null`}function $o(t){return u`${t} is not null`}function Ao(t){return u`exists ${t}`}function Bo(t){return u`not exists ${t}`}function Qo(t,e,s){return u`${t} between ${M(e,t)} and ${M(s,t)}`}function Eo(t,e,s){return u`${t} not between ${M(e,t)} and ${M(s,t)}`}function Oo(t,e){return u`${t} like ${e}`}function Po(t,e){return u`${t} not like ${e}`}function Uo(t,e){return u`${t} ilike ${e}`}function Do(t,e){return u`${t} not ilike ${e}`}function ko(t){return u`${t} asc`}function Mo(t){return u`${t} desc`}class xr{constructor(e,s,r){this.sourceTable=e,this.referencedTable=s,this.relationName=r,this.referencedTableName=s[m.Symbol.Name]}static[f]="Relation";referencedTableName;fieldName}class jo{constructor(e,s){this.table=e,this.config=s}static[f]="Relations"}class ye extends xr{constructor(e,s,r,n){super(e,s,r?.relationName),this.config=r,this.isNullable=n}static[f]="One";withFieldName(e){const s=new ye(this.sourceTable,this.referencedTable,this.config,this.isNullable);return s.fieldName=e,s}}class pt extends xr{constructor(e,s,r){super(e,s,r?.relationName),this.config=r}static[f]="Many";withFieldName(e){const s=new pt(this.sourceTable,this.referencedTable,this.config);return s.fieldName=e,s}}function Fo(){return{and:Qt,between:Qo,eq:je,exists:Ao,gt:xo,gte:Ro,ilike:Uo,inArray:Lo,isNull:vo,isNotNull:$o,like:Oo,lt:To,lte:Io,ne:qo,not:Co,notBetween:Eo,notExists:Bo,notLike:Po,notIlike:Do,notInArray:No,or:_o,sql:u}}function Vo(){return{sql:u,asc:ko,desc:Mo}}function zo(t,e){Object.keys(t).length===1&&"default"in t&&!d(t.default,m)&&(t=t.default);const s={},r={},n={};for(const[i,o]of Object.entries(t))if(d(o,m)){const a=De(o),l=r[a];s[a]=i,n[i]={tsName:i,dbName:o[m.Symbol.Name],schema:o[m.Symbol.Schema],columns:o[m.Symbol.Columns],relations:l?.relations??{},primaryKey:l?.primaryKey??[]};for(const c of Object.values(o[m.Symbol.Columns]))c.primary&&n[i].primaryKey.push(c);const h=o[m.Symbol.ExtraConfigBuilder]?.(o[m.Symbol.ExtraConfigColumns]);if(h)for(const c of Object.values(h))d(c,go)&&n[i].primaryKey.push(...c.columns)}else if(d(o,jo)){const a=De(o.table),l=s[a],h=o.config(e(o.table));let c;for(const[p,y]of Object.entries(h))if(l){const _=n[l];_.relations[p]=y}else a in r||(r[a]={relations:{},primaryKey:c}),r[a].relations[p]=y}return{tables:n,tableNamesMap:s}}function Ko(t){return function(s,r){return new ye(t,s,r,r?.fields.reduce((n,i)=>n&&i.notNull,!0)??!1)}}function Jo(t){return function(s,r){return new pt(t,s,r)}}function Wo(t,e,s){if(d(s,ye)&&s.config)return{fields:s.config.fields,references:s.config.references};const r=e[De(s.referencedTable)];if(!r)throw new Error(`Table "${s.referencedTable[m.Symbol.Name]}" not found in schema`);const n=t[r];if(!n)throw new Error(`Table "${r}" not found in schema`);const i=s.sourceTable,o=e[De(i)];if(!o)throw new Error(`Table "${i[m.Symbol.Name]}" not found in schema`);const a=[];for(const l of Object.values(n.relations))(s.relationName&&s!==l&&l.relationName===s.relationName||!s.relationName&&l.referencedTable===s.sourceTable)&&a.push(l);if(a.length>1)throw s.relationName?new Error(`There are multiple relations with name "${s.relationName}" in table "${r}"`):new Error(`There are multiple relations between "${r}" and "${s.sourceTable[m.Symbol.Name]}". Please specify relation name`);if(a[0]&&d(a[0],ye)&&a[0].config)return{fields:a[0].config.references,references:a[0].config.fields};throw new Error(`There is not enough information to infer relation "${o}.${s.fieldName}"`)}function Ho(t){return{one:Ko(t),many:Jo(t)}}function Et(t,e,s,r,n=i=>i){const i={};for(const[o,a]of r.entries())if(a.isJson){const l=e.relations[a.tsKey],h=s[o],c=typeof h=="string"?JSON.parse(h):h;i[a.tsKey]=d(l,ye)?c&&Et(t,t[a.relationTableTsKey],c,a.selection,n):c.map(p=>Et(t,t[a.relationTableTsKey],p,a.selection,n))}else{const l=n(s[o]),h=a.field;let c;d(h,$)?c=h:d(h,b)?c=h.decoder:c=h.sql.decoder,i[a.tsKey]=l===null?null:c.mapFromDriverValue(l)}return i}class at{constructor(e){this.table=e}static[f]="ColumnAliasProxyHandler";get(e,s){return s==="table"?this.table:e[s]}}class Zt{constructor(e,s){this.alias=e,this.replaceOriginalName=s}static[f]="TableAliasProxyHandler";get(e,s){if(s===m.Symbol.IsAlias)return!0;if(s===m.Symbol.Name)return this.alias;if(this.replaceOriginalName&&s===m.Symbol.OriginalName)return this.alias;if(s===E)return{...e[E],name:this.alias,isAlias:!0};if(s===m.Symbol.Columns){const n=e[m.Symbol.Columns];if(!n)return n;const i={};return Object.keys(n).map(o=>{i[o]=new Proxy(n[o],new at(new Proxy(e,this)))}),i}const r=e[s];return d(r,$)?new Proxy(r,new at(new Proxy(e,this))):r}}function St(t,e){return new Proxy(t,new Zt(e,!1))}function ee(t,e){return new Proxy(t,new at(new Proxy(t.table,new Zt(e,!1))))}function Rr(t,e){return new b.Aliased(lt(t.sql,e),t.fieldAlias)}function lt(t,e){return u.join(t.queryChunks.map(s=>d(s,$)?ee(s,e):d(s,b)?lt(s,e):d(s,b.Aliased)?Rr(s,e):s))}class U{static[f]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,s){if(s==="_")return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(s===E)return{...e[E],selectedFields:new Proxy(e[E].selectedFields,this)};if(typeof s=="symbol")return e[s];const n=(d(e,V)?e._.selectedFields:d(e,$e)?e[E].selectedFields:e)[s];if(d(n,b.Aliased)){if(this.config.sqlAliasedBehavior==="sql"&&!n.isSelectionField)return n.sql;const i=n.clone();return i.isSelectionField=!0,i}if(d(n,b)){if(this.config.sqlBehavior==="sql")return n;throw new Error(`You tried to reference "${s}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return d(n,$)?this.config.alias?new Proxy(n,new at(new Proxy(n.table,new Zt(this.config.alias,this.config.replaceOriginalName??!1)))):n:typeof n!="object"||n===null?n:new Proxy(n,new U(this.config))}}class be{static[f]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(s=>(e?.(),s),s=>{throw e?.(),s})}then(e,s){return this.execute().then(e,s)}}class Go{static[f]="SQLiteForeignKeyBuilder";reference;_onUpdate;_onDelete;constructor(e,s){this.reference=()=>{const{name:r,columns:n,foreignColumns:i}=e();return{name:r,columns:n,foreignTable:i[0].table,foreignColumns:i}},s&&(this._onUpdate=s.onUpdate,this._onDelete=s.onDelete)}onUpdate(e){return this._onUpdate=e,this}onDelete(e){return this._onDelete=e,this}build(e){return new Yo(e,this)}}class Yo{constructor(e,s){this.table=e,this.reference=s.reference,this.onUpdate=s._onUpdate,this.onDelete=s._onDelete}static[f]="SQLiteForeignKey";reference;onUpdate;onDelete;getName(){const{name:e,columns:s,foreignColumns:r}=this.reference(),n=s.map(a=>a.name),i=r.map(a=>a.name),o=[this.table[oe],...n,r[0].table[oe],...i];return e??`${o.join("_")}_fk`}}function Zo(t,e){return`${t[oe]}_${e.join("_")}_unique`}class H extends no{static[f]="SQLiteColumnBuilder";foreignKeyConfigs=[];references(e,s={}){return this.foreignKeyConfigs.push({ref:e,actions:s}),this}unique(e){return this.config.isUnique=!0,this.config.uniqueName=e,this}generatedAlwaysAs(e,s){return this.config.generated={as:e,type:"always",mode:s?.mode??"virtual"},this}buildForeignKeys(e,s){return this.foreignKeyConfigs.map(({ref:r,actions:n})=>((i,o)=>{const a=new Go(()=>{const l=i();return{columns:[e],foreignColumns:[l]}});return o.onUpdate&&a.onUpdate(o.onUpdate),o.onDelete&&a.onDelete(o.onDelete),a.build(s)})(r,n))}}class k extends ${constructor(e,s){s.uniqueName||(s.uniqueName=Zo(e,[s.name])),super(e,s),this.table=e}static[f]="SQLiteColumn"}class Xo extends H{static[f]="SQLiteBigIntBuilder";constructor(e){super(e,"bigint","SQLiteBigInt")}build(e){return new ea(e,this.config)}}class ea extends k{static[f]="SQLiteBigInt";getSQLType(){return"blob"}mapFromDriverValue(e){if(Buffer.isBuffer(e))return BigInt(e.toString());if(e instanceof ArrayBuffer){const s=new TextDecoder;return BigInt(s.decode(e))}return BigInt(String.fromCodePoint(...e))}mapToDriverValue(e){return Buffer.from(e.toString())}}class ta extends H{static[f]="SQLiteBlobJsonBuilder";constructor(e){super(e,"json","SQLiteBlobJson")}build(e){return new sa(e,this.config)}}class sa extends k{static[f]="SQLiteBlobJson";getSQLType(){return"blob"}mapFromDriverValue(e){if(Buffer.isBuffer(e))return JSON.parse(e.toString());if(e instanceof ArrayBuffer){const s=new TextDecoder;return JSON.parse(s.decode(e))}return JSON.parse(String.fromCodePoint(...e))}mapToDriverValue(e){return Buffer.from(JSON.stringify(e))}}class ra extends H{static[f]="SQLiteBlobBufferBuilder";constructor(e){super(e,"buffer","SQLiteBlobBuffer")}build(e){return new na(e,this.config)}}class na extends k{static[f]="SQLiteBlobBuffer";mapFromDriverValue(e){return Buffer.isBuffer(e)?e:Buffer.from(e)}getSQLType(){return"blob"}}function ia(t,e){const{name:s,config:r}=Me(t,e);return r?.mode==="json"?new ta(s):r?.mode==="bigint"?new Xo(s):new ra(s)}class oa extends H{static[f]="SQLiteCustomColumnBuilder";constructor(e,s,r){super(e,"custom","SQLiteCustomColumn"),this.config.fieldConfig=s,this.config.customTypeParams=r}build(e){return new aa(e,this.config)}}class aa extends k{static[f]="SQLiteCustomColumn";sqlName;mapTo;mapFrom;constructor(e,s){super(e,s),this.sqlName=s.customTypeParams.dataType(s.fieldConfig),this.mapTo=s.customTypeParams.toDriver,this.mapFrom=s.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return typeof this.mapFrom=="function"?this.mapFrom(e):e}mapToDriverValue(e){return typeof this.mapTo=="function"?this.mapTo(e):e}}function la(t){return(e,s)=>{const{name:r,config:n}=Me(e,s);return new oa(r,n,t)}}class Xt extends H{static[f]="SQLiteBaseIntegerBuilder";constructor(e,s,r){super(e,s,r),this.config.autoIncrement=!1}primaryKey(e){return e?.autoIncrement&&(this.config.autoIncrement=!0),this.config.hasDefault=!0,super.primaryKey()}}class es extends k{static[f]="SQLiteBaseInteger";autoIncrement=this.config.autoIncrement;getSQLType(){return"integer"}}class ua extends Xt{static[f]="SQLiteIntegerBuilder";constructor(e){super(e,"number","SQLiteInteger")}build(e){return new ca(e,this.config)}}class ca extends es{static[f]="SQLiteInteger"}class ha extends Xt{static[f]="SQLiteTimestampBuilder";constructor(e,s){super(e,"date","SQLiteTimestamp"),this.config.mode=s}defaultNow(){return this.default(u`(cast((julianday('now') - 2440587.5)*86400000 as integer))`)}build(e){return new da(e,this.config)}}class da extends es{static[f]="SQLiteTimestamp";mode=this.config.mode;mapFromDriverValue(e){return this.config.mode==="timestamp"?new Date(e*1e3):new Date(e)}mapToDriverValue(e){const s=e.getTime();return this.config.mode==="timestamp"?Math.floor(s/1e3):s}}class fa extends Xt{static[f]="SQLiteBooleanBuilder";constructor(e,s){super(e,"boolean","SQLiteBoolean"),this.config.mode=s}build(e){return new pa(e,this.config)}}class pa extends es{static[f]="SQLiteBoolean";mode=this.config.mode;mapFromDriverValue(e){return Number(e)===1}mapToDriverValue(e){return e?1:0}}function ne(t,e){const{name:s,config:r}=Me(t,e);return r?.mode==="timestamp"||r?.mode==="timestamp_ms"?new ha(s,r.mode):r?.mode==="boolean"?new fa(s,r.mode):new ua(s)}class ma extends H{static[f]="SQLiteNumericBuilder";constructor(e){super(e,"string","SQLiteNumeric")}build(e){return new ya(e,this.config)}}class ya extends k{static[f]="SQLiteNumeric";mapFromDriverValue(e){return typeof e=="string"?e:String(e)}getSQLType(){return"numeric"}}class ba extends H{static[f]="SQLiteNumericNumberBuilder";constructor(e){super(e,"number","SQLiteNumericNumber")}build(e){return new wa(e,this.config)}}class wa extends k{static[f]="SQLiteNumericNumber";mapFromDriverValue(e){return typeof e=="number"?e:Number(e)}mapToDriverValue=String;getSQLType(){return"numeric"}}class ga extends H{static[f]="SQLiteNumericBigIntBuilder";constructor(e){super(e,"bigint","SQLiteNumericBigInt")}build(e){return new Sa(e,this.config)}}class Sa extends k{static[f]="SQLiteNumericBigInt";mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){return"numeric"}}function qa(t,e){const{name:s,config:r}=Me(t,e),n=r?.mode;return n==="number"?new ba(s):n==="bigint"?new ga(s):new ma(s)}class _a extends H{static[f]="SQLiteRealBuilder";constructor(e){super(e,"number","SQLiteReal")}build(e){return new Ca(e,this.config)}}class Ca extends k{static[f]="SQLiteReal";getSQLType(){return"real"}}function xa(t){return new _a(t??"")}class Ra extends H{static[f]="SQLiteTextBuilder";constructor(e,s){super(e,"string","SQLiteText"),this.config.enumValues=s.enum,this.config.length=s.length}build(e){return new Ta(e,this.config)}}class Ta extends k{static[f]="SQLiteText";enumValues=this.config.enumValues;length=this.config.length;constructor(e,s){super(e,s)}getSQLType(){return`text${this.config.length?`(${this.config.length})`:""}`}}class Ia extends H{static[f]="SQLiteTextJsonBuilder";constructor(e){super(e,"json","SQLiteTextJson")}build(e){return new La(e,this.config)}}class La extends k{static[f]="SQLiteTextJson";getSQLType(){return"text"}mapFromDriverValue(e){return JSON.parse(e)}mapToDriverValue(e){return JSON.stringify(e)}}function se(t,e={}){const{name:s,config:r}=Me(t,e);return r.mode==="json"?new Ia(s):new Ra(s,r)}function Na(){return{blob:ia,customType:la,integer:ne,numeric:qa,real:xa,text:se}}const Ot=Symbol.for("drizzle:SQLiteInlineForeignKeys");class K extends m{static[f]="SQLiteTable";static Symbol=Object.assign({},m.Symbol,{InlineForeignKeys:Ot});[m.Symbol.Columns];[Ot]=[];[m.Symbol.ExtraConfigBuilder]=void 0}function va(t,e,s,r,n=t){const i=new K(t,r,n),o=typeof e=="function"?e(Na()):e,a=Object.fromEntries(Object.entries(o).map(([h,c])=>{const p=c;p.setName(h);const y=p.build(i);return i[Ot].push(...p.buildForeignKeys(y,i)),[h,y]})),l=Object.assign(i,a);return l[m.Symbol.Columns]=a,l[m.Symbol.ExtraConfigColumns]=a,l}const Tr=(t,e,s)=>va(t,e);function he(t){return d(t,K)?[`${t[m.Symbol.BaseName]}`]:d(t,V)?t._.usedTables??[]:d(t,b)?t.usedTables??[]:[]}class qs extends be{constructor(e,s,r,n){super(),this.table=e,this.session=s,this.dialect=r,this.config={table:e,withList:n}}static[f]="SQLiteDelete";config;where(e){return this.config.where=e,this}orderBy(...e){if(typeof e[0]=="function"){const s=e[0](new Proxy(this.config.table[m.Symbol.Columns],new U({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(s)?s:[s];this.config.orderBy=r}else{const s=e;this.config.orderBy=s}return this}limit(e){return this.config.limit=e,this}returning(e=this.table[K.Symbol.Columns]){return this.config.returning=me(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){const{typings:e,...s}=this.dialect.sqlToQuery(this.getSQL());return s}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0,void 0,{type:"delete",tables:he(this.config.table)})}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(e){return this._prepare().execute(e)}$dynamic(){return this}}function $a(t){return(t.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(s=>s.toLowerCase()).join("_")}function Aa(t){return(t.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((s,r,n)=>{const i=n===0?r.toLowerCase():`${r[0].toUpperCase()}${r.slice(1)}`;return s+i},"")}function Ba(t){return t}class Qa{static[f]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert=e==="snake_case"?$a:e==="camelCase"?Aa:Ba}getColumnCasing(e){if(!e.keyAsName)return e.name;const s=e.table[m.Symbol.Schema]??"public",r=e.table[m.Symbol.OriginalName],n=`${s}.${r}.${e.name}`;return this.cache[n]||this.cacheTable(e.table),this.cache[n]}cacheTable(e){const s=e[m.Symbol.Schema]??"public",r=e[m.Symbol.OriginalName],n=`${s}.${r}`;if(!this.cachedTables[n]){for(const i of Object.values(e[m.Symbol.Columns])){const o=`${n}.${i.name}`;this.cache[o]=this.convert(i.name)}this.cachedTables[n]=!0}}clearCache(){this.cache={},this.cachedTables={}}}class ts extends Error{static[f]="DrizzleError";constructor({message:e,cause:s}){super(e),this.name="DrizzleError",this.cause=s}}class Ea extends ts{static[f]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}class ss extends $e{static[f]="SQLiteViewBase"}class ut{static[f]="SQLiteDialect";casing;constructor(e){this.casing=new Qa(e?.casing)}escapeName(e){return`"${e}"`}escapeParam(e){return"?"}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;const s=[u`with `];for(const[r,n]of e.entries())s.push(u`${u.identifier(n._.alias)} as (${n._.sql})`),r<e.length-1&&s.push(u`, `);return s.push(u` `),u.join(s)}buildDeleteQuery({table:e,where:s,returning:r,withList:n,limit:i,orderBy:o}){const a=this.buildWithCTE(n),l=r?u` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,h=s?u` where ${s}`:void 0,c=this.buildOrderBy(o),p=this.buildLimit(i);return u`${a}delete from ${e}${h}${l}${c}${p}`}buildUpdateSet(e,s){const r=e[m.Symbol.Columns],n=Object.keys(r).filter(o=>s[o]!==void 0||r[o]?.onUpdateFn!==void 0),i=n.length;return u.join(n.flatMap((o,a)=>{const l=r[o],h=s[o]??u.param(l.onUpdateFn(),l),c=u`${u.identifier(this.casing.getColumnCasing(l))} = ${h}`;return a<i-1?[c,u.raw(", ")]:[c]}))}buildUpdateQuery({table:e,set:s,where:r,returning:n,withList:i,joins:o,from:a,limit:l,orderBy:h}){const c=this.buildWithCTE(i),p=this.buildUpdateSet(e,s),y=a&&u.join([u.raw(" from "),this.buildFromTable(a)]),_=this.buildJoins(o),S=n?u` returning ${this.buildSelection(n,{isSingleTable:!0})}`:void 0,w=r?u` where ${r}`:void 0,C=this.buildOrderBy(h),T=this.buildLimit(l);return u`${c}update ${e} set ${p}${y}${_}${w}${S}${C}${T}`}buildSelection(e,{isSingleTable:s=!1}={}){const r=e.length,n=e.flatMap(({field:i},o)=>{const a=[];if(d(i,b.Aliased)&&i.isSelectionField)a.push(u.identifier(i.fieldAlias));else if(d(i,b.Aliased)||d(i,b)){const l=d(i,b.Aliased)?i.sql:i;s?a.push(new b(l.queryChunks.map(h=>d(h,$)?u.identifier(this.casing.getColumnCasing(h)):h))):a.push(l),d(i,b.Aliased)&&a.push(u` as ${u.identifier(i.fieldAlias)}`)}else if(d(i,$)){const l=i.table[m.Symbol.Name];i.columnType==="SQLiteNumericBigInt"?s?a.push(u`cast(${u.identifier(this.casing.getColumnCasing(i))} as text)`):a.push(u`cast(${u.identifier(l)}.${u.identifier(this.casing.getColumnCasing(i))} as text)`):s?a.push(u.identifier(this.casing.getColumnCasing(i))):a.push(u`${u.identifier(l)}.${u.identifier(this.casing.getColumnCasing(i))}`)}return o<r-1&&a.push(u`, `),a});return u.join(n)}buildJoins(e){if(!e||e.length===0)return;const s=[];if(e)for(const[r,n]of e.entries()){r===0&&s.push(u` `);const i=n.table,o=n.on?u` on ${n.on}`:void 0;if(d(i,K)){const a=i[K.Symbol.Name],l=i[K.Symbol.Schema],h=i[K.Symbol.OriginalName],c=a===h?void 0:n.alias;s.push(u`${u.raw(n.joinType)} join ${l?u`${u.identifier(l)}.`:void 0}${u.identifier(h)}${c&&u` ${u.identifier(c)}`}${o}`)}else s.push(u`${u.raw(n.joinType)} join ${i}${o}`);r<e.length-1&&s.push(u` `)}return u.join(s)}buildLimit(e){return typeof e=="object"||typeof e=="number"&&e>=0?u` limit ${e}`:void 0}buildOrderBy(e){const s=[];if(e)for(const[r,n]of e.entries())s.push(n),r<e.length-1&&s.push(u`, `);return s.length>0?u` order by ${u.join(s)}`:void 0}buildFromTable(e){return d(e,m)&&e[m.Symbol.IsAlias]?u`${u`${u.identifier(e[m.Symbol.Schema]??"")}.`.if(e[m.Symbol.Schema])}${u.identifier(e[m.Symbol.OriginalName])} ${u.identifier(e[m.Symbol.Name])}`:e}buildSelectQuery({withList:e,fields:s,fieldsFlat:r,where:n,having:i,table:o,joins:a,orderBy:l,groupBy:h,limit:c,offset:p,distinct:y,setOperators:_}){const S=r??me(s);for(const z of S)if(d(z.field,$)&&qe(z.field.table)!==(d(o,V)?o._.alias:d(o,ss)?o[E].name:d(o,b)?void 0:qe(o))&&!(G=>a?.some(({alias:ze})=>ze===(G[m.Symbol.IsAlias]?qe(G):G[m.Symbol.BaseName])))(z.field.table)){const G=qe(z.field.table);throw new Error(`Your "${z.path.join("->")}" field references a column "${G}"."${z.field.name}", but the table "${G}" is not part of the query! Did you forget to join it?`)}const w=!a||a.length===0,C=this.buildWithCTE(e),T=y?u` distinct`:void 0,O=this.buildSelection(S,{isSingleTable:w}),P=this.buildFromTable(o),x=this.buildJoins(a),Z=n?u` where ${n}`:void 0,j=i?u` having ${i}`:void 0,g=[];if(h)for(const[z,G]of h.entries())g.push(G),z<h.length-1&&g.push(u`, `);const I=g.length>0?u` group by ${u.join(g)}`:void 0,X=this.buildOrderBy(l),Ve=this.buildLimit(c),yt=p?u` offset ${p}`:void 0,we=u`${C}select${T} ${O} from ${P}${x}${Z}${I}${j}${X}${Ve}${yt}`;return _.length>0?this.buildSetOperations(we,_):we}buildSetOperations(e,s){const[r,...n]=s;if(!r)throw new Error("Cannot pass undefined values to any set operator");return n.length===0?this.buildSetOperationQuery({leftSelect:e,setOperator:r}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:r}),n)}buildSetOperationQuery({leftSelect:e,setOperator:{type:s,isAll:r,rightSelect:n,limit:i,orderBy:o,offset:a}}){const l=u`${e.getSQL()} `,h=u`${n.getSQL()}`;let c;if(o&&o.length>0){const S=[];for(const w of o)if(d(w,k))S.push(u.identifier(w.name));else if(d(w,b)){for(let C=0;C<w.queryChunks.length;C++){const T=w.queryChunks[C];d(T,k)&&(w.queryChunks[C]=u.identifier(this.casing.getColumnCasing(T)))}S.push(u`${w}`)}else S.push(u`${w}`);c=u` order by ${u.join(S,u`, `)}`}const p=typeof i=="object"||typeof i=="number"&&i>=0?u` limit ${i}`:void 0,y=u.raw(`${s} ${r?"all ":""}`),_=a?u` offset ${a}`:void 0;return u`${l}${y}${h}${c}${p}${_}`}buildInsertQuery({table:e,values:s,onConflict:r,returning:n,withList:i,select:o}){const a=[],l=e[m.Symbol.Columns],h=Object.entries(l).filter(([w,C])=>!C.shouldDisableInsert()),c=h.map(([,w])=>u.identifier(this.casing.getColumnCasing(w)));if(o){const w=s;d(w,b)?a.push(w):a.push(w.getSQL())}else{const w=s;a.push(u.raw("values "));for(const[C,T]of w.entries()){const O=[];for(const[P,x]of h){const Z=T[P];if(Z===void 0||d(Z,ue)&&Z.value===void 0){let j;if(x.default!==null&&x.default!==void 0)j=d(x.default,b)?x.default:u.param(x.default,x);else if(x.defaultFn!==void 0){const g=x.defaultFn();j=d(g,b)?g:u.param(g,x)}else if(!x.default&&x.onUpdateFn!==void 0){const g=x.onUpdateFn();j=d(g,b)?g:u.param(g,x)}else j=u`null`;O.push(j)}else O.push(Z)}a.push(O),C<w.length-1&&a.push(u`, `)}}const p=this.buildWithCTE(i),y=u.join(a),_=n?u` returning ${this.buildSelection(n,{isSingleTable:!0})}`:void 0,S=r?.length?u.join(r):void 0;return u`${p}insert into ${e} ${c} ${y}${S}${_}`}sqlToQuery(e,s){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,invokeSource:s})}buildRelationalQuery({fullSchema:e,schema:s,tableNamesMap:r,table:n,tableConfig:i,queryConfig:o,tableAlias:a,nestedQueryRelation:l,joinOn:h}){let c=[],p,y,_=[],S;const w=[];if(o===!0)c=Object.entries(i.columns).map(([O,P])=>({dbKey:P.name,tsKey:O,field:ee(P,a),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{const T=Object.fromEntries(Object.entries(i.columns).map(([g,I])=>[g,ee(I,a)]));if(o.where){const g=typeof o.where=="function"?o.where(T,Fo()):o.where;S=g&&lt(g,a)}const O=[];let P=[];if(o.columns){let g=!1;for(const[I,X]of Object.entries(o.columns))X!==void 0&&I in i.columns&&(!g&&X===!0&&(g=!0),P.push(I));P.length>0&&(P=g?P.filter(I=>o.columns?.[I]===!0):Object.keys(i.columns).filter(I=>!P.includes(I)))}else P=Object.keys(i.columns);for(const g of P){const I=i.columns[g];O.push({tsKey:g,value:I})}let x=[];o.with&&(x=Object.entries(o.with).filter(g=>!!g[1]).map(([g,I])=>({tsKey:g,queryConfig:I,relation:i.relations[g]})));let Z;if(o.extras){Z=typeof o.extras=="function"?o.extras(T,{sql:u}):o.extras;for(const[g,I]of Object.entries(Z))O.push({tsKey:g,value:Rr(I,a)})}for(const{tsKey:g,value:I}of O)c.push({dbKey:d(I,b.Aliased)?I.fieldAlias:i.columns[g].name,tsKey:g,field:d(I,$)?ee(I,a):I,relationTableTsKey:void 0,isJson:!1,selection:[]});let j=typeof o.orderBy=="function"?o.orderBy(T,Vo()):o.orderBy??[];Array.isArray(j)||(j=[j]),_=j.map(g=>d(g,$)?ee(g,a):lt(g,a)),p=o.limit,y=o.offset;for(const{tsKey:g,queryConfig:I,relation:X}of x){const Ve=Wo(s,r,X),yt=De(X.referencedTable),we=r[yt],z=`${a}_${g}`,G=Qt(...Ve.fields.map((Ar,Br)=>je(ee(Ve.references[Br],z),ee(Ar,a)))),ze=this.buildRelationalQuery({fullSchema:e,schema:s,tableNamesMap:r,table:e[we],tableConfig:s[we],queryConfig:d(X,ye)?I===!0?{limit:1}:{...I,limit:1}:I,tableAlias:z,joinOn:G,nestedQueryRelation:X}),$r=u`(${ze.sql})`.as(g);c.push({dbKey:g,tsKey:g,field:$r,relationTableTsKey:we,isJson:!0,selection:ze.selection})}}if(c.length===0)throw new ts({message:`No fields selected for table "${i.tsName}" ("${a}"). You need to have at least one item in "columns", "with" or "extras". If you need to select all columns, omit the "columns" key or set it to undefined.`});let C;if(S=Qt(h,S),l){let T=u`json_array(${u.join(c.map(({field:x})=>d(x,k)?u.identifier(this.casing.getColumnCasing(x)):d(x,b.Aliased)?x.sql:x),u`, `)})`;d(l,pt)&&(T=u`coalesce(json_group_array(${T}), json_array())`);const O=[{dbKey:"data",tsKey:"data",field:T.as("data"),isJson:!0,relationTableTsKey:i.tsName,selection:c}];p!==void 0||y!==void 0||_.length>0?(C=this.buildSelectQuery({table:St(n,a),fields:{},fieldsFlat:[{path:[],field:u.raw("*")}],where:S,limit:p,offset:y,orderBy:_,setOperators:[]}),S=void 0,p=void 0,y=void 0,_=void 0):C=St(n,a),C=this.buildSelectQuery({table:d(C,K)?C:new V(C,{},a),fields:{},fieldsFlat:O.map(({field:x})=>({path:[],field:d(x,$)?ee(x,a):x})),joins:w,where:S,limit:p,offset:y,orderBy:_,setOperators:[]})}else C=this.buildSelectQuery({table:St(n,a),fields:{},fieldsFlat:c.map(({field:T})=>({path:[],field:d(T,$)?ee(T,a):T})),joins:w,where:S,limit:p,offset:y,orderBy:_,setOperators:[]});return{tableTsKey:i.tsName,sql:C,selection:c}}}class Oa extends ut{static[f]="SQLiteSyncDialect";migrate(e,s,r){const n=r===void 0||typeof r=="string"?"__drizzle_migrations":r.migrationsTable??"__drizzle_migrations",i=u`
			CREATE TABLE IF NOT EXISTS ${u.identifier(n)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at numeric
			)
		`;s.run(i);const a=s.values(u`SELECT id, hash, created_at FROM ${u.identifier(n)} ORDER BY created_at DESC LIMIT 1`)[0]??void 0;s.run(u`BEGIN`);try{for(const l of e)if(!a||Number(a[2])<l.folderMillis){for(const h of l.sql)s.run(u.raw(h));s.run(u`INSERT INTO ${u.identifier(n)} ("hash", "created_at") VALUES(${l.hash}, ${l.folderMillis})`)}s.run(u`COMMIT`)}catch(l){throw s.run(u`ROLLBACK`),l}}}class Pa extends ut{static[f]="SQLiteAsyncDialect";async migrate(e,s,r){const n=r===void 0||typeof r=="string"?"__drizzle_migrations":r.migrationsTable??"__drizzle_migrations",i=u`
			CREATE TABLE IF NOT EXISTS ${u.identifier(n)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at numeric
			)
		`;await s.run(i);const a=(await s.values(u`SELECT id, hash, created_at FROM ${u.identifier(n)} ORDER BY created_at DESC LIMIT 1`))[0]??void 0;await s.transaction(async l=>{for(const h of e)if(!a||Number(a[2])<h.folderMillis){for(const c of h.sql)await l.run(u.raw(c));await l.run(u`INSERT INTO ${u.identifier(n)} ("hash", "created_at") VALUES(${h.hash}, ${h.folderMillis})`)}})}}class Ua{static[f]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}class ie{static[f]="SQLiteSelectBuilder";fields;session;dialect;withList;distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,this.withList=e.withList,this.distinct=e.distinct}from(e){const s=!!this.fields;let r;return this.fields?r=this.fields:d(e,V)?r=Object.fromEntries(Object.keys(e._.selectedFields).map(n=>[n,e[n]])):d(e,ss)?r=e[E].selectedFields:d(e,b)?r={}:r=fo(e),new Ir({table:e,fields:r,isPartialSelect:s,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct})}}class Da extends Ua{static[f]="SQLiteSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;cacheConfig=void 0;usedTables=new Set;constructor({table:e,fields:s,isPartialSelect:r,session:n,dialect:i,withList:o,distinct:a}){super(),this.config={withList:o,table:e,fields:{...s},distinct:a,setOperators:[]},this.isPartialSelect=r,this.session=n,this.dialect=i,this._={selectedFields:s,config:this.config},this.tableName=Bt(e),this.joinsNotNullableMap=typeof this.tableName=="string"?{[this.tableName]:!0}:{};for(const l of he(e))this.usedTables.add(l)}getUsedTables(){return[...this.usedTables]}createJoin(e){return(s,r)=>{const n=this.tableName,i=Bt(s);for(const o of he(s))this.usedTables.add(o);if(typeof i=="string"&&this.config.joins?.some(o=>o.alias===i))throw new Error(`Alias "${i}" is already used in this query`);if(!this.isPartialSelect&&(Object.keys(this.joinsNotNullableMap).length===1&&typeof n=="string"&&(this.config.fields={[n]:this.config.fields}),typeof i=="string"&&!d(s,b))){const o=d(s,V)?s._.selectedFields:d(s,$e)?s[E].selectedFields:s[m.Symbol.Columns];this.config.fields[i]=o}if(typeof r=="function"&&(r=r(new Proxy(this.config.fields,new U({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:r,table:s,joinType:e,alias:i}),typeof i=="string")switch(e){case"left":{this.joinsNotNullableMap[i]=!1;break}case"right":{this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([o])=>[o,!1])),this.joinsNotNullableMap[i]=!0;break}case"cross":case"inner":{this.joinsNotNullableMap[i]=!0;break}case"full":{this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([o])=>[o,!1])),this.joinsNotNullableMap[i]=!1;break}}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");crossJoin=this.createJoin("cross");createSetOperator(e,s){return r=>{const n=typeof r=="function"?r(ka()):r;if(!Yt(this.getSelectedFields(),n.getSelectedFields()))throw new Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:s,rightSelect:n}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);except=this.createSetOperator("except",!1);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return typeof e=="function"&&(e=e(new Proxy(this.config.fields,new U({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return typeof e=="function"&&(e=e(new Proxy(this.config.fields,new U({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if(typeof e[0]=="function"){const s=e[0](new Proxy(this.config.fields,new U({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(s)?s:[s]}else this.config.groupBy=e;return this}orderBy(...e){if(typeof e[0]=="function"){const s=e[0](new Proxy(this.config.fields,new U({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(s)?s:[s];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=r:this.config.orderBy=r}else{const s=e;this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=s:this.config.orderBy=s}return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){const{typings:e,...s}=this.dialect.sqlToQuery(this.getSQL());return s}as(e){const s=[];if(s.push(...he(this.config.table)),this.config.joins)for(const r of this.config.joins)s.push(...he(r.table));return new Proxy(new V(this.getSQL(),this.config.fields,e,!1,[...new Set(s)]),new U({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new U({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class Ir extends Da{static[f]="SQLiteSelect";_prepare(e=!0){if(!this.session)throw new Error("Cannot execute a query on a query builder. Please use a database instance instead.");const s=me(this.config.fields),r=this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),s,"all",!0,void 0,{type:"select",tables:[...this.usedTables]},this.cacheConfig);return r.joinsNotNullableMap=this.joinsNotNullableMap,r}$withCache(e){return this.cacheConfig=e===void 0?{config:{},enable:!0,autoInvalidate:!0}:e===!1?{enable:!1}:{enable:!0,autoInvalidate:!0,...e},this}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.all()}}ho(Ir,[be]);function mt(t,e){return(s,r,...n)=>{const i=[r,...n].map(o=>({type:t,isAll:e,rightSelect:o}));for(const o of i)if(!Yt(s.getSelectedFields(),o.rightSelect.getSelectedFields()))throw new Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return s.addSetOperators(i)}}const ka=()=>({union:Ma,unionAll:ja,intersect:Fa,except:Va}),Ma=mt("union",!1),ja=mt("union",!0),Fa=mt("intersect",!1),Va=mt("except",!1);class Lr{static[f]="SQLiteQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=d(e,ut)?e:void 0,this.dialectConfig=d(e,ut)?void 0:e}$with=(e,s)=>{const r=this;return{as:i=>(typeof i=="function"&&(i=i(r)),new Proxy(new gr(i.getSQL(),s??("getSelectedFields"in i?i.getSelectedFields()??{}:{}),e,!0),new U({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){const s=this;function r(i){return new ie({fields:i??void 0,session:void 0,dialect:s.getDialect(),withList:e})}function n(i){return new ie({fields:i??void 0,session:void 0,dialect:s.getDialect(),withList:e,distinct:!0})}return{select:r,selectDistinct:n}}select(e){return new ie({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new ie({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}getDialect(){return this.dialect||(this.dialect=new Oa(this.dialectConfig)),this.dialect}}class _s{constructor(e,s,r,n){this.table=e,this.session=s,this.dialect=r,this.withList=n}static[f]="SQLiteInsertBuilder";values(e){if(e=Array.isArray(e)?e:[e],e.length===0)throw new Error("values() must be called with at least one value");const s=e.map(r=>{const n={},i=this.table[m.Symbol.Columns];for(const o of Object.keys(r)){const a=r[o];n[o]=d(a,b)?a:new ue(a,i[o])}return n});return new Cs(this.table,s,this.session,this.dialect,this.withList)}select(e){const s=typeof e=="function"?e(new Lr):e;if(!d(s,b)&&!Yt(this.table[$t],s._.selectedFields))throw new Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new Cs(this.table,s,this.session,this.dialect,this.withList,!0)}}class Cs extends be{constructor(e,s,r,n,i,o){super(),this.session=r,this.dialect=n,this.config={table:e,values:s,withList:i,select:o}}static[f]="SQLiteInsert";config;returning(e=this.config.table[K.Symbol.Columns]){return this.config.returning=me(e),this}onConflictDoNothing(e={}){if(this.config.onConflict||(this.config.onConflict=[]),e.target===void 0)this.config.onConflict.push(u` on conflict do nothing`);else{const s=Array.isArray(e.target)?u`${e.target}`:u`${[e.target]}`,r=e.where?u` where ${e.where}`:u``;this.config.onConflict.push(u` on conflict ${s} do nothing${r}`)}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw new Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');this.config.onConflict||(this.config.onConflict=[]);const s=e.where?u` where ${e.where}`:void 0,r=e.targetWhere?u` where ${e.targetWhere}`:void 0,n=e.setWhere?u` where ${e.setWhere}`:void 0,i=Array.isArray(e.target)?u`${e.target}`:u`${[e.target]}`,o=this.dialect.buildUpdateSet(this.config.table,Cr(this.config.table,e.set));return this.config.onConflict.push(u` on conflict ${i}${r} do update set ${o}${s}${n}`),this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){const{typings:e,...s}=this.dialect.sqlToQuery(this.getSQL());return s}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0,void 0,{type:"insert",tables:he(this.config.table)})}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.config.returning?this.all():this.run()}$dynamic(){return this}}class xs{constructor(e,s,r,n){this.table=e,this.session=s,this.dialect=r,this.withList=n}static[f]="SQLiteUpdateBuilder";set(e){return new za(this.table,Cr(this.table,e),this.session,this.dialect,this.withList)}}class za extends be{constructor(e,s,r,n,i){super(),this.session=r,this.dialect=n,this.config={set:s,table:e,withList:i,joins:[]}}static[f]="SQLiteUpdate";config;from(e){return this.config.from=e,this}createJoin(e){return(s,r)=>{const n=Bt(s);if(typeof n=="string"&&this.config.joins.some(i=>i.alias===n))throw new Error(`Alias "${n}" is already used in this query`);if(typeof r=="function"){const i=this.config.from?d(s,K)?s[m.Symbol.Columns]:d(s,V)?s._.selectedFields:d(s,ss)?s[E].selectedFields:void 0:void 0;r=r(new Proxy(this.config.table[m.Symbol.Columns],new U({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),i&&new Proxy(i,new U({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}return this.config.joins.push({on:r,table:s,joinType:e,alias:n}),this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}orderBy(...e){if(typeof e[0]=="function"){const s=e[0](new Proxy(this.config.table[m.Symbol.Columns],new U({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(s)?s:[s];this.config.orderBy=r}else{const s=e;this.config.orderBy=s}return this}limit(e){return this.config.limit=e,this}returning(e=this.config.table[K.Symbol.Columns]){return this.config.returning=me(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){const{typings:e,...s}=this.dialect.sqlToQuery(this.getSQL());return s}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0,void 0,{type:"insert",tables:he(this.config.table)})}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.config.returning?this.all():this.run()}$dynamic(){return this}}class ct extends b{constructor(e){super(ct.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.session=e.session,this.sql=ct.buildCount(e.source,e.filters)}sql;static[f]="SQLiteCountBuilderAsync";[Symbol.toStringTag]="SQLiteCountBuilderAsync";session;static buildEmbeddedCount(e,s){return u`(select count(*) from ${e}${u.raw(" where ").if(s)}${s})`}static buildCount(e,s){return u`select count(*) from ${e}${u.raw(" where ").if(s)}${s}`}then(e,s){return Promise.resolve(this.session.count(this.sql)).then(e,s)}catch(e){return this.then(void 0,e)}finally(e){return this.then(s=>(e?.(),s),s=>{throw e?.(),s})}}class Ka{constructor(e,s,r,n,i,o,a,l){this.mode=e,this.fullSchema=s,this.schema=r,this.tableNamesMap=n,this.table=i,this.tableConfig=o,this.dialect=a,this.session=l}static[f]="SQLiteAsyncRelationalQueryBuilder";findMany(e){return this.mode==="sync"?new Rs(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many"):new Pt(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return this.mode==="sync"?new Rs(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first"):new Pt(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class Pt extends be{constructor(e,s,r,n,i,o,a,l,h){super(),this.fullSchema=e,this.schema=s,this.tableNamesMap=r,this.table=n,this.tableConfig=i,this.dialect=o,this.session=a,this.config=l,this.mode=h}static[f]="SQLiteAsyncRelationalQuery";mode;getSQL(){return this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}).sql}_prepare(e=!1){const{query:s,builtQuery:r}=this._toSQL();return this.session[e?"prepareOneTimeQuery":"prepareQuery"](r,void 0,this.mode==="first"?"get":"all",!0,(n,i)=>{const o=n.map(a=>Et(this.schema,this.tableConfig,a,s.selection,i));return this.mode==="first"?o[0]:o})}prepare(){return this._prepare(!1)}_toSQL(){const e=this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}),s=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:s}}toSQL(){return this._toSQL().builtQuery}executeRaw(){return this.mode==="first"?this._prepare(!1).get():this._prepare(!1).all()}async execute(){return this.executeRaw()}}class Rs extends Pt{static[f]="SQLiteSyncRelationalQuery";sync(){return this.executeRaw()}}class We extends be{constructor(e,s,r,n,i){super(),this.execute=e,this.getSQL=s,this.dialect=n,this.mapBatchResult=i,this.config={action:r}}static[f]="SQLiteRaw";config;getQuery(){return{...this.dialect.sqlToQuery(this.getSQL()),method:this.config.action}}mapResult(e,s){return s?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class Nr{constructor(e,s,r,n){this.resultKind=e,this.dialect=s,this.session=r,this._=n?{schema:n.schema,fullSchema:n.fullSchema,tableNamesMap:n.tableNamesMap}:{schema:void 0,fullSchema:{},tableNamesMap:{}},this.query={};const i=this.query;if(this._.schema)for(const[o,a]of Object.entries(this._.schema))i[o]=new Ka(e,n.fullSchema,this._.schema,this._.tableNamesMap,n.fullSchema[o],a,s,r);this.$cache={invalidate:async o=>{}}}static[f]="BaseSQLiteDatabase";query;$with=(e,s)=>{const r=this;return{as:i=>(typeof i=="function"&&(i=i(new Lr(r.dialect))),new Proxy(new gr(i.getSQL(),s??("getSelectedFields"in i?i.getSelectedFields()??{}:{}),e,!0),new U({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,s){return new ct({source:e,filters:s,session:this.session})}with(...e){const s=this;function r(l){return new ie({fields:l??void 0,session:s.session,dialect:s.dialect,withList:e})}function n(l){return new ie({fields:l??void 0,session:s.session,dialect:s.dialect,withList:e,distinct:!0})}function i(l){return new xs(l,s.session,s.dialect,e)}function o(l){return new _s(l,s.session,s.dialect,e)}function a(l){return new qs(l,s.session,s.dialect,e)}return{select:r,selectDistinct:n,update:i,insert:o,delete:a}}select(e){return new ie({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new ie({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}update(e){return new xs(e,this.session,this.dialect)}$cache;insert(e){return new _s(e,this.session,this.dialect)}delete(e){return new qs(e,this.session,this.dialect)}run(e){const s=typeof e=="string"?u.raw(e):e.getSQL();return this.resultKind==="async"?new We(async()=>this.session.run(s),()=>s,"run",this.dialect,this.session.extractRawRunValueFromBatchResult.bind(this.session)):this.session.run(s)}all(e){const s=typeof e=="string"?u.raw(e):e.getSQL();return this.resultKind==="async"?new We(async()=>this.session.all(s),()=>s,"all",this.dialect,this.session.extractRawAllValueFromBatchResult.bind(this.session)):this.session.all(s)}get(e){const s=typeof e=="string"?u.raw(e):e.getSQL();return this.resultKind==="async"?new We(async()=>this.session.get(s),()=>s,"get",this.dialect,this.session.extractRawGetValueFromBatchResult.bind(this.session)):this.session.get(s)}values(e){const s=typeof e=="string"?u.raw(e):e.getSQL();return this.resultKind==="async"?new We(async()=>this.session.values(s),()=>s,"values",this.dialect,this.session.extractRawValuesValueFromBatchResult.bind(this.session)):this.session.values(s)}transaction(e,s){return this.session.transaction(e,s)}}class Ja{static[f]="Cache"}class vr extends Ja{strategy(){return"all"}static[f]="NoopCache";async get(e){}async put(e,s,r,n){}async onMutate(e){}}async function Ts(t,e){const s=`${t}-${JSON.stringify(e)}`,n=new TextEncoder().encode(s),i=await crypto.subtle.digest("SHA-256",n);return[...new Uint8Array(i)].map(l=>l.toString(16).padStart(2,"0")).join("")}class re extends Error{constructor(e,s,r){super(`Failed query: ${e}
params: ${s}`),this.query=e,this.params=s,this.cause=r,Error.captureStackTrace(this,re),r&&(this.cause=r)}}class Wa extends be{constructor(e){super(),this.resultCb=e}static[f]="ExecuteResultSync";async execute(){return this.resultCb()}sync(){return this.resultCb()}}class Ha{constructor(e,s,r,n,i,o){this.mode=e,this.executeMethod=s,this.query=r,this.cache=n,this.queryMetadata=i,this.cacheConfig=o,n&&n.strategy()==="all"&&o===void 0&&(this.cacheConfig={enable:!0,autoInvalidate:!0}),this.cacheConfig?.enable||(this.cacheConfig=void 0)}static[f]="PreparedQuery";joinsNotNullableMap;async queryWithCache(e,s,r){if(this.cache===void 0||d(this.cache,vr)||this.queryMetadata===void 0)try{return await r()}catch(n){throw new re(e,s,n)}if(this.cacheConfig&&!this.cacheConfig.enable)try{return await r()}catch(n){throw new re(e,s,n)}if((this.queryMetadata.type==="insert"||this.queryMetadata.type==="update"||this.queryMetadata.type==="delete")&&this.queryMetadata.tables.length>0)try{const[n]=await Promise.all([r(),this.cache.onMutate({tables:this.queryMetadata.tables})]);return n}catch(n){throw new re(e,s,n)}if(!this.cacheConfig)try{return await r()}catch(n){throw new re(e,s,n)}if(this.queryMetadata.type==="select"){const n=await this.cache.get(this.cacheConfig.tag??await Ts(e,s),this.queryMetadata.tables,this.cacheConfig.tag!==void 0,this.cacheConfig.autoInvalidate);if(n===void 0){let i;try{i=await r()}catch(o){throw new re(e,s,o)}return await this.cache.put(this.cacheConfig.tag??await Ts(e,s),i,this.cacheConfig.autoInvalidate?this.queryMetadata.tables:[],this.cacheConfig.tag!==void 0,this.cacheConfig.config),i}return n}try{return await r()}catch(n){throw new re(e,s,n)}}getQuery(){return this.query}mapRunResult(e,s){return e}mapAllResult(e,s){throw new Error("Not implemented")}mapGetResult(e,s){throw new Error("Not implemented")}execute(e){return this.mode==="async"?this[this.executeMethod](e):new Wa(()=>this[this.executeMethod](e))}mapResult(e,s){switch(this.executeMethod){case"run":return this.mapRunResult(e,s);case"all":return this.mapAllResult(e,s);case"get":return this.mapGetResult(e,s)}}}class Ga{constructor(e){this.dialect=e}static[f]="SQLiteSession";prepareOneTimeQuery(e,s,r,n,i,o,a){return this.prepareQuery(e,s,r,n,i,o,a)}run(e){const s=this.dialect.sqlToQuery(e);try{return this.prepareOneTimeQuery(s,void 0,"run",!1).run()}catch(r){throw new ts({cause:r,message:`Failed to run the query '${s.sql}'`})}}extractRawRunValueFromBatchResult(e){return e}all(e){return this.prepareOneTimeQuery(this.dialect.sqlToQuery(e),void 0,"run",!1).all()}extractRawAllValueFromBatchResult(e){throw new Error("Not implemented")}get(e){return this.prepareOneTimeQuery(this.dialect.sqlToQuery(e),void 0,"run",!1).get()}extractRawGetValueFromBatchResult(e){throw new Error("Not implemented")}values(e){return this.prepareOneTimeQuery(this.dialect.sqlToQuery(e),void 0,"run",!1).values()}async count(e){return(await this.values(e))[0][0]}extractRawValuesValueFromBatchResult(e){throw new Error("Not implemented")}}class Ya extends Nr{constructor(e,s,r,n,i=0){super(e,s,r,n),this.schema=n,this.nestedIndex=i}static[f]="SQLiteTransaction";rollback(){throw new Ea}}class rs extends Ga{constructor(e,s,r,n,i){super(s),this.client=e,this.schema=r,this.options=n,this.tx=i,this.logger=n.logger??new bo,this.cache=n.cache??new vr}static[f]="LibSQLSession";logger;cache;prepareQuery(e,s,r,n,i,o,a){return new Za(this.client,e,this.logger,this.cache,o,a,s,this.tx,r,n,i)}async batch(e){const s=[],r=[];for(const i of e){const o=i._prepare(),a=o.getQuery();s.push(o),r.push({sql:a.sql,args:a.params})}return(await this.client.batch(r)).map((i,o)=>s[o].mapResult(i,!0))}async migrate(e){const s=[],r=[];for(const i of e){const o=i._prepare(),a=o.getQuery();s.push(o),r.push({sql:a.sql,args:a.params})}return(await this.client.migrate(r)).map((i,o)=>s[o].mapResult(i,!0))}async transaction(e,s){const r=await this.client.transaction(),n=new rs(this.client,this.dialect,this.schema,this.options,r),i=new ns("async",this.dialect,n,this.schema);try{const o=await e(i);return await r.commit(),o}catch(o){throw await r.rollback(),o}}extractRawAllValueFromBatchResult(e){return e.rows}extractRawGetValueFromBatchResult(e){return e.rows[0]}extractRawValuesValueFromBatchResult(e){return e.rows}}class ns extends Ya{static[f]="LibSQLTransaction";async transaction(e){const s=`sp${this.nestedIndex}`,r=new ns("async",this.dialect,this.session,this.schema,this.nestedIndex+1);await this.session.run(u.raw(`savepoint ${s}`));try{const n=await e(r);return await this.session.run(u.raw(`release savepoint ${s}`)),n}catch(n){throw await this.session.run(u.raw(`rollback to savepoint ${s}`)),n}}}class Za extends Ha{constructor(e,s,r,n,i,o,a,l,h,c,p){super("async",h,s,n,i,o),this.client=e,this.logger=r,this.fields=a,this.tx=l,this._isResponseInArrayMode=c,this.customResultMapper=p,this.customResultMapper=p,this.fields=a}static[f]="LibSQLPreparedQuery";async run(e){const s=Je(this.query.params,e??{});return this.logger.logQuery(this.query.sql,s),await this.queryWithCache(this.query.sql,s,async()=>{const r={sql:this.query.sql,args:s};return this.tx?this.tx.execute(r):this.client.execute(r)})}async all(e){const{fields:s,logger:r,query:n,tx:i,client:o,customResultMapper:a}=this;if(!s&&!a){const h=Je(n.params,e??{});return r.logQuery(n.sql,h),await this.queryWithCache(n.sql,h,async()=>{const c={sql:n.sql,args:h};return(i?i.execute(c):o.execute(c)).then(({rows:p})=>this.mapAllResult(p))})}const l=await this.values(e);return this.mapAllResult(l)}mapAllResult(e,s){return s&&(e=e.rows),!this.fields&&!this.customResultMapper?e.map(r=>Is(r)):this.customResultMapper?this.customResultMapper(e,He):e.map(r=>ws(this.fields,Array.prototype.slice.call(r).map(n=>He(n)),this.joinsNotNullableMap))}async get(e){const{fields:s,logger:r,query:n,tx:i,client:o,customResultMapper:a}=this;if(!s&&!a){const h=Je(n.params,e??{});return r.logQuery(n.sql,h),await this.queryWithCache(n.sql,h,async()=>{const c={sql:n.sql,args:h};return(i?i.execute(c):o.execute(c)).then(({rows:p})=>this.mapGetResult(p))})}const l=await this.values(e);return this.mapGetResult(l)}mapGetResult(e,s){s&&(e=e.rows);const r=e[0];if(!this.fields&&!this.customResultMapper)return Is(r);if(r)return this.customResultMapper?this.customResultMapper(e,He):ws(this.fields,Array.prototype.slice.call(r).map(n=>He(n)),this.joinsNotNullableMap)}async values(e){const s=Je(this.query.params,e??{});return this.logger.logQuery(this.query.sql,s),await this.queryWithCache(this.query.sql,s,async()=>{const r={sql:this.query.sql,args:s};return(this.tx?this.tx.execute(r):this.client.execute(r)).then(({rows:n})=>n)})}isResponseInArrayMode(){return this._isResponseInArrayMode}}function Is(t){return Object.keys(t).reduce((e,s)=>(Object.prototype.propertyIsEnumerable.call(t,s)&&(e[s]=t[s]),e),{})}function He(t){if(typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer){if(typeof Buffer<"u")return t instanceof Buffer?t:Buffer.from(t);if(typeof TextDecoder<"u")return new TextDecoder().decode(t);throw new Error("TextDecoder is not available. Please provide either Buffer or TextDecoder polyfill.")}return t}class Xa extends Nr{static[f]="LibSQLDatabase";async batch(e){return this.session.batch(e)}}function Qe(t,e={}){const s=new Pa({casing:e.casing});let r;e.logger===!0?r=new yo:e.logger!==!1&&(r=e.logger);let n;if(e.schema){const a=zo(e.schema,Ho);n={fullSchema:e.schema,schema:a.tables,tableNamesMap:a.tableNamesMap}}const i=new rs(t,s,n,{logger:r,cache:e.cache},void 0),o=new Xa("async",s,i,n);return o.$client=t,o.$cache=e.cache,o.$cache&&(o.$cache.invalidate=e.cache?.onMutate),o}function Ut(...t){if(typeof t[0]=="string"){const e=Ze({url:t[0]});return Qe(e,t[1])}if(po(t[0])){const{connection:e,client:s,...r}=t[0];if(s)return Qe(s,r);const n=Ze(typeof e=="string"?{url:e}:e);return Qe(n,r)}return Qe(t[0],t[1])}(t=>{function e(s){return Qe({},s)}t.mock=e})(Ut||(Ut={}));const te=Tr("users",{id:ne("id").primaryKey({autoIncrement:!0}),dingTalkUnionId:se("dingTalkUnionId").notNull().unique(),isAdmin:ne("isAdmin",{mode:"boolean"}).notNull().default(!1),token:ne("token").notNull().default(2e4),requestTimes:ne("requestTimes").notNull().default(0),dingTalkUserId:se("dingTalkUserId"),name:se("name"),avatar:se("avatar"),mobile:se("mobile"),createdAt:se("createdAt").notNull().default(u`(datetime('now'))`),updatedAt:se("updatedAt").notNull().default(u`(datetime('now'))`)}),el=Tr("statistics",{id:ne("id").primaryKey({autoIncrement:!0}),totalRequestTimes:ne("totalRequestTimes").notNull().default(0),totalTokenUsage:ne("totalTokenUsage").notNull().default(0)}),tl=Object.freeze(Object.defineProperty({__proto__:null,statistics:el,users:te},Symbol.toStringTag,{value:"Module"})),sl=Ze({url:"libsql://sinochem-agent-vercel-icfg-************************.aws-ap-northeast-1.turso.io",authToken:"***************************************************************************************************************************************************************************************************************************************************************************"}),Fe=Ut(sl,{schema:tl});async function ul(){try{return await Fe.select().from(te)}catch(t){throw console.error("Error getting all users:",t),t}}async function cl(t,e){try{const s=new Date().toISOString();return await Fe.update(te).set({...e,updatedAt:s}).where(je(te.dingTalkUnionId,t)).returning()}catch(s){throw console.error("Error updating user:",s),s}}async function hl(t){try{await Fe.delete(te).where(je(te.dingTalkUnionId,t))}catch(e){throw console.error("Error deleting user:",e),e}}async function dl(t){try{const e=await Fe.select().from(te).where(je(te.dingTalkUnionId,t));return e.length>0?e[0]:null}catch(e){throw console.error("Error getting user by DingTalk UnionId:",e),e}}async function fl(t){try{const e={dingTalkUnionId:t.dingTalkUnionId,dingTalkUserId:t.dingTalkUserId,name:t.name||"钉钉用户",avatar:t.avatar,mobile:t.mobile,isAdmin:!1,token:2e4,requestTimes:0};return(await Fe.insert(te).values(e).returning())[0]}catch(e){throw console.error("Error creating user with DingTalk info:",e),e}}export{ul as a,fl as c,hl as d,dl as g,cl as u};
