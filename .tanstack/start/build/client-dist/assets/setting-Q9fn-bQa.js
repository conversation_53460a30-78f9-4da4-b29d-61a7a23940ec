import{j as e,r as d}from"./main-CJJWzuV9.js";import{B as r}from"./button-8ggP9MaO.js";import{C as o,a as x,b as m,c as h,d as j}from"./card-f1EGyQL4.js";import{S as y}from"./separator-Bkne-Rv6.js";import{B as u}from"./badge-DOzKpig8.js";import{c as t}from"./createLucideIcon-BR-plM33.js";import"./index-DFx3G0N8.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],f=t("bell",N);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],b=t("database",g);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],k=t("globe",v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],w=t("monitor",C);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],_=t("moon",S);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]],M=t("palette",z);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],A=t("save",$);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],U=t("shield",T);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],V=t("sun",B);function E(){const[l,c]=d.useState("system"),[n,a]=d.useState("medium");return e.jsxs(o,{children:[e.jsxs(x,{children:[e.jsxs(m,{className:"flex items-center",children:[e.jsx(M,{className:"h-5 w-5 mr-2"}),"外观设置"]}),e.jsx(h,{children:"自定义应用的外观和主题"})]}),e.jsxs(j,{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"主题模式"}),e.jsx("div",{className:"flex space-x-3",children:[{value:"light",label:"浅色",icon:V},{value:"dark",label:"深色",icon:_},{value:"system",label:"跟随系统",icon:w}].map(({value:s,label:i,icon:p})=>e.jsxs("button",{onClick:()=>c(s),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l===s?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:bg-gray-50"}`,children:[e.jsx(p,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:i})]},s))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"字体大小"}),e.jsx("div",{className:"flex space-x-3",children:[{value:"small",label:"小"},{value:"medium",label:"中"},{value:"large",label:"大"}].map(({value:s,label:i})=>e.jsx("button",{onClick:()=>a(s),className:`px-4 py-2 rounded-lg border transition-colors ${n===s?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:bg-gray-50"}`,children:e.jsx("span",{className:"text-sm",children:i})},s))})]})]})]})}function L(){const[l,c]=d.useState({dingtalk:!0,push:!1,sound:!0,desktop:!0}),n=a=>{c(s=>({...s,[a]:!s[a]}))};return e.jsxs(o,{children:[e.jsxs(x,{children:[e.jsxs(m,{className:"flex items-center",children:[e.jsx(f,{className:"h-5 w-5 mr-2"}),"通知设置"]}),e.jsx(h,{children:"管理您接收通知的方式"})]}),e.jsx(j,{className:"space-y-4",children:[{key:"dingtalk",label:"钉钉通知",description:"接收重要更新的钉钉通知"},{key:"push",label:"推送通知",description:"接收浏览器推送通知"},{key:"sound",label:"声音提醒",description:"播放通知声音"},{key:"desktop",label:"桌面通知",description:"显示桌面通知"}].map(({key:a,label:s,description:i})=>e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:s}),e.jsx("p",{className:"text-xs text-gray-500",children:i})]}),e.jsx("button",{onClick:()=>n(a),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${l[a]?"bg-blue-600":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${l[a]?"translate-x-6":"translate-x-1"}`})})]},a))})]})}function q(){const[l,c]=d.useState("zh-CN"),[n,a]=d.useState("Asia/Shanghai");return e.jsxs(o,{children:[e.jsxs(x,{children:[e.jsxs(m,{className:"flex items-center",children:[e.jsx(k,{className:"h-5 w-5 mr-2"}),"语言和地区"]}),e.jsx(h,{children:"设置您的语言偏好和时区"})]}),e.jsxs(j,{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"显示语言"}),e.jsxs("select",{value:l,onChange:s=>c(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"zh-CN",children:"简体中文"}),e.jsx("option",{value:"zh-TW",children:"繁體中文"}),e.jsx("option",{value:"en-US",children:"English (US)"}),e.jsx("option",{value:"ja-JP",children:"日本語"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"时区"}),e.jsxs("select",{value:n,onChange:s=>a(s.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"Asia/Shanghai",children:"中国标准时间 (UTC+8)"}),e.jsx("option",{value:"Asia/Tokyo",children:"日本标准时间 (UTC+9)"}),e.jsx("option",{value:"America/New_York",children:"美国东部时间 (UTC-5)"}),e.jsx("option",{value:"Europe/London",children:"格林威治时间 (UTC+0)"})]})]})]})]})}function D(){return e.jsxs(o,{children:[e.jsxs(x,{children:[e.jsxs(m,{className:"flex items-center",children:[e.jsx(U,{className:"h-5 w-5 mr-2"}),"隐私与安全"]}),e.jsx(h,{children:"管理您的隐私设置和安全选项"})]}),e.jsx(j,{className:"space-y-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"数据收集"}),e.jsx("p",{className:"text-xs text-gray-500",children:"允许收集匿名使用数据以改进服务"})]}),e.jsx(u,{variant:"outline",children:"已启用"})]}),e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Cookie 设置"}),e.jsx("p",{className:"text-xs text-gray-500",children:"管理网站 Cookie 和跟踪设置"})]}),e.jsx(r,{variant:"outline",size:"sm",children:"管理"})]}),e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"两步验证"}),e.jsx("p",{className:"text-xs text-gray-500",children:"为您的账户添加额外的安全保护"})]}),e.jsx(r,{variant:"outline",size:"sm",children:"设置"})]})]})})]})}function H(){return e.jsxs(o,{children:[e.jsxs(x,{children:[e.jsxs(m,{className:"flex items-center",children:[e.jsx(b,{className:"h-5 w-5 mr-2"}),"数据管理"]}),e.jsx(h,{children:"管理您的数据存储和备份设置"})]}),e.jsxs(j,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"自动备份"}),e.jsx("p",{className:"text-xs text-gray-500",children:"自动备份您的对话记录和设置"})]}),e.jsx(u,{children:"每日备份"})]}),e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:"导出数据"}),e.jsx("p",{className:"text-xs text-gray-500",children:"下载您的所有数据副本"})]}),e.jsx(r,{variant:"outline",size:"sm",children:"导出"})]}),e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium  text-red-600",children:"删除账户"}),e.jsx("p",{className:"text-xs text-gray-500",children:"永久删除您的账户和所有数据"})]}),e.jsx(r,{variant:"outline",size:"sm",className:"text-red-600 border-red-200 hover:bg-red-50",children:"删除"})]})]}),e.jsx(y,{className:"my-6"}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(r,{className:"flex items-center",children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),"保存所有设置"]})})]})]})}const Y=function(){return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-4xl mx-auto p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"系统设置"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"管理您的应用偏好设置和系统配置"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(E,{}),e.jsx(L,{}),e.jsx(q,{}),e.jsx(D,{}),e.jsx(H,{})]})]})})};export{Y as component};
