import { eq, or } from 'drizzle-orm';
import { db } from './drizzle';
import { users, type User, type NewUser } from './schema';
import bcrypt from 'bcryptjs';


// 获取所有用户
export async function getAllUsers(): Promise<User[]> {
	try {
		return await db.select().from(users);
	} catch (error) {
		console.error("Error getting all users:", error);
		throw error;
	}
}


// 更新用户信息
export async function updateUser(
	dingTalkUnionId: string,
	updates: Partial<Omit<User, 'id' | 'dingTalkUnionId' | 'createdAt'>>
): Promise<User[]> {
	try {
		const now = new Date().toISOString();
		return await db
			.update(users)
			.set({ ...updates, updatedAt: now })
			.where(eq(users.dingTalkUnionId, dingTalkUnionId))
			.returning();
	} catch (error) {
		console.error("Error updating user:", error);
		throw error;
	}
}

// 删除用户
export async function deleteUser(dingTalkUnionId: string): Promise<void> {
	try {
		await db.delete(users).where(eq(users.dingTalkUnionId, dingTalkUnionId));
	} catch (error) {
		console.error("Error deleting user:", error);
		throw error;
	}
}

// 根据钉钉UnionId获取用户
export async function getUserByDingTalkUnionId(dingTalkUnionId: string): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(eq(users.dingTalkUnionId, dingTalkUnionId));
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by DingTalk UnionId:", error);
		throw error;
	}
}

// 创建用户（包含完整的钉钉信息）
export async function createUserWithDingTalkInfo(
	dingTalkInfo: {
		dingTalkUnionId: string; // 必须提供UnionId
		dingTalkUserId?: string;
		name?: string;
		avatar?: string;
		mobile?: string;
	}
): Promise<User> {
	try {
		const newUser: NewUser = {
			dingTalkUnionId: dingTalkInfo.dingTalkUnionId,
			dingTalkUserId: dingTalkInfo.dingTalkUserId,
			name: dingTalkInfo.name || '钉钉用户',
			avatar: dingTalkInfo.avatar,
			mobile: dingTalkInfo.mobile,
			isAdmin: false, // 默认非管理员
			token: 20000, // 默认 token
			requestTimes: 0, // 默认请求次数
		};
		const result = await db.insert(users).values(newUser).returning();
		return result[0];
	} catch (error) {
		console.error("Error creating user with DingTalk info:", error);
		throw error;
	}
}

// 获取或创建用户
export async function getOrCreateUser(dingTalkUnionId: string): Promise<User> {
	// 先尝试获取用户
	let user = await getUserByDingTalkUnionId(dingTalkUnionId);

	if (!user) {
		// 用户不存在，创建新用户
		const dingTalkInfo = {
			dingTalkUnionId,
			name: '新用户',
			mobile: '未设置',
		};

		user = await createUserWithDingTalkInfo(dingTalkInfo);
		console.log("创建新用户:", user);
	} else {
		console.log("用户已存在:", user);
	}

	return user;
}

// 根据邮箱获取用户
export async function getUserByEmail(email: string): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(eq(users.email, email));
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by email:", error);
		throw error;
	}
}

// 创建邮箱用户
export async function createUserWithEmail(
	userData: {
		email: string;
		password: string;
		name?: string;
	}
): Promise<User> {
	try {
		// 检查邮箱是否已存在
		const existingUser = await getUserByEmail(userData.email);
		if (existingUser) {
			throw new Error('Email already exists');
		}

		// 加密密码
		const saltRounds = 10;
		const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

		const newUser: NewUser = {
			email: userData.email,
			password: hashedPassword,
			name: userData.name || '新用户',
			isAdmin: false,
			token: 20000,
			requestTimes: 0,
		};

		const result = await db.insert(users).values(newUser).returning();
		return result[0];
	} catch (error) {
		console.error("Error creating user with email:", error);
		throw error;
	}
}

// 验证用户密码
export async function verifyUserPassword(email: string, password: string): Promise<User | null> {
	try {
		const user = await getUserByEmail(email);
		if (!user || !user.password) {
			return null;
		}

		const isPasswordValid = await bcrypt.compare(password, user.password);
		if (!isPasswordValid) {
			return null;
		}

		return user;
	} catch (error) {
		console.error("Error verifying user password:", error);
		throw error;
	}
}

// 根据邮箱或钉钉UnionId获取用户
export async function getUserByEmailOrDingTalk(identifier: string): Promise<User | null> {
	try {
		const result = await db.select().from(users).where(
			or(
				eq(users.email, identifier),
				eq(users.dingTalkUnionId, identifier)
			)
		);
		return result.length > 0 ? result[0] : null;
	} catch (error) {
		console.error("Error getting user by email or DingTalk:", error);
		throw error;
	}
}

