"use client";

import * as React from "react";
import { LogIn } from "lucide-react";

/**
 * 简洁的登录组件
 * 仅支持钉钉登录
 */
const SignIn = () => {
	/**
	 * 处理钉钉登录
	 */
	const handleDingTalkLogin = () => {
		// 钉钉应用配置
		const clientId = import.meta.env.VITE_DINGTALK_CLIENT_ID || "your_client_id";
		const redirectUri = encodeURIComponent(
			window.location.origin + "/auth/dingtalk/callback"
		);
		const state = "dingtalk_login";

		if (!clientId || clientId === "your_client_id") {
			alert("钉钉应用配置不完整，请检查环境变量 VITE_DINGTALK_CLIENT_ID");
			return;
		}

		// 构建钉钉OAuth2.0授权URL
		const authUrl = `https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=openid%20corpid&prompt=consent`;

		// 跳转到钉钉授权页面
		window.location.href = authUrl;
	};

	return (
		<div className="min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1">
			<div className="w-full max-w-sm bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black">
				{/* 登录图标 */}
				<div className="flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5">
					<LogIn className="w-7 h-7 text-black" />
				</div>

				{/* 标题和描述 */}
				<h2 className="text-2xl font-semibold mb-2 text-center">钉钉登录</h2>
				<p className="text-gray-500 text-sm mb-8 text-center">
					使用钉钉账户安全登录
				</p>

				{/* 钉钉登录按钮 */}
				<button
					onClick={handleDingTalkLogin}
					className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition mb-6 flex items-center justify-center gap-2"
				>
					<svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
					</svg>
					使用钉钉登录
				</button>

				{/* 帮助信息 */}
				<div className="text-center text-sm text-gray-500">
					登录即表示您同意我们的{" "}
					<a
						href="/terms"
						className="text-blue-600 hover:underline font-medium"
					>
						服务条款
					</a>{" "}
					和{" "}
					<a
						href="/privacy"
						className="text-blue-600 hover:underline font-medium"
					>
						隐私政策
					</a>
				</div>
			</div>
		</div>
	);
};

export { SignIn };
