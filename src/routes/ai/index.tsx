import { createFileRoute } from "@tanstack/react-router";
import { MultimodalChatInput } from "@/components/ui/multimodal-input";
import { FileUpload } from "@/components/ui/file-upload";
import { Tab } from "@/components/ui/tab";
import { ConversationSidebar } from "@/components/conversation-sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ContextCard } from "@/components/ui/context-card";
import { UserAvatar } from "@/components/ui/user-avatar";
import { PanelLeftClose, PanelLeftOpen, Copy } from "lucide-react";
import {
	ChatBubble,
	ChatBubbleAvatar,
	ChatBubbleMessage,
	ChatBubbleAction,
	ChatBubbleActionWrapper,
} from "@/components/ui/chat-bubble";
import { ProtectedRoute } from "@/components/auth/protected-route";
import * as React from "react";

export const Route = createFileRoute("/ai/")({
	component: RouteComponent,
});

// AI功能选项
const AI_MODES = ["知识库查询", "方案改进"];

// 文档引用类型定义
interface DocReference {
	index_id: string;
	doc_name: string;
	text: string;
	page_number?: number[];
	title?: string;
	doc_id?: string;
	images?: any[];
}

// 消息类型定义
interface Message {
	id: string;
	content: string;
	sender: "user" | "ai";
	timestamp: Date;
	isLoading?: boolean;
	docReferences?: DocReference[];
	expandedReferences?: Set<number>;
}

// API响应类型定义
interface ApiResponse {
	output: {
		text: string;
		session_id: string;
		finish_reason?: string;
		reject_status?: boolean;
		doc_references?: DocReference[];
	};
	usage: {
		models: Array<{
			model_id: string;
			input_tokens: number;
			output_tokens: number;
		}>;
	};
	request_id: string;
}

/**
 * AI 聊天页面组件
 * 集成了多模态输入功能，支持文本和文件输入
 * 需要用户登录后才能访问
 *
 * 多轮对话功能说明：
 * 1. 首次对话时，不传递session_id，API会返回新的session_id
 * 2. 后续对话时，传递之前获取的session_id，实现上下文连续性
 * 3. 用户可以点击"新对话"按钮清空会话状态，开始新的对话
 * 4. 会话ID会在界面上显示（部分），让用户了解当前对话状态
 *
 * API配置：
 * - 需要在环境变量中设置 DASHSCOPE_API_KEY 和 YOUR_APP_ID
 * - 或者直接在代码中替换相应的常量值
 */
function RouteComponent() {
	return (
		<ProtectedRoute>
			<AIChat />
		</ProtectedRoute>
	);
}

/**
 * AI 聊天主要组件
 * 包含所有聊天功能的实现
 */
function AIChat() {
	// Tab选择状态
	const [selectedMode, setSelectedMode] = React.useState(AI_MODES[0]);
	// 侧边栏状态
	const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(false);
	// 消息列表状态
	const [messages, setMessages] = React.useState<Message[]>([]);
	// 会话ID状态，用于多轮对话
	const [sessionId, setSessionId] = React.useState<string | null>(null);
	// 展开的文档引用状态
	const [expandedReferences, setExpandedReferences] = React.useState<{
		[messageId: string]: Set<number>;
	}>({});
	// API配置
	const API_KEY = import.meta.env.VITE_DASHSCOPE_API_KEY || "YOUR_API_KEY";
	const APP_ID = import.meta.env.VITE_YOUR_APP_ID || "YOUR_APP_ID";

	// 文件选择状态（仅用于UI显示）
	const [selectedFile, setSelectedFile] = React.useState<File | null>(null);

	// 切换侧边栏
	const toggleSidebar = () => {
		setIsSidebarCollapsed(!isSidebarCollapsed);
	};

	// 切换文档引用展开状态
	const toggleReferenceExpansion = (messageId: string, refIndex: number) => {
		setExpandedReferences((prev) => {
			const messageExpanded = prev[messageId] || new Set();
			const newExpanded = new Set(messageExpanded);
			if (newExpanded.has(refIndex)) {
				newExpanded.delete(refIndex);
			} else {
				newExpanded.add(refIndex);
			}
			return {
				...prev,
				[messageId]: newExpanded,
			};
		});
	};

	/**
	 * 调用DashScope API进行多轮对话
	 * @param prompt 用户输入的问题
	 * @param currentSessionId 当前会话ID（可选）
	 * @returns API响应
	 */
	const callDashScopeAPI = async (
		prompt: string,
		currentSessionId?: string
	): Promise<ApiResponse> => {
		// 调试：打印API配置信息
		console.log("=== API调用调试信息 ===");
		console.log("API_KEY:", API_KEY);
		console.log("APP_ID:", APP_ID);
		console.log(
			"环境变量 VITE_DASHSCOPE_API_KEY:",
			import.meta.env.VITE_DASHSCOPE_API_KEY
		);
		console.log("环境变量 VITE_YOUR_APP_ID:", import.meta.env.VITE_YOUR_APP_ID);
		console.log("========================");

		const url = `https://dashscope.aliyuncs.com/api/v1/apps/${APP_ID}/completion`;

		const requestBody: any = {
			input: {
				prompt: prompt,
			},
			parameters: {},
			debug: {},
		};

		// 如果有会话ID，添加到请求中以实现多轮对话
		if (currentSessionId) {
			requestBody.input.session_id = currentSessionId;
		}

		const response = await fetch(url, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${API_KEY}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
		}

		return await response.json();
	};

	// 发送消息处理函数
	const handleSendMessage = async (content: string) => {
		const userMessage: Message = {
			id: Date.now().toString(),
			content,
			sender: "user",
			timestamp: new Date(),
		};

		// 添加用户消息
		setMessages((prev) => [...prev, userMessage]);

		// 添加AI加载状态消息
		const loadingMessage: Message = {
			id: (Date.now() + 1).toString(),
			content: "",
			sender: "ai",
			timestamp: new Date(),
			isLoading: true,
		};
		setMessages((prev) => [...prev, loadingMessage]);

		try {
			// 调用DashScope API进行多轮对话
			const apiResponse = await callDashScopeAPI(
				content,
				sessionId || undefined
			);

			// 更新会话ID（首次对话时获取，后续对话时保持）
			if (apiResponse.output.session_id) {
				setSessionId(apiResponse.output.session_id);
			}

			// 更新AI回复消息
			const aiResponse: Message = {
				id: loadingMessage.id,
				content: apiResponse.output.text,
				sender: "ai",
				timestamp: new Date(),
				isLoading: false,
				docReferences: apiResponse.output.doc_references,
			};
			setMessages((prev) =>
				prev.map((msg) => (msg.id === loadingMessage.id ? aiResponse : msg))
			);
		} catch (error) {
			console.error("API调用失败:", error);
			// 显示错误消息
			const errorMessage: Message = {
				id: loadingMessage.id,
				content: `抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${error instanceof Error ? error.message : "未知错误"}`,
				sender: "ai",
				timestamp: new Date(),
				isLoading: false,
			};
			setMessages((prev) =>
				prev.map((msg) => (msg.id === loadingMessage.id ? errorMessage : msg))
			);
		}
	};

	// 复制消息内容
	const handleCopyMessage = async (content: string) => {
		try {
			await navigator.clipboard.writeText(content);
			// 可以在这里添加成功提示，比如 toast 通知
			console.log("复制成功");
		} catch (err) {
			console.error("复制失败:", err);
			// 降级方案：使用传统的复制方法
			const textArea = document.createElement("textarea");
			textArea.value = content;
			document.body.appendChild(textArea);
			textArea.select();
			document.execCommand("copy");
			document.body.removeChild(textArea);
		}
	};

	/**
	 * 处理文件上传（仅UI展示，无实际功能）
	 */
	const handleFileUpload = () => {
		if (!selectedFile) {
			alert("请先选择一个文件");
			return;
		}
		// 仅用于UI展示，无实际上传功能
		alert("文件上传功能暂未实现");
	};




	return (
		<ConversationSidebar
			isCollapsed={isSidebarCollapsed}
			onToggle={toggleSidebar}
		>
			<div className="h-screen bg-gray-50 flex flex-col">
				{/* Tab导航区域 - 固定在顶部 */}
				<div className="bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0">
					<div className="flex items-center justify-between">
						{/* 侧边栏切换按钮 */}
						<Button
							variant="ghost"
							size="sm"
							onClick={toggleSidebar}
							className="h-8 w-8 p-0"
							title={
								isSidebarCollapsed
									? "展开侧边栏 (Ctrl+B)"
									: "收起侧边栏 (Ctrl+B)"
							}
						>
							{isSidebarCollapsed ? (
								<PanelLeftOpen className="h-4 w-4" />
							) : (
								<PanelLeftClose className="h-4 w-4" />
							)}
						</Button>

						{/* Tab选择器 */}
						<div className="flex justify-center flex-1">
							<div className="flex w-fit rounded-full bg-muted p-1">
								{AI_MODES.map((mode) => (
									<Tab
										key={mode}
										text={mode}
										selected={selectedMode === mode}
										setSelected={setSelectedMode}
										discount={mode === "方案改进"}
									/>
								))}
							</div>
						</div>

						{/* 用户头像 */}
						<UserAvatar className="ml-4" />
					</div>
				</div>

				{/* 聊天区域 - 可滚动的主要内容区域 */}
				<main className="flex-1 flex flex-col min-h-0 overflow-hidden">
					{selectedMode === "知识库查询" ? (
						<>
							{/* 消息列表区域 - 可滚动 */}
							<div className="flex-1 overflow-y-auto p-6 pb-0 min-h-0">
								{messages.length === 0 ? (
									<div className="text-center text-gray-500 mt-20">
								<p className="text-lg mb-2">👋 欢迎使用库无忧</p>
								<p>开始对话，快速检索石化油储行业</p>
										{sessionId && (
											<p className="text-sm mt-4 text-blue-600">
												🔗 多轮对话已启用 (会话ID: {sessionId.slice(0, 8)}...)
											</p>
										)}
									</div>
								) : (
									<div className="max-w-4xl mx-auto space-y-4">
										{messages.map((message) => {
											const variant =
												message.sender === "user" ? "sent" : "received";
											return (
												<div key={message.id} className="space-y-2">
													{/* AI消息的文档引用badges */}
													{message.sender === "ai" &&
														message.docReferences &&
														message.docReferences.length > 0 && (
															<div className="space-y-2 mb-2 ml-12">
																<div className="flex flex-wrap gap-2">
																	{message.docReferences.map((ref, index) => {
																		const isExpanded =
																			expandedReferences[message.id]?.has(
																				index
																			) || false;
																		return (
																			<div key={index} className="space-y-1">
																				<ContextCard.Trigger
																					content={ref.doc_name}
																					side="top"
																				>
																					<Badge
																						variant="outline"
																						className="text-xs cursor-pointer hover:bg-gray-100"
																						onClick={() =>
																							toggleReferenceExpansion(
																								message.id,
																								index
																							)
																						}
																					>
																						{ref.index_id}
																					</Badge>
																				</ContextCard.Trigger>
																				{isExpanded && (
																					<div className="mt-2 p-3 bg-gray-50 rounded-md border text-sm">
																						<div className="font-medium text-gray-700 mb-1">
																							{ref.doc_name}
																						</div>
																						<div className="text-gray-600">
																							{ref.text}
																						</div>
																					</div>
																				)}
																			</div>
																		);
																	})}
																</div>
															</div>
														)}
													<ChatBubble variant={variant}>
														<ChatBubbleAvatar
															src={
																variant === "sent"
																	? "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=64&h=64&q=80&crop=faces&fit=crop"
																	: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=64&h=64&q=80&crop=faces&fit=crop"
															}
															fallback={variant === "sent" ? "用户" : "AI"}
														/>
														<div className="flex-1">
															<ChatBubbleMessage
																variant={variant}
																isLoading={message.isLoading}
															>
																{message.content}
															</ChatBubbleMessage>
															{/* AI消息的操作按钮 */}
															{message.sender === "ai" &&
																!message.isLoading && (
																	<ChatBubbleActionWrapper>
																		<ChatBubbleAction
																			icon={<Copy className="h-3 w-3" />}
																			onClick={() =>
																				handleCopyMessage(message.content)
																			}
																		/>
																	</ChatBubbleActionWrapper>
																)}
														</div>
													</ChatBubble>
												</div>
											);
										})}
									</div>
								)}
							</div>

							{/* 输入区域 - 固定在底部 */}
							<div className="w-full flex justify-center flex-shrink-0 p-6 pt-0">
								<div className="w-full max-w-3xl">
									<MultimodalChatInput onSend={handleSendMessage} />
								</div>
							</div>
						</>
					) : (
						<>
							{/* 方案改进模式 */}
							<div className="flex-1 flex flex-col justify-center items-center p-6">
								<div className="text-center text-gray-500 mb-6">
									<p className="text-lg mb-2">📄 方案改进</p>
									<p>上传文件进行智能分析和优化建议</p>
								</div>
								<div className="w-full max-w-md space-y-4">
									<FileUpload onFileSelect={setSelectedFile} />



									<Button
										onClick={handleFileUpload}
										disabled={!selectedFile}
										className="w-full"
									>
										上传文件（演示）
									</Button>
								</div>
							</div>
						</>
					)}
				</main>
			</div>
		</ConversationSidebar>
	);
}
