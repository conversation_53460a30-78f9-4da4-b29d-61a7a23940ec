# 要想在阿里云百炼上传文件，需要注意以下几点：
您可以通过本文默认的公网终端节点https://dashscope.aliyuncs.com/api/v1/访问百炼平台，首先申请文件上传租约，通过ApplyFileUploadLease接口。
这是接口的文档
我的 workspaceId 为	llm-lfveoh9ou5gxbu1r
POST /{WorkspaceId}/datacenter/category/{CategoryId} HTTP/1.1
路径参数
字段名称	字段详情
CategoryId
string
上传用于构建知识库的非结构化数据时：此处允许传入 default，即使用系统创建的“默认类目”。

WorkspaceId
string
上传文档所属的业务空间 ID。获取方式请参见如何使用业务空间。

示例值:
llm-3z7uw7fwz0vexxxx
请求参数
字段名称	字段详情
FileName
string
上传用于构建知识库的非结构化数据时：展开详情

示例值:
XXXX产品清单.pdf
参考取值来源:
ListFile
DescribeFile
Md5
string
上传文档的 MD5 值，服务端会验证该字段（当前暂未开启），请正确填写。

示例值:
19657c391f6c70bcea63c154d8606bb3
字符长度 <= 64
字符长度 >= 1
SizeInBytes
string
上传文档的大小，单位字节，服务端会验证该字段（当前暂未开启），请正确填写。取值范围：1B-100M。

示例值:
1000
CategoryType
string
类目类型，不传入该参数时，默认值为 UNSTRUCTURED。取值范围：展开详情

示例值:
UNSTRUCTURED
枚举值:
UNSTRUCTURED
SESSION_FILE
UseInternalEndpoint
boolean
若您使用了阿里云百炼安全存储空间，需要生成仅阿里云同地域内网可访问的租约 URL 链接时，此处可传入 true，以提高安全性。不传入该参数时，默认值为 false，即生成公网可访问的租约 URL。展开详情

示例值:
false
枚举值:
返回参数
字段名称	字段详情
Code
string
错误状态码。

示例值:
DataCenter.FileTooLarge
Data
object
接口业务数据字段。

Message
string
错误信息。

示例值:
User not authorized to operate on the specified resource
RequestId
string
请求 ID。

示例值:
778C0B3B-xxxx-5FC1-A947-36EDD13606AB
Status
string
接口返回的状态码。

示例值:
200
Success
boolean
接口调用是否成功，可能值为：展开详情

示例值:
true


说明

此接口响应中的Data.FileUploadLeaseId、Data.Param.Method、Data.Param.Url、Data.Param.Headers.X-bailian-extra和Data.Param.Headers.Content-Type字段的值请妥善保存，它们将用于后续的上传步骤。

此接口响应中的Data.Param.Url字段的值（即租约）有效期为分钟级，请尽快上传文档，以免租约过期导致无法上传。

上传文件至阿里云百炼的临时存储

示例代码如下，请根据代码提示替换上一步获取的实际字段值，然后运行代码。若响应为“File uploaded successfully.”，则表示上传成功。

示例代码

多语言示例请自行编写。

PythonJava
 
# 示例代码仅供参考，请勿在生产环境中直接使用
import requests
from urllib.parse import urlparse

def upload_file(pre_signed_url, file_path):
    try:
        # 设置请求头
        headers = {
            "X-bailian-extra": "请替换为您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param.Headers中X-bailian-extra字段的值",
            "Content-Type": "请替换为您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param.Headers中Content-Type字段的值"
        }

        # 读取文档并上传
        with open(file_path, 'rb') as file:
            # 下方设置请求方法用于文档上传，需与您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param中Method字段的值一致
            response = requests.put(pre_signed_url, data=file, headers=headers)

        # 检查响应状态码
        if response.status_code == 200:
            print("File uploaded successfully.")
        else:
            print(f"Failed to upload the file. ResponseCode: {response.status_code}")

    except Exception as e:
        print(f"An error occurred: {str(e)}")

def upload_file_link(pre_signed_url, source_url_string):
    try:
        # 设置请求头
        headers = {
            "X-bailian-extra": "请替换为您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param.Headers中X-bailian-extra字段的值",
            "Content-Type": "请替换为您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param.Headers中Content-Type字段的值"
        }

        # 设置访问OSS的请求方法为GET
        source_response = requests.get(source_url_string)
        if source_response.status_code != 200:
            raise RuntimeError("Failed to get source file.")

        # 下方设置请求方法用于文档上传，需与您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param中Method字段的值一致
        response = requests.put(pre_signed_url, data=source_response.content, headers=headers)

        # 检查响应状态码
        if response.status_code == 200:
            print("File uploaded successfully.")
        else:
            print(f"Failed to upload the file. ResponseCode: {response.status_code}")

    except Exception as e:
        print(f"An error occurred: {str(e)}")

if __name__ == "__main__":

    pre_signed_url_or_http_url = "请替换为您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param中Url字段的值"

    # 文档来源可以是本地，上传本地文档至百炼临时存储
    file_path = "请替换为您需要上传文档的实际本地路径"
    upload_file(pre_signed_url_or_http_url, file_path)

    # 文档来源还可以是阿里云对象存储OSS
    # file_path = "请替换为您需要上传文档的实际阿里云对象存储OSS可公网访问地址"
    # upload_file_link(pre_signed_url_or_http_url, file_path)
将文件添加至阿里云百炼的数据管理

上一步操作成功后，文档将暂存于阿里云百炼的临时存储空间内 12 小时，您可以在线调试AddFile接口获取会话文件ID。
AddFile 接口
请求语法
PUT /{WorkspaceId}/datacenter/file HTTP/1.1
路径参数
字段名称	字段详情
WorkspaceId
string
业务空间 ID，即文档将上传至该业务空间中。获取方式请参见如何使用业务空间。

示例值:
llm-3shx2gu255oqxxxx
请求参数
字段名称	字段详情
LeaseId
string
上传租约 ID，对应 ApplyFileUploadLease 接口返回的 FileUploadLeaseId。

示例值:
68abd1dea7b6404d8f7d7b9f7fbd332d.17166xxxxxxxx
参考取值来源:
ApplyFileUploadLease
Parser
string
解析器类型。取值范围：展开详情

示例值:
DASHSCOPE_DOCMIND
CategoryId
string
当 CategoryType 为 UNSTRUCTURED 时，需传入上传文档所属类目 ID，即 AddCategory 接口返回的CategoryId。您也可以前往应用数据-非结构化数据页签，单击类目名称旁的 ID 图标获取类目 ID。此处允许传入 default，即使用系统创建的“默认类目”。展开详情
示例值:
cate_cdd11b1b79a74e8bbd675c356a91ee35xxxxxxxx
Tags
array<string>
文档关联的标签列表。默认值为空，即文档不关联任何标签。最多传入 10 个标签。

CategoryType
string
类目类型，可选，默认值为 UNSTRUCTURED，取值范围：展开详情

示例值:
UNSTRUCTURED
枚举值:
UNSTRUCTURED
SESSION_FILE
OriginalFileUrl
string
说明 该参数暂不开放，请勿传入。展开详情
返回参数
字段名称	字段详情
Code
string
错误状态码。

示例值:
DataCenter.FileTooLarge
Data
object
接口业务数据字段。

Message
string
错误信息。

示例值:
User not authorized to operate on the specified resource.
RequestId
string
请求 ID。

示例值:
778C0B3B-xxxx-5FC1-A947-36EDD13606AB
Status
string
接口返回的状态码。

示例值:
200
Success
string
接口调用是否成功，可能值为：展开详情

示例值:
true