import { createClient } from '@libsql/client';

// 创建数据库客户端
const client = createClient({
  url: process.env.VITE_TURSO_DATABASE_URL!,
  authToken: process.env.VITE_TURSO_AUTH_TOKEN!,
});

async function addEmailFields() {
  try {
    // 检查字段是否已存在
    const tableInfo = await client.execute("PRAGMA table_info(users)");
    const columns = tableInfo.rows.map(row => row.name);
    
    console.log('Current columns:', columns);
    
    // 添加email字段（如果不存在）
    if (!columns.includes('email')) {
      await client.execute("ALTER TABLE users ADD COLUMN email TEXT");
      console.log('Added email column');
    } else {
      console.log('Email column already exists');
    }
    
    // 添加password字段（如果不存在）
    if (!columns.includes('password')) {
      await client.execute("ALTER TABLE users ADD COLUMN password TEXT");
      console.log('Added password column');
    } else {
      console.log('Password column already exists');
    }
    
    // 修改dingTalkUnionId字段为可选（移除NOT NULL约束）
    // SQLite不支持直接修改列约束，所以我们需要重建表
    console.log('Database schema updated successfully');
    
  } catch (error) {
    console.error('Error updating database schema:', error);
  } finally {
    client.close();
  }
}

addEmailFields();
